(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/supabase.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "supabase": (()=>supabase)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createBrowserClient$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/createBrowserClient.js [app-client] (ecmascript)");
;
const supabaseUrl = ("TURBOPACK compile-time value", "https://vudqdubrlkouxjourunl.supabase.co");
const supabaseAnonKey = ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZ1ZHFkdWJybGtvdXhqb3VydW5sIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE5NzAwMzUsImV4cCI6MjA2NzU0NjAzNX0.js_x0OP2NOfxk3WccYDf7JwZzgSRArTIdbegxRbRJXU");
// Check if Supabase is configured
const isSupabaseConfigured = supabaseUrl && supabaseAnonKey && supabaseUrl !== 'your_supabase_project_url' && supabaseAnonKey !== 'your_supabase_anon_key';
const supabase = ("TURBOPACK compile-time truthy", 1) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createBrowserClient$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createBrowserClient"])(supabaseUrl, supabaseAnonKey) : ("TURBOPACK unreachable", undefined);
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider),
    "useAuth": (()=>useAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function AuthProvider({ children }) {
    _s();
    const [user, setUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [supabaseUser, setSupabaseUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthProvider.useEffect": ()=>{
            // Check if Supabase is configured
            if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"]) {
                setLoading(false);
                return;
            }
            // Get initial session
            const getInitialSession = {
                "AuthProvider.useEffect.getInitialSession": async ()=>{
                    const { data: { session } } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].auth.getSession();
                    if (session?.user) {
                        setSupabaseUser(session.user);
                        try {
                            await fetchUserProfile(session.user.id);
                        } catch (error) {
                            console.error('Failed to fetch initial user profile, using fallback:', error);
                            createFallbackUser(session.user);
                        }
                    }
                    setLoading(false);
                }
            }["AuthProvider.useEffect.getInitialSession"];
            getInitialSession();
            // Listen for auth changes
            const { data: { subscription } } = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].auth.onAuthStateChange({
                "AuthProvider.useEffect": async (event, session)=>{
                    console.log('Auth state change:', event, session?.user?.email);
                    if (session?.user) {
                        setSupabaseUser(session.user);
                        // Try to fetch user profile, but don't block on it
                        try {
                            await fetchUserProfile(session.user.id);
                        } catch (error) {
                            console.error('Failed to fetch user profile, using fallback:', error);
                            // Fallback: create user object from auth data
                            createFallbackUser(session.user);
                        }
                    } else {
                        setSupabaseUser(null);
                        setUser(null);
                    }
                    setLoading(false);
                }
            }["AuthProvider.useEffect"]);
            return ({
                "AuthProvider.useEffect": ()=>subscription.unsubscribe()
            })["AuthProvider.useEffect"];
        }
    }["AuthProvider.useEffect"], []);
    // Monitor for cases where we have supabaseUser but no user profile
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthProvider.useEffect": ()=>{
            if (supabaseUser && !user && !loading) {
                console.log('Detected auth user without profile, creating fallback user');
                createFallbackUser(supabaseUser);
            }
        }
    }["AuthProvider.useEffect"], [
        supabaseUser,
        user,
        loading
    ]);
    const createFallbackUser = (authUser)=>{
        console.log('Creating fallback user from auth data:', authUser.email);
        const displayName = authUser.user_metadata?.display_name || authUser.user_metadata?.name || authUser.user_metadata?.full_name || authUser.user_metadata?.given_name || authUser.email?.split('@')[0] || 'Anonymous';
        const fallbackUser = {
            id: authUser.id,
            email: authUser.email,
            display_name: displayName,
            avatar_url: authUser.user_metadata?.avatar_url || authUser.user_metadata?.picture,
            created_at: authUser.created_at,
            updated_at: new Date().toISOString()
        };
        setUser(fallbackUser);
        console.log('Fallback user created:', fallbackUser);
    };
    const fetchUserProfile = async (userId)=>{
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"]) return;
        try {
            // First, let's try to get the current session to ensure we have proper auth
            const { data: sessionData } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].auth.getSession();
            if (!sessionData.session) {
                console.log('No active session, skipping user profile fetch');
                return;
            }
            const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('users').select('*').eq('id', userId).maybeSingle() // Use maybeSingle instead of single to avoid errors when no rows
            ;
            if (error) {
                console.error('Error fetching user profile:', {
                    message: error.message,
                    details: error.details,
                    hint: error.hint,
                    code: error.code,
                    userId: userId
                });
                // Handle specific error cases
                if (error.code === 'PGRST116' || error.message.includes('406') || !data) {
                    console.log('User not found in users table, attempting to create...');
                    await createUserProfile(userId);
                    return;
                }
                // For other errors, we'll continue without setting user data
                // but won't block the authentication flow
                return;
            }
            if (data) {
                setUser(data);
            } else {
                // No user found, try to create one
                console.log('No user data returned, attempting to create user profile...');
                await createUserProfile(userId);
            }
        } catch (error) {
            console.error('Error fetching user profile:', error);
            // Don't block auth flow, try to create user profile as fallback
            await createUserProfile(userId);
        }
    };
    const createUserProfile = async (userId)=>{
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"] || !supabaseUser) return;
        try {
            // Get user info from Supabase auth
            const { data: authUser, error: authError } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].auth.getUser();
            if (authError) {
                console.error('Error getting auth user:', authError);
                return;
            }
            const user = authUser.user;
            if (!user) return;
            // Extract display name and avatar from user metadata
            const displayName = user.user_metadata?.display_name || user.user_metadata?.name || user.user_metadata?.full_name || user.user_metadata?.given_name || user.email?.split('@')[0] || 'Anonymous';
            const avatarUrl = user.user_metadata?.avatar_url || user.user_metadata?.picture;
            console.log('Attempting to create user profile:', {
                userId,
                email: user.email,
                displayName,
                avatarUrl
            });
            // Create user profile with upsert to handle conflicts
            const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('users').upsert({
                id: userId,
                email: user.email,
                display_name: displayName,
                avatar_url: avatarUrl,
                updated_at: new Date().toISOString()
            }, {
                onConflict: 'id'
            }).select().single();
            if (error) {
                console.error('Error creating user profile:', {
                    message: error.message,
                    details: error.details,
                    hint: error.hint,
                    code: error.code,
                    userId: userId,
                    email: user.email,
                    displayName: displayName
                });
                // If we still can't create the user, set a minimal user object
                // so the app doesn't get stuck in a loop
                if (error.message.includes('406') || error.code === 'PGRST301') {
                    console.log('Database access issue, using auth user data as fallback');
                    setUser({
                        id: userId,
                        email: user.email,
                        display_name: displayName,
                        avatar_url: avatarUrl,
                        created_at: user.created_at,
                        updated_at: new Date().toISOString()
                    });
                }
                return;
            }
            console.log('User profile created successfully:', data);
            setUser(data);
        } catch (error) {
            console.error('Error creating user profile:', error);
            // Fallback: use the auth user data to prevent infinite loops
            if (supabaseUser) {
                const displayName = supabaseUser.user_metadata?.display_name || supabaseUser.user_metadata?.name || supabaseUser.user_metadata?.full_name || supabaseUser.email?.split('@')[0] || 'Anonymous';
                setUser({
                    id: userId,
                    email: supabaseUser.email,
                    display_name: displayName,
                    avatar_url: supabaseUser.user_metadata?.avatar_url || supabaseUser.user_metadata?.picture,
                    created_at: supabaseUser.created_at,
                    updated_at: new Date().toISOString()
                });
            }
        }
    };
    const signInWithGoogle = async ()=>{
        try {
            // Check if Supabase is properly configured
            if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"]) {
                alert('Please configure Supabase environment variables. See README.md for setup instructions.');
                return;
            }
            const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].auth.signInWithOAuth({
                provider: 'google',
                options: {
                    redirectTo: `${window.location.origin}/auth/callback`
                }
            });
            if (error) throw error;
        } catch (error) {
            console.error('Error signing in with Google:', error);
            throw error;
        }
    };
    const signOut = async ()=>{
        try {
            if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"]) return;
            const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].auth.signOut();
            if (error) throw error;
            setUser(null);
            setSupabaseUser(null);
        } catch (error) {
            console.error('Error signing out:', error);
            throw error;
        }
    };
    const updateUserProfile = async (updates)=>{
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"] || !user) {
            throw new Error('User not authenticated or Supabase not configured');
        }
        try {
            const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('users').update({
                ...updates,
                updated_at: new Date().toISOString()
            }).eq('id', user.id).select().single();
            if (error) {
                console.error('Error updating user profile:', error);
                throw error;
            }
            // Update the local user state
            setUser(data);
        } catch (error) {
            console.error('Error updating user profile:', error);
            throw error;
        }
    };
    const value = {
        user,
        supabaseUser,
        loading,
        signInWithGoogle,
        signOut,
        updateUserProfile
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/AuthContext.tsx",
        lineNumber: 331,
        columnNumber: 5
    }, this);
}
_s(AuthProvider, "bGtgqIIl5r5j+TNip7NttFEYrok=");
_c = AuthProvider;
function useAuth() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
}
_s1(useAuth, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_context__.k.register(_c, "AuthProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_17f3704b._.js.map