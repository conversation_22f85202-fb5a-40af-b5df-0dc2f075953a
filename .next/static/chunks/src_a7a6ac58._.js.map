{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/components/auth/LoginButton.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { LogIn, LogOut, User } from 'lucide-react'\n\nexport default function LoginButton() {\n  const { user, signInWithGoogle, signOut, loading } = useAuth()\n  const [isLoading, setIsLoading] = useState(false)\n\n  const handleSignIn = async () => {\n    setIsLoading(true)\n    try {\n      await signInWithGoogle()\n    } catch (error) {\n      console.error('Sign in error:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleSignOut = async () => {\n    setIsLoading(true)\n    try {\n      await signOut()\n    } catch (error) {\n      console.error('Sign out error:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"animate-pulse bg-gray-200 rounded-md h-10 w-24\"></div>\n    )\n  }\n\n  if (user) {\n    return null // Account button will be handled by AccountButton component\n  }\n\n  return (\n    <button\n      onClick={handleSignIn}\n      disabled={isLoading}\n      className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n    >\n      {isLoading ? (\n        <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n      ) : (\n        <>\n          <LogIn className=\"w-4 h-4 mr-2\" />\n          Sign in with Google\n        </>\n      )}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,eAAe;QACnB,aAAa;QACb,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;QAClC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB;QACpB,aAAa;QACb,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;QACnC,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;;;;;IAEnB;IAEA,IAAI,MAAM;QACR,OAAO,KAAK,4DAA4D;;IAC1E;IAEA,qBACE,6LAAC;QACC,SAAS;QACT,UAAU;QACV,WAAU;kBAET,0BACC,6LAAC;YAAI,WAAU;;;;;iCAEf;;8BACE,6LAAC,2MAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;gBAAiB;;;;;;;;AAM5C;GApDwB;;QAC+B,kIAAA,CAAA,UAAO;;;KADtC", "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/components/auth/AccountButton.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useRouter } from 'next/navigation'\nimport { User, Settings, LogOut, ChevronDown } from 'lucide-react'\n\nexport default function AccountButton() {\n  const { user, signOut, loading } = useAuth()\n  const [isDropdownOpen, setIsDropdownOpen] = useState(false)\n  const [isLoading, setIsLoading] = useState(false)\n  const dropdownRef = useRef<HTMLDivElement>(null)\n  const router = useRouter()\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    function handleClickOutside(event: MouseEvent) {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsDropdownOpen(false)\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside)\n    }\n  }, [])\n\n  const handleSignOut = async () => {\n    setIsLoading(true)\n    try {\n      await signOut()\n      setIsDropdownOpen(false)\n      router.push('/')\n    } catch (error) {\n      console.error('Sign out error:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleProfileClick = () => {\n    setIsDropdownOpen(false)\n    router.push('/profile')\n  }\n\n  const getInitials = (name: string) => {\n    return name\n      .split(' ')\n      .map(word => word.charAt(0))\n      .join('')\n      .toUpperCase()\n      .slice(0, 2)\n  }\n\n  if (loading || !user) {\n    return null\n  }\n\n  return (\n    <div className=\"relative\" ref={dropdownRef}>\n      <button\n        onClick={() => setIsDropdownOpen(!isDropdownOpen)}\n        className=\"flex items-center space-x-2 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors\"\n      >\n        {user.avatar_url ? (\n          <img\n            src={user.avatar_url}\n            alt={user.display_name}\n            className=\"w-8 h-8 rounded-full object-cover\"\n          />\n        ) : (\n          <div className=\"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium\">\n            {getInitials(user.display_name)}\n          </div>\n        )}\n        <span className=\"hidden sm:block\">{user.display_name}</span>\n        <ChevronDown className={`w-4 h-4 transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`} />\n      </button>\n\n      {isDropdownOpen && (\n        <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50\">\n          <div className=\"py-1\">\n            <div className=\"px-4 py-2 text-sm text-gray-500 border-b\">\n              <div className=\"font-medium text-gray-900\">{user.display_name}</div>\n              <div className=\"text-xs\">{user.email}</div>\n            </div>\n            \n            <button\n              onClick={handleProfileClick}\n              className=\"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\"\n            >\n              <User className=\"w-4 h-4 mr-3\" />\n              Profile\n            </button>\n            \n            <button\n              onClick={handleProfileClick}\n              className=\"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\"\n            >\n              <Settings className=\"w-4 h-4 mr-3\" />\n              Account Settings\n            </button>\n            \n            <div className=\"border-t\">\n              <button\n                onClick={handleSignOut}\n                disabled={isLoading}\n                className=\"flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors disabled:opacity-50\"\n              >\n                {isLoading ? (\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-3\"></div>\n                ) : (\n                  <LogOut className=\"w-4 h-4 mr-3\" />\n                )}\n                {isLoading ? 'Signing out...' : 'Sign Out'}\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACzC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,SAAS,mBAAmB,KAAiB;gBAC3C,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;oBAC9E,kBAAkB;gBACpB;YACF;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;2CAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;kCAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,aAAa;QACb,IAAI;YACF,MAAM;YACN,kBAAkB;YAClB,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;QACnC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,qBAAqB;QACzB,kBAAkB;QAClB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,IACxB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,IAAI,WAAW,CAAC,MAAM;QACpB,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;QAAW,KAAK;;0BAC7B,6LAAC;gBACC,SAAS,IAAM,kBAAkB,CAAC;gBAClC,WAAU;;oBAET,KAAK,UAAU,iBACd,6LAAC;wBACC,KAAK,KAAK,UAAU;wBACpB,KAAK,KAAK,YAAY;wBACtB,WAAU;;;;;6CAGZ,6LAAC;wBAAI,WAAU;kCACZ,YAAY,KAAK,YAAY;;;;;;kCAGlC,6LAAC;wBAAK,WAAU;kCAAmB,KAAK,YAAY;;;;;;kCACpD,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAW,CAAC,6BAA6B,EAAE,iBAAiB,eAAe,IAAI;;;;;;;;;;;;YAG7F,gCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAA6B,KAAK,YAAY;;;;;;8CAC7D,6LAAC;oCAAI,WAAU;8CAAW,KAAK,KAAK;;;;;;;;;;;;sCAGtC,6LAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAInC,6LAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAIvC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;;oCAET,0BACC,6LAAC;wCAAI,WAAU;;;;;6DAEf,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAEnB,YAAY,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhD;GApHwB;;QACa,kIAAA,CAAA,UAAO;QAI3B,qIAAA,CAAA,YAAS;;;KALF", "debugId": null}}, {"offset": {"line": 344, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/components/layout/Navigation.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { useAuth } from '@/contexts/AuthContext'\nimport LoginButton from '@/components/auth/LoginButton'\nimport AccountButton from '@/components/auth/AccountButton'\nimport { Crown, Home, User, Gamepad2 } from 'lucide-react'\n\nexport default function Navigation() {\n  const { user } = useAuth()\n\n  return (\n    <nav className=\"bg-white shadow-lg\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <Crown className=\"h-8 w-8 text-blue-600\" />\n              <span className=\"text-xl font-bold text-gray-900\">ChessHub</span>\n            </Link>\n          </div>\n\n          <div className=\"flex items-center space-x-8\">\n            <Link\n              href=\"/\"\n              className=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium\"\n            >\n              <Home className=\"w-4 h-4\" />\n              <span>Home</span>\n            </Link>\n\n            <Link\n              href=\"/demo\"\n              className=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium\"\n            >\n              <Gamepad2 className=\"w-4 h-4\" />\n              <span>Demo</span>\n            </Link>\n\n            {user && (\n              <>\n                <Link\n                  href=\"/play\"\n                  className=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  <Gamepad2 className=\"w-4 h-4\" />\n                  <span>Play</span>\n                </Link>\n                <Link\n                  href=\"/profile\"\n                  className=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  <User className=\"w-4 h-4\" />\n                  <span>Profile</span>\n                </Link>\n              </>\n            )}\n\n            <LoginButton />\n            <AccountButton />\n          </div>\n        </div>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEvB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,6LAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;;;;;;kCAItD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAA<PERSON>,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC,sMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;kDAAK;;;;;;;;;;;;0CAGR,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;kDAAK;;;;;;;;;;;;4BAGP,sBACC;;kDACE,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;0DAAK;;;;;;;;;;;;;;0CAKZ,6LAAC,4IAAA,CAAA,UAAW;;;;;0CACZ,6LAAC,8IAAA,CAAA,UAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM1B;GAzDwB;;QACL,kIAAA,CAAA,UAAO;;;KADF", "debugId": null}}, {"offset": {"line": 559, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/components/AuthDebug.tsx"], "sourcesContent": ["'use client'\n\nimport { useAuth } from '@/contexts/AuthContext'\nimport { supabase } from '@/lib/supabase'\nimport { useState, useEffect } from 'react'\n\nexport default function AuthDebug() {\n  const { user, supabaseUser, loading } = useAuth()\n  const [debugInfo, setDebugInfo] = useState<any>(null)\n  const [isDebugging, setIsDebugging] = useState(false)\n  const [authLogs, setAuthLogs] = useState<string[]>([])\n\n  // Capture console logs related to authentication\n  useEffect(() => {\n    const originalConsoleError = console.error\n    const originalConsoleLog = console.log\n\n    console.error = (...args) => {\n      const message = args.join(' ')\n      if (message.includes('auth') || message.includes('user') || message.includes('profile') || message.includes('Error')) {\n        setAuthLogs(prev => [...prev.slice(-9), `ERROR: ${message}`])\n      }\n      originalConsoleError(...args)\n    }\n\n    console.log = (...args) => {\n      const message = args.join(' ')\n      if (message.includes('auth') || message.includes('user') || message.includes('profile') || message.includes('User')) {\n        setAuthLogs(prev => [...prev.slice(-9), `LOG: ${message}`])\n      }\n      originalConsoleLog(...args)\n    }\n\n    return () => {\n      console.error = originalConsoleError\n      console.log = originalConsoleLog\n    }\n  }, [])\n\n  const runDebugCheck = async () => {\n    if (!supabase) {\n      setDebugInfo({ error: 'Supabase not configured' })\n      return\n    }\n\n    setIsDebugging(true)\n    const info: any = {}\n\n    try {\n      // Check current session\n      const { data: sessionData, error: sessionError } = await supabase.auth.getSession()\n      info.session = {\n        exists: !!sessionData.session,\n        user: sessionData.session?.user ? {\n          id: sessionData.session.user.id,\n          email: sessionData.session.user.email,\n          metadata: sessionData.session.user.user_metadata\n        } : null,\n        error: sessionError\n      }\n\n      // Check user profile in database\n      if (sessionData.session?.user) {\n        const { data: userProfile, error: profileError } = await supabase\n          .from('users')\n          .select('*')\n          .eq('id', sessionData.session.user.id)\n          .single()\n\n        info.userProfile = {\n          exists: !!userProfile,\n          data: userProfile,\n          error: profileError ? {\n            message: profileError.message,\n            code: profileError.code,\n            details: profileError.details,\n            hint: profileError.hint\n          } : null\n        }\n\n        // Try to manually create user if missing\n        if (profileError?.code === 'PGRST116') {\n          const user = sessionData.session.user\n          const displayName = user.user_metadata?.display_name || \n                             user.user_metadata?.name || \n                             user.user_metadata?.full_name ||\n                             user.user_metadata?.given_name ||\n                             user.email?.split('@')[0] || \n                             'Anonymous'\n\n          const avatarUrl = user.user_metadata?.avatar_url || \n                           user.user_metadata?.picture\n\n          const { data: createdUser, error: createError } = await supabase\n            .from('users')\n            .insert({\n              id: user.id,\n              email: user.email!,\n              display_name: displayName,\n              avatar_url: avatarUrl\n            })\n            .select()\n            .single()\n\n          info.userCreation = {\n            attempted: true,\n            success: !!createdUser,\n            data: createdUser,\n            error: createError ? {\n              message: createError.message,\n              code: createError.code,\n              details: createError.details,\n              hint: createError.hint\n            } : null\n          }\n        }\n      }\n\n      // Check all users in database\n      const { data: allUsers, error: allUsersError } = await supabase\n        .from('users')\n        .select('id, email, display_name, created_at')\n        .limit(10)\n\n      info.allUsers = {\n        count: allUsers?.length || 0,\n        data: allUsers,\n        error: allUsersError\n      }\n\n    } catch (error) {\n      info.error = error\n    }\n\n    setDebugInfo(info)\n    setIsDebugging(false)\n  }\n\n  if (!supabase) {\n    return (\n      <div className=\"p-4 bg-red-50 border border-red-200 rounded-lg\">\n        <h3 className=\"font-bold text-red-800\">Supabase Not Configured</h3>\n        <p className=\"text-red-600\">Please check your environment variables.</p>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"p-4 bg-gray-50 border border-gray-200 rounded-lg\">\n      <h3 className=\"font-bold text-gray-800 mb-4\">Authentication Debug Info</h3>\n\n      <div className=\"space-y-4\">\n        <div>\n          <h4 className=\"font-semibold\">Auth Context State:</h4>\n          <pre className=\"text-sm bg-white p-2 rounded border overflow-auto\">\n            {JSON.stringify({\n              loading,\n              hasUser: !!user,\n              hasSupabaseUser: !!supabaseUser,\n              userEmail: user?.email || supabaseUser?.email,\n              userId: user?.id || supabaseUser?.id,\n              userDisplayName: user?.display_name,\n              userAvatarUrl: user?.avatar_url,\n              supabaseUserMetadata: supabaseUser?.user_metadata\n            }, null, 2)}\n          </pre>\n        </div>\n\n        {authLogs.length > 0 && (\n          <div>\n            <h4 className=\"font-semibold\">Recent Auth Logs:</h4>\n            <div className=\"text-sm bg-white p-2 rounded border max-h-32 overflow-auto\">\n              {authLogs.map((log, index) => (\n                <div key={index} className={`mb-1 ${log.startsWith('ERROR:') ? 'text-red-600' : 'text-gray-600'}`}>\n                  {log}\n                </div>\n              ))}\n            </div>\n            <button\n              onClick={() => setAuthLogs([])}\n              className=\"mt-2 px-2 py-1 text-xs bg-gray-200 text-gray-700 rounded hover:bg-gray-300\"\n            >\n              Clear Logs\n            </button>\n          </div>\n        )}\n\n        <div className=\"flex space-x-2\">\n          <button\n            onClick={runDebugCheck}\n            disabled={isDebugging}\n            className=\"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50\"\n          >\n            {isDebugging ? 'Running Debug Check...' : 'Run Debug Check'}\n          </button>\n\n          <button\n            onClick={() => window.location.reload()}\n            className=\"px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600\"\n          >\n            Refresh Page\n          </button>\n        </div>\n\n        {debugInfo && (\n          <div>\n            <h4 className=\"font-semibold\">Debug Results:</h4>\n            <pre className=\"text-sm bg-white p-2 rounded border overflow-auto max-h-96\">\n              {JSON.stringify(debugInfo, null, 2)}\n            </pre>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAChD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAErD,iDAAiD;IACjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM,uBAAuB,QAAQ,KAAK;YAC1C,MAAM,qBAAqB,QAAQ,GAAG;YAEtC,QAAQ,KAAK;uCAAG,CAAC,GAAG;oBAClB,MAAM,UAAU,KAAK,IAAI,CAAC;oBAC1B,IAAI,QAAQ,QAAQ,CAAC,WAAW,QAAQ,QAAQ,CAAC,WAAW,QAAQ,QAAQ,CAAC,cAAc,QAAQ,QAAQ,CAAC,UAAU;wBACpH;mDAAY,CAAA,OAAQ;uCAAI,KAAK,KAAK,CAAC,CAAC;oCAAI,CAAC,OAAO,EAAE,SAAS;iCAAC;;oBAC9D;oBACA,wBAAwB;gBAC1B;;YAEA,QAAQ,GAAG;uCAAG,CAAC,GAAG;oBAChB,MAAM,UAAU,KAAK,IAAI,CAAC;oBAC1B,IAAI,QAAQ,QAAQ,CAAC,WAAW,QAAQ,QAAQ,CAAC,WAAW,QAAQ,QAAQ,CAAC,cAAc,QAAQ,QAAQ,CAAC,SAAS;wBACnH;mDAAY,CAAA,OAAQ;uCAAI,KAAK,KAAK,CAAC,CAAC;oCAAI,CAAC,KAAK,EAAE,SAAS;iCAAC;;oBAC5D;oBACA,sBAAsB;gBACxB;;YAEA;uCAAO;oBACL,QAAQ,KAAK,GAAG;oBAChB,QAAQ,GAAG,GAAG;gBAChB;;QACF;8BAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,IAAI,CAAC,yHAAA,CAAA,WAAQ,EAAE;YACb,aAAa;gBAAE,OAAO;YAA0B;YAChD;QACF;QAEA,eAAe;QACf,MAAM,OAAY,CAAC;QAEnB,IAAI;YACF,wBAAwB;YACxB,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;YACjF,KAAK,OAAO,GAAG;gBACb,QAAQ,CAAC,CAAC,YAAY,OAAO;gBAC7B,MAAM,YAAY,OAAO,EAAE,OAAO;oBAChC,IAAI,YAAY,OAAO,CAAC,IAAI,CAAC,EAAE;oBAC/B,OAAO,YAAY,OAAO,CAAC,IAAI,CAAC,KAAK;oBACrC,UAAU,YAAY,OAAO,CAAC,IAAI,CAAC,aAAa;gBAClD,IAAI;gBACJ,OAAO;YACT;YAEA,iCAAiC;YACjC,IAAI,YAAY,OAAO,EAAE,MAAM;gBAC7B,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC9D,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,YAAY,OAAO,CAAC,IAAI,CAAC,EAAE,EACpC,MAAM;gBAET,KAAK,WAAW,GAAG;oBACjB,QAAQ,CAAC,CAAC;oBACV,MAAM;oBACN,OAAO,eAAe;wBACpB,SAAS,aAAa,OAAO;wBAC7B,MAAM,aAAa,IAAI;wBACvB,SAAS,aAAa,OAAO;wBAC7B,MAAM,aAAa,IAAI;oBACzB,IAAI;gBACN;gBAEA,yCAAyC;gBACzC,IAAI,cAAc,SAAS,YAAY;oBACrC,MAAM,OAAO,YAAY,OAAO,CAAC,IAAI;oBACrC,MAAM,cAAc,KAAK,aAAa,EAAE,gBACrB,KAAK,aAAa,EAAE,QACpB,KAAK,aAAa,EAAE,aACpB,KAAK,aAAa,EAAE,cACpB,KAAK,KAAK,EAAE,MAAM,IAAI,CAAC,EAAE,IACzB;oBAEnB,MAAM,YAAY,KAAK,aAAa,EAAE,cACrB,KAAK,aAAa,EAAE;oBAErC,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7D,IAAI,CAAC,SACL,MAAM,CAAC;wBACN,IAAI,KAAK,EAAE;wBACX,OAAO,KAAK,KAAK;wBACjB,cAAc;wBACd,YAAY;oBACd,GACC,MAAM,GACN,MAAM;oBAET,KAAK,YAAY,GAAG;wBAClB,WAAW;wBACX,SAAS,CAAC,CAAC;wBACX,MAAM;wBACN,OAAO,cAAc;4BACnB,SAAS,YAAY,OAAO;4BAC5B,MAAM,YAAY,IAAI;4BACtB,SAAS,YAAY,OAAO;4BAC5B,MAAM,YAAY,IAAI;wBACxB,IAAI;oBACN;gBACF;YACF;YAEA,8BAA8B;YAC9B,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC5D,IAAI,CAAC,SACL,MAAM,CAAC,uCACP,KAAK,CAAC;YAET,KAAK,QAAQ,GAAG;gBACd,OAAO,UAAU,UAAU;gBAC3B,MAAM;gBACN,OAAO;YACT;QAEF,EAAE,OAAO,OAAO;YACd,KAAK,KAAK,GAAG;QACf;QAEA,aAAa;QACb,eAAe;IACjB;IAEA,IAAI,CAAC,yHAAA,CAAA,WAAQ,EAAE;QACb,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAyB;;;;;;8BACvC,6LAAC;oBAAE,WAAU;8BAAe;;;;;;;;;;;;IAGlC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA+B;;;;;;0BAE7C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAgB;;;;;;0CAC9B,6LAAC;gCAAI,WAAU;0CACZ,KAAK,SAAS,CAAC;oCACd;oCACA,SAAS,CAAC,CAAC;oCACX,iBAAiB,CAAC,CAAC;oCACnB,WAAW,MAAM,SAAS,cAAc;oCACxC,QAAQ,MAAM,MAAM,cAAc;oCAClC,iBAAiB,MAAM;oCACvB,eAAe,MAAM;oCACrB,sBAAsB,cAAc;gCACtC,GAAG,MAAM;;;;;;;;;;;;oBAIZ,SAAS,MAAM,GAAG,mBACjB,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAgB;;;;;;0CAC9B,6LAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,KAAK,sBAClB,6LAAC;wCAAgB,WAAW,CAAC,KAAK,EAAE,IAAI,UAAU,CAAC,YAAY,iBAAiB,iBAAiB;kDAC9F;uCADO;;;;;;;;;;0CAKd,6LAAC;gCACC,SAAS,IAAM,YAAY,EAAE;gCAC7B,WAAU;0CACX;;;;;;;;;;;;kCAML,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,cAAc,2BAA2B;;;;;;0CAG5C,6LAAC;gCACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;gCACrC,WAAU;0CACX;;;;;;;;;;;;oBAKF,2BACC,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAgB;;;;;;0CAC9B,6LAAC;gCAAI,WAAU;0CACZ,KAAK,SAAS,CAAC,WAAW,MAAM;;;;;;;;;;;;;;;;;;;;;;;;AAO/C;GAjNwB;;QACkB,kIAAA,CAAA,UAAO;;;KADzB", "debugId": null}}, {"offset": {"line": 886, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { useAuth } from '@/contexts/AuthContext'\nimport Navigation from '@/components/layout/Navigation'\nimport LoginButton from '@/components/auth/LoginButton'\nimport AuthDebug from '@/components/AuthDebug'\nimport { Crown, Users, Trophy, Zap, AlertCircle, X } from 'lucide-react'\nimport { useEffect, useState } from 'react'\n\nexport default function Home() {\n  const { user } = useAuth()\n  const [error, setError] = useState<string | null>(null)\n  const [errorDescription, setErrorDescription] = useState<string | null>(null)\n\n  useEffect(() => {\n    // Check for error parameters in URL\n    const urlParams = new URLSearchParams(window.location.search)\n    const errorParam = urlParams.get('error')\n    const errorDescParam = urlParams.get('error_description')\n\n    if (errorParam) {\n      setError(errorParam)\n      setErrorDescription(errorDescParam)\n\n      // Clean up URL\n      const newUrl = window.location.pathname\n      window.history.replaceState({}, document.title, newUrl)\n    }\n  }, [])\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navigation />\n\n      {/* Error Alert */}\n      {error && (\n        <div className=\"bg-red-50 border border-red-200 rounded-md p-4 mx-4 mt-4\">\n          <div className=\"flex\">\n            <div className=\"flex-shrink-0\">\n              <AlertCircle className=\"h-5 w-5 text-red-400\" />\n            </div>\n            <div className=\"ml-3 flex-1\">\n              <h3 className=\"text-sm font-medium text-red-800\">\n                Authentication Error\n              </h3>\n              <div className=\"mt-2 text-sm text-red-700\">\n                <p>\n                  {error === 'server_error' && 'Database error saving new user. Please try again.'}\n                  {error === 'auth_error' && 'Authentication failed. Please try again.'}\n                  {error === 'pkce_error' && 'Authentication configuration error. Please contact support.'}\n                  {error === 'unexpected_error' && 'An unexpected error occurred. Please try again.'}\n                  {!['server_error', 'auth_error', 'pkce_error', 'unexpected_error'].includes(error) && `Error: ${error}`}\n                </p>\n                {errorDescription && (\n                  <p className=\"mt-1 text-xs text-red-600\">\n                    Details: {decodeURIComponent(errorDescription)}\n                  </p>\n                )}\n              </div>\n            </div>\n            <div className=\"ml-auto pl-3\">\n              <div className=\"-mx-1.5 -my-1.5\">\n                <button\n                  onClick={() => setError(null)}\n                  className=\"inline-flex bg-red-50 rounded-md p-1.5 text-red-500 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-red-50 focus:ring-red-600\"\n                >\n                  <X className=\"h-4 w-4\" />\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Debug Section - Only show in development */}\n      {process.env.NODE_ENV === 'development' && (\n        <div className=\"mx-4 mt-4\">\n          <AuthDebug />\n        </div>\n      )}\n\n      {/* Hero Section */}\n      <div className=\"relative bg-white overflow-hidden\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"relative z-10 pb-8 bg-white sm:pb-16 md:pb-20 lg:max-w-2xl lg:w-full lg:pb-28 xl:pb-32\">\n            <main className=\"mt-10 mx-auto max-w-7xl px-4 sm:mt-12 sm:px-6 md:mt-16 lg:mt-20 lg:px-8 xl:mt-28\">\n              <div className=\"sm:text-center lg:text-left\">\n                <h1 className=\"text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl\">\n                  <span className=\"block xl:inline\">Play Chess</span>{' '}\n                  <span className=\"block text-blue-600 xl:inline\">Online</span>\n                </h1>\n                <p className=\"mt-3 text-base text-gray-500 sm:mt-5 sm:text-lg sm:max-w-xl sm:mx-auto md:mt-5 md:text-xl lg:mx-0\">\n                  Challenge friends, improve your skills, and enjoy the timeless game of chess with our modern, real-time multiplayer platform.\n                </p>\n                <div className=\"mt-5 sm:mt-8 sm:flex sm:justify-center lg:justify-start\">\n                  {user ? (\n                    <div className=\"rounded-md shadow\">\n                      <Link\n                        href=\"/play\"\n                        className=\"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 md:py-4 md:text-lg md:px-10\"\n                      >\n                        Start Playing\n                      </Link>\n                    </div>\n                  ) : (\n                    <div className=\"rounded-md shadow\">\n                      <LoginButton />\n                    </div>\n                  )}\n                </div>\n              </div>\n            </main>\n          </div>\n        </div>\n        <div className=\"lg:absolute lg:inset-y-0 lg:right-0 lg:w-1/2\">\n          <div className=\"h-56 w-full bg-gradient-to-br from-blue-400 to-blue-600 sm:h-72 md:h-96 lg:w-full lg:h-full flex items-center justify-center\">\n            <Crown className=\"h-32 w-32 text-white opacity-20\" />\n          </div>\n        </div>\n      </div>\n\n      {/* Features Section */}\n      <div className=\"py-12 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"lg:text-center\">\n            <h2 className=\"text-base text-blue-600 font-semibold tracking-wide uppercase\">Features</h2>\n            <p className=\"mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl\">\n              Everything you need to play chess\n            </p>\n          </div>\n\n          <div className=\"mt-10\">\n            <div className=\"space-y-10 md:space-y-0 md:grid md:grid-cols-2 md:gap-x-8 md:gap-y-10\">\n              <div className=\"relative\">\n                <div className=\"absolute flex items-center justify-center h-12 w-12 rounded-md bg-blue-500 text-white\">\n                  <Users className=\"h-6 w-6\" />\n                </div>\n                <p className=\"ml-16 text-lg leading-6 font-medium text-gray-900\">Real-time Multiplayer</p>\n                <p className=\"mt-2 ml-16 text-base text-gray-500\">\n                  Play with friends online in real-time with instant move synchronization and live game updates.\n                </p>\n              </div>\n\n              <div className=\"relative\">\n                <div className=\"absolute flex items-center justify-center h-12 w-12 rounded-md bg-blue-500 text-white\">\n                  <Trophy className=\"h-6 w-6\" />\n                </div>\n                <p className=\"ml-16 text-lg leading-6 font-medium text-gray-900\">Match History & Stats</p>\n                <p className=\"mt-2 ml-16 text-base text-gray-500\">\n                  Track your progress with detailed match history, statistics, and performance analytics.\n                </p>\n              </div>\n\n              <div className=\"relative\">\n                <div className=\"absolute flex items-center justify-center h-12 w-12 rounded-md bg-blue-500 text-white\">\n                  <Zap className=\"h-6 w-6\" />\n                </div>\n                <p className=\"ml-16 text-lg leading-6 font-medium text-gray-900\">Fast & Responsive</p>\n                <p className=\"mt-2 ml-16 text-base text-gray-500\">\n                  Enjoy smooth gameplay with our optimized interface that works perfectly on all devices.\n                </p>\n              </div>\n\n              <div className=\"relative\">\n                <div className=\"absolute flex items-center justify-center h-12 w-12 rounded-md bg-blue-500 text-white\">\n                  <Crown className=\"h-6 w-6\" />\n                </div>\n                <p className=\"ml-16 text-lg leading-6 font-medium text-gray-900\">Professional Experience</p>\n                <p className=\"mt-2 ml-16 text-base text-gray-500\">\n                  Clean, modern interface with all the features you expect from a professional chess platform.\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;AA4EO;;AA1EP;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AARA;;;;;;;;AAUe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAExE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,oCAAoC;YACpC,MAAM,YAAY,IAAI,gBAAgB,OAAO,QAAQ,CAAC,MAAM;YAC5D,MAAM,aAAa,UAAU,GAAG,CAAC;YACjC,MAAM,iBAAiB,UAAU,GAAG,CAAC;YAErC,IAAI,YAAY;gBACd,SAAS;gBACT,oBAAoB;gBAEpB,eAAe;gBACf,MAAM,SAAS,OAAO,QAAQ,CAAC,QAAQ;gBACvC,OAAO,OAAO,CAAC,YAAY,CAAC,CAAC,GAAG,SAAS,KAAK,EAAE;YAClD;QACF;yBAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,6IAAA,CAAA,UAAU;;;;;YAGV,uBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;sCAEzB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;8CAGjD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;gDACE,UAAU,kBAAkB;gDAC5B,UAAU,gBAAgB;gDAC1B,UAAU,gBAAgB;gDAC1B,UAAU,sBAAsB;gDAChC,CAAC;oDAAC;oDAAgB;oDAAc;oDAAc;iDAAmB,CAAC,QAAQ,CAAC,UAAU,CAAC,OAAO,EAAE,OAAO;;;;;;;wCAExG,kCACC,6LAAC;4CAAE,WAAU;;gDAA4B;gDAC7B,mBAAmB;;;;;;;;;;;;;;;;;;;sCAKrC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS,IAAM,SAAS;oCACxB,WAAU;8CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASxB,oDAAyB,+BACxB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,kIAAA,CAAA,UAAS;;;;;;;;;;0BAKd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,WAAU;0CACd,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAAkB;;;;;;gDAAkB;8DACpD,6LAAC;oDAAK,WAAU;8DAAgC;;;;;;;;;;;;sDAElD,6LAAC;4CAAE,WAAU;sDAAoG;;;;;;sDAGjH,6LAAC;4CAAI,WAAU;sDACZ,qBACC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;qEAKH,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,4IAAA,CAAA,UAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ1B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAMvB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAgE;;;;;;8CAC9E,6LAAC;oCAAE,WAAU;8CAAkF;;;;;;;;;;;;sCAKjG,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,6LAAC;gDAAE,WAAU;0DAAoD;;;;;;0DACjE,6LAAC;gDAAE,WAAU;0DAAqC;;;;;;;;;;;;kDAKpD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAEpB,6LAAC;gDAAE,WAAU;0DAAoD;;;;;;0DACjE,6LAAC;gDAAE,WAAU;0DAAqC;;;;;;;;;;;;kDAKpD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;;;;;;0DAEjB,6LAAC;gDAAE,WAAU;0DAAoD;;;;;;0DACjE,6LAAC;gDAAE,WAAU;0DAAqC;;;;;;;;;;;;kDAKpD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,6LAAC;gDAAE,WAAU;0DAAoD;;;;;;0DACjE,6LAAC;gDAAE,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUlE;GAzKwB;;QACL,kIAAA,CAAA,UAAO;;;KADF", "debugId": null}}]}