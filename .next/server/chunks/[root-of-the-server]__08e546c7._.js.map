{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/lib/supabase-server.ts"], "sourcesContent": ["import { createServerClient as createSSRServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY\n\n// Check if Supabase is configured\nconst isSupabaseConfigured = supabaseUrl && supabaseAnonKey &&\n  supabaseUrl !== 'your_supabase_project_url' &&\n  supabaseAnonKey !== 'your_supabase_anon_key'\n\n// Server-side Supabase client (for API routes)\nexport const createServerClient = async () => {\n  if (!isSupabaseConfigured) {\n    throw new Error('Supabase is not configured. Please set NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY environment variables.')\n  }\n\n  const cookieStore = await cookies()\n\n  return createSSRServerClient(\n    supabaseUrl!,\n    supabaseAnonKey!,\n    {\n      cookies: {\n        getAll() {\n          return cookieStore.getAll()\n        },\n        setAll(cookiesToSet) {\n          try {\n            cookiesToSet.forEach(({ name, value, options }) =>\n              cookieStore.set(name, value, options)\n            )\n          } catch (error) {\n            // The `setAll` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    }\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEA,MAAM;AACN,MAAM;AAEN,kCAAkC;AAClC,MAAM,uBAAuB,eAAe,mBAC1C,gBAAgB,+BAChB,oBAAoB;AAGf,MAAM,qBAAqB;IAChC,uCAA2B;;IAE3B;IAEA,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAqB,AAAD,EACzB,aACA,iBACA;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAO,OAAO;gBACd,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 188, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/app/auth/callback/route.ts"], "sourcesContent": ["import { createServerClient } from '@/lib/supabase-server'\nimport { NextRequest, NextResponse } from 'next/server'\n\nexport async function GET(request: NextRequest) {\n  const requestUrl = new URL(request.url)\n  const code = requestUrl.searchParams.get('code')\n  const error = requestUrl.searchParams.get('error')\n  const errorDescription = requestUrl.searchParams.get('error_description')\n\n  // Handle OAuth errors\n  if (error) {\n    console.error('OAuth error:', error, errorDescription)\n    return NextResponse.redirect(`${requestUrl.origin}/?error=${error}&error_description=${encodeURIComponent(errorDescription || '')}`)\n  }\n\n  if (code) {\n    try {\n      const supabase = await createServerClient()\n\n      // Try to exchange the code for a session\n      const { data, error: exchangeError } = await supabase.auth.exchangeCodeForSession(code)\n\n      if (exchangeError) {\n        console.error('Error exchanging code for session:', exchangeError)\n\n        // If it's a PKCE error, try to handle it differently\n        if (exchangeError.message.includes('code verifier')) {\n          console.log('PKCE flow error detected, this might be due to configuration mismatch')\n          return NextResponse.redirect(`${requestUrl.origin}/?error=pkce_error&error_description=${encodeURIComponent('Authentication flow mismatch. Please check your Supabase OAuth configuration.')}`)\n        }\n\n        return NextResponse.redirect(`${requestUrl.origin}/?error=auth_error&error_description=${encodeURIComponent(exchangeError.message)}`)\n      }\n\n      // Log successful authentication for debugging\n      if (data.user) {\n        console.log('User authenticated successfully:', {\n          id: data.user.id,\n          email: data.user.email,\n          metadata: data.user.user_metadata,\n          identities: data.user.identities?.map(i => ({ provider: i.provider, id: i.id }))\n        })\n\n        // Check if this is a different account than previously signed in\n        const previousUserId = request.cookies.get('previous_user_id')?.value\n        if (previousUserId && previousUserId !== data.user.id) {\n          console.log('Different user account detected:', {\n            previousUserId,\n            currentUserId: data.user.id,\n            currentEmail: data.user.email\n          })\n        }\n      }\n\n      // Create a response that will set the auth cookies\n      const response = NextResponse.redirect(`${requestUrl.origin}/`)\n\n      // Set a cookie to track the current user for detecting account switches\n      if (data.user) {\n        response.cookies.set('previous_user_id', data.user.id, {\n          httpOnly: true,\n          secure: process.env.NODE_ENV === 'production',\n          sameSite: 'lax',\n          maxAge: 60 * 60 * 24 * 30 // 30 days\n        })\n      }\n\n      return response\n\n    } catch (error) {\n      console.error('Unexpected error in auth callback:', error)\n      return NextResponse.redirect(`${requestUrl.origin}/?error=unexpected_error&error_description=${encodeURIComponent('An unexpected error occurred during authentication')}`)\n    }\n  }\n\n  // Redirect to home page if no code (shouldn't happen in normal flow)\n  return NextResponse.redirect(`${requestUrl.origin}/`)\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,MAAM,aAAa,IAAI,IAAI,QAAQ,GAAG;IACtC,MAAM,OAAO,WAAW,YAAY,CAAC,GAAG,CAAC;IACzC,MAAM,QAAQ,WAAW,YAAY,CAAC,GAAG,CAAC;IAC1C,MAAM,mBAAmB,WAAW,YAAY,CAAC,GAAG,CAAC;IAErD,sBAAsB;IACtB,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,gBAAgB,OAAO;QACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,GAAG,WAAW,MAAM,CAAC,QAAQ,EAAE,MAAM,mBAAmB,EAAE,mBAAmB,oBAAoB,KAAK;IACrI;IAEA,IAAI,MAAM;QACR,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,kIAAA,CAAA,qBAAkB,AAAD;YAExC,yCAAyC;YACzC,MAAM,EAAE,IAAI,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,sBAAsB,CAAC;YAElF,IAAI,eAAe;gBACjB,QAAQ,KAAK,CAAC,sCAAsC;gBAEpD,qDAAqD;gBACrD,IAAI,cAAc,OAAO,CAAC,QAAQ,CAAC,kBAAkB;oBACnD,QAAQ,GAAG,CAAC;oBACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,GAAG,WAAW,MAAM,CAAC,qCAAqC,EAAE,mBAAmB,kFAAkF;gBAChM;gBAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,GAAG,WAAW,MAAM,CAAC,qCAAqC,EAAE,mBAAmB,cAAc,OAAO,GAAG;YACtI;YAEA,8CAA8C;YAC9C,IAAI,KAAK,IAAI,EAAE;gBACb,QAAQ,GAAG,CAAC,oCAAoC;oBAC9C,IAAI,KAAK,IAAI,CAAC,EAAE;oBAChB,OAAO,KAAK,IAAI,CAAC,KAAK;oBACtB,UAAU,KAAK,IAAI,CAAC,aAAa;oBACjC,YAAY,KAAK,IAAI,CAAC,UAAU,EAAE,IAAI,CAAA,IAAK,CAAC;4BAAE,UAAU,EAAE,QAAQ;4BAAE,IAAI,EAAE,EAAE;wBAAC,CAAC;gBAChF;gBAEA,iEAAiE;gBACjE,MAAM,iBAAiB,QAAQ,OAAO,CAAC,GAAG,CAAC,qBAAqB;gBAChE,IAAI,kBAAkB,mBAAmB,KAAK,IAAI,CAAC,EAAE,EAAE;oBACrD,QAAQ,GAAG,CAAC,oCAAoC;wBAC9C;wBACA,eAAe,KAAK,IAAI,CAAC,EAAE;wBAC3B,cAAc,KAAK,IAAI,CAAC,KAAK;oBAC/B;gBACF;YACF;YAEA,mDAAmD;YACnD,MAAM,WAAW,gIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,GAAG,WAAW,MAAM,CAAC,CAAC,CAAC;YAE9D,wEAAwE;YACxE,IAAI,KAAK,IAAI,EAAE;gBACb,SAAS,OAAO,CAAC,GAAG,CAAC,oBAAoB,KAAK,IAAI,CAAC,EAAE,EAAE;oBACrD,UAAU;oBACV,QAAQ,oDAAyB;oBACjC,UAAU;oBACV,QAAQ,KAAK,KAAK,KAAK,GAAG,UAAU;gBACtC;YACF;YAEA,OAAO;QAET,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,OAAO,gIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,GAAG,WAAW,MAAM,CAAC,2CAA2C,EAAE,mBAAmB,uDAAuD;QAC3K;IACF;IAEA,qEAAqE;IACrE,OAAO,gIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,GAAG,WAAW,MAAM,CAAC,CAAC,CAAC;AACtD", "debugId": null}}]}