{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/components/auth/LoginButton.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { LogIn, LogOut, User } from 'lucide-react'\n\nexport default function LoginButton() {\n  const { user, signInWithGoogle, signOut, loading } = useAuth()\n  const [isLoading, setIsLoading] = useState(false)\n\n  const handleSignIn = async () => {\n    setIsLoading(true)\n    try {\n      await signInWithGoogle()\n    } catch (error) {\n      console.error('Sign in error:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleSignOut = async () => {\n    setIsLoading(true)\n    try {\n      await signOut()\n    } catch (error) {\n      console.error('Sign out error:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"animate-pulse bg-gray-200 rounded-md h-10 w-24\"></div>\n    )\n  }\n\n  if (user) {\n    return null // Account button will be handled by AccountButton component\n  }\n\n  return (\n    <button\n      onClick={handleSignIn}\n      disabled={isLoading}\n      className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n    >\n      {isLoading ? (\n        <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n      ) : (\n        <>\n          <LogIn className=\"w-4 h-4 mr-2\" />\n          Sign in with Google\n        </>\n      )}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,eAAe;QACnB,aAAa;QACb,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;QAClC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB;QACpB,aAAa;QACb,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;QACnC,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;;;;;IAEnB;IAEA,IAAI,MAAM;QACR,OAAO,KAAK,4DAA4D;;IAC1E;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,UAAU;QACV,WAAU;kBAET,0BACC,8OAAC;YAAI,WAAU;;;;;iCAEf;;8BACE,8OAAC,wMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;gBAAiB;;;;;;;;AAM5C", "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/components/auth/AccountButton.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useRouter } from 'next/navigation'\nimport { User, Settings, LogOut, ChevronDown } from 'lucide-react'\n\nexport default function AccountButton() {\n  const { user, signOut, loading } = useAuth()\n  const [isDropdownOpen, setIsDropdownOpen] = useState(false)\n  const [isLoading, setIsLoading] = useState(false)\n  const dropdownRef = useRef<HTMLDivElement>(null)\n  const router = useRouter()\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    function handleClickOutside(event: MouseEvent) {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsDropdownOpen(false)\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside)\n    }\n  }, [])\n\n  const handleSignOut = async () => {\n    setIsLoading(true)\n    try {\n      await signOut()\n      setIsDropdownOpen(false)\n      router.push('/')\n    } catch (error) {\n      console.error('Sign out error:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleProfileClick = () => {\n    setIsDropdownOpen(false)\n    router.push('/profile')\n  }\n\n  const getInitials = (name: string) => {\n    return name\n      .split(' ')\n      .map(word => word.charAt(0))\n      .join('')\n      .toUpperCase()\n      .slice(0, 2)\n  }\n\n  if (loading || !user) {\n    return null\n  }\n\n  return (\n    <div className=\"relative\" ref={dropdownRef}>\n      <button\n        onClick={() => setIsDropdownOpen(!isDropdownOpen)}\n        className=\"flex items-center space-x-2 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors\"\n      >\n        {user.avatar_url ? (\n          <img\n            src={user.avatar_url}\n            alt={user.display_name}\n            className=\"w-8 h-8 rounded-full object-cover\"\n          />\n        ) : (\n          <div className=\"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium\">\n            {getInitials(user.display_name)}\n          </div>\n        )}\n        <span className=\"hidden sm:block\">{user.display_name}</span>\n        <ChevronDown className={`w-4 h-4 transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`} />\n      </button>\n\n      {isDropdownOpen && (\n        <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50\">\n          <div className=\"py-1\">\n            <div className=\"px-4 py-2 text-sm text-gray-500 border-b\">\n              <div className=\"font-medium text-gray-900\">{user.display_name}</div>\n              <div className=\"text-xs\">{user.email}</div>\n            </div>\n            \n            <button\n              onClick={handleProfileClick}\n              className=\"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\"\n            >\n              <User className=\"w-4 h-4 mr-3\" />\n              Profile\n            </button>\n            \n            <button\n              onClick={handleProfileClick}\n              className=\"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\"\n            >\n              <Settings className=\"w-4 h-4 mr-3\" />\n              Account Settings\n            </button>\n            \n            <div className=\"border-t\">\n              <button\n                onClick={handleSignOut}\n                disabled={isLoading}\n                className=\"flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors disabled:opacity-50\"\n              >\n                {isLoading ? (\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-3\"></div>\n                ) : (\n                  <LogOut className=\"w-4 h-4 mr-3\" />\n                )}\n                {isLoading ? 'Signing out...' : 'Sign Out'}\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACzC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,mBAAmB,KAAiB;YAC3C,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E,kBAAkB;YACpB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,aAAa;QACb,IAAI;YACF,MAAM;YACN,kBAAkB;YAClB,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;QACnC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,qBAAqB;QACzB,kBAAkB;QAClB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,IACxB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,IAAI,WAAW,CAAC,MAAM;QACpB,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;QAAW,KAAK;;0BAC7B,8OAAC;gBACC,SAAS,IAAM,kBAAkB,CAAC;gBAClC,WAAU;;oBAET,KAAK,UAAU,iBACd,8OAAC;wBACC,KAAK,KAAK,UAAU;wBACpB,KAAK,KAAK,YAAY;wBACtB,WAAU;;;;;6CAGZ,8OAAC;wBAAI,WAAU;kCACZ,YAAY,KAAK,YAAY;;;;;;kCAGlC,8OAAC;wBAAK,WAAU;kCAAmB,KAAK,YAAY;;;;;;kCACpD,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAW,CAAC,6BAA6B,EAAE,iBAAiB,eAAe,IAAI;;;;;;;;;;;;YAG7F,gCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAA6B,KAAK,YAAY;;;;;;8CAC7D,8OAAC;oCAAI,WAAU;8CAAW,KAAK,KAAK;;;;;;;;;;;;sCAGtC,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAInC,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAIvC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;;oCAET,0BACC,8OAAC;wCAAI,WAAU;;;;;6DAEf,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAEnB,YAAY,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhD", "debugId": null}}, {"offset": {"line": 337, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/components/layout/Navigation.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { useAuth } from '@/contexts/AuthContext'\nimport LoginButton from '@/components/auth/LoginButton'\nimport AccountButton from '@/components/auth/AccountButton'\nimport { Crown, Home, User, Gamepad2 } from 'lucide-react'\n\nexport default function Navigation() {\n  const { user } = useAuth()\n\n  return (\n    <nav className=\"bg-white shadow-lg\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <Crown className=\"h-8 w-8 text-blue-600\" />\n              <span className=\"text-xl font-bold text-gray-900\">ChessHub</span>\n            </Link>\n          </div>\n\n          <div className=\"flex items-center space-x-8\">\n            <Link\n              href=\"/\"\n              className=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium\"\n            >\n              <Home className=\"w-4 h-4\" />\n              <span>Home</span>\n            </Link>\n\n            <Link\n              href=\"/demo\"\n              className=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium\"\n            >\n              <Gamepad2 className=\"w-4 h-4\" />\n              <span>Demo</span>\n            </Link>\n\n            {user && (\n              <>\n                <Link\n                  href=\"/play\"\n                  className=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  <Gamepad2 className=\"w-4 h-4\" />\n                  <span>Play</span>\n                </Link>\n                <Link\n                  href=\"/profile\"\n                  className=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  <User className=\"w-4 h-4\" />\n                  <span>Profile</span>\n                </Link>\n              </>\n            )}\n\n            <LoginButton />\n            <AccountButton />\n          </div>\n        </div>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEvB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;;;;;;kCAItD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,mMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;kDAAK;;;;;;;;;;;;0CAGR,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;kDAAK;;;;;;;;;;;;4BAGP,sBACC;;kDACE,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAK;;;;;;;;;;;;;;0CAKZ,8OAAC,yIAAA,CAAA,UAAW;;;;;0CACZ,8OAAC,2IAAA,CAAA,UAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM1B", "debugId": null}}, {"offset": {"line": 539, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/app/profile/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useRouter } from 'next/navigation'\nimport { useEffect, useState } from 'react'\nimport Navigation from '@/components/layout/Navigation'\nimport { User, Mail, Calendar, Edit2, Save, X, Camera, LogOut } from 'lucide-react'\n\nexport default function ProfilePage() {\n  const { user, supabaseUser, loading, updateUserProfile, signOut } = useAuth()\n  const router = useRouter()\n  const [isEditing, setIsEditing] = useState(false)\n  const [editForm, setEditForm] = useState({\n    display_name: '',\n    avatar_url: ''\n  })\n  const [isSaving, setIsSaving] = useState(false)\n  const [isSigningOut, setIsSigningOut] = useState(false)\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/')\n    }\n  }, [user, loading, router])\n\n  useEffect(() => {\n    if (user) {\n      setEditForm({\n        display_name: user.display_name || '',\n        avatar_url: user.avatar_url || ''\n      })\n    }\n  }, [user])\n\n  const handleEditToggle = () => {\n    if (isEditing) {\n      // Reset form when canceling\n      setEditForm({\n        display_name: user?.display_name || '',\n        avatar_url: user?.avatar_url || ''\n      })\n    }\n    setIsEditing(!isEditing)\n  }\n\n  const handleSave = async () => {\n    if (!user) return\n\n    setIsSaving(true)\n    try {\n      await updateUserProfile({\n        display_name: editForm.display_name,\n        avatar_url: editForm.avatar_url || undefined\n      })\n\n      setIsEditing(false)\n    } catch (error) {\n      console.error('Error saving profile:', error)\n      alert('Failed to save profile. Please try again.')\n    } finally {\n      setIsSaving(false)\n    }\n  }\n\n  const handleSignOut = async () => {\n    setIsSigningOut(true)\n    try {\n      await signOut()\n      router.push('/')\n    } catch (error) {\n      console.error('Error signing out:', error)\n      alert('Failed to sign out. Please try again.')\n    } finally {\n      setIsSigningOut(false)\n    }\n  }\n\n  const getInitials = (name: string) => {\n    return name\n      .split(' ')\n      .map(word => word.charAt(0))\n      .join('')\n      .toUpperCase()\n      .slice(0, 2)\n  }\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    })\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Navigation />\n        <div className=\"max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8\">\n          <div className=\"animate-pulse\">\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <div className=\"h-8 bg-gray-200 rounded w-1/4 mb-6\"></div>\n              <div className=\"flex items-center space-x-6 mb-8\">\n                <div className=\"w-24 h-24 bg-gray-200 rounded-full\"></div>\n                <div className=\"space-y-2\">\n                  <div className=\"h-6 bg-gray-200 rounded w-48\"></div>\n                  <div className=\"h-4 bg-gray-200 rounded w-64\"></div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return null\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navigation />\n      \n      <div className=\"max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8\">\n        <div className=\"bg-white rounded-lg shadow\">\n          <div className=\"px-6 py-4 border-b border-gray-200\">\n            <div className=\"flex items-center justify-between\">\n              <h1 className=\"text-2xl font-bold text-gray-900\">Profile</h1>\n              <button\n                onClick={handleEditToggle}\n                className=\"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                {isEditing ? (\n                  <>\n                    <X className=\"w-4 h-4 mr-2\" />\n                    Cancel\n                  </>\n                ) : (\n                  <>\n                    <Edit2 className=\"w-4 h-4 mr-2\" />\n                    Edit Profile\n                  </>\n                )}\n              </button>\n            </div>\n          </div>\n\n          <div className=\"p-6\">\n            <div className=\"flex items-center space-x-6 mb-8\">\n              <div className=\"relative\">\n                {user.avatar_url ? (\n                  <img\n                    src={user.avatar_url}\n                    alt={user.display_name}\n                    className=\"w-24 h-24 rounded-full object-cover\"\n                  />\n                ) : (\n                  <div className=\"w-24 h-24 bg-blue-600 rounded-full flex items-center justify-center text-white text-2xl font-bold\">\n                    {getInitials(user.display_name)}\n                  </div>\n                )}\n                {isEditing && (\n                  <button className=\"absolute bottom-0 right-0 bg-blue-600 text-white rounded-full p-2 hover:bg-blue-700 transition-colors\">\n                    <Camera className=\"w-4 h-4\" />\n                  </button>\n                )}\n              </div>\n\n              <div className=\"flex-1\">\n                {isEditing ? (\n                  <div className=\"space-y-4\">\n                    <div>\n                      <label htmlFor=\"display_name\" className=\"block text-sm font-medium text-gray-700\">\n                        Display Name\n                      </label>\n                      <input\n                        type=\"text\"\n                        id=\"display_name\"\n                        value={editForm.display_name}\n                        onChange={(e) => setEditForm({ ...editForm, display_name: e.target.value })}\n                        className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                      />\n                    </div>\n                    <div>\n                      <label htmlFor=\"avatar_url\" className=\"block text-sm font-medium text-gray-700\">\n                        Avatar URL\n                      </label>\n                      <input\n                        type=\"url\"\n                        id=\"avatar_url\"\n                        value={editForm.avatar_url}\n                        onChange={(e) => setEditForm({ ...editForm, avatar_url: e.target.value })}\n                        className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                        placeholder=\"https://example.com/avatar.jpg\"\n                      />\n                    </div>\n                    <button\n                      onClick={handleSave}\n                      disabled={isSaving}\n                      className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50\"\n                    >\n                      {isSaving ? (\n                        <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                      ) : (\n                        <Save className=\"w-4 h-4 mr-2\" />\n                      )}\n                      {isSaving ? 'Saving...' : 'Save Changes'}\n                    </button>\n                  </div>\n                ) : (\n                  <div>\n                    <h2 className=\"text-2xl font-bold text-gray-900\">{user.display_name}</h2>\n                    <p className=\"text-gray-600 flex items-center mt-2\">\n                      <Mail className=\"w-4 h-4 mr-2\" />\n                      {user.email}\n                    </p>\n                    <p className=\"text-gray-600 flex items-center mt-1\">\n                      <Calendar className=\"w-4 h-4 mr-2\" />\n                      Member since {formatDate(user.created_at)}\n                    </p>\n                  </div>\n                )}\n              </div>\n            </div>\n\n            <div className=\"border-t border-gray-200 pt-6\">\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Account Information</h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <dt className=\"text-sm font-medium text-gray-500\">User ID</dt>\n                  <dd className=\"mt-1 text-sm text-gray-900 font-mono\">{user.id}</dd>\n                </div>\n                <div>\n                  <dt className=\"text-sm font-medium text-gray-500\">Account Created</dt>\n                  <dd className=\"mt-1 text-sm text-gray-900\">{formatDate(user.created_at)}</dd>\n                </div>\n                <div>\n                  <dt className=\"text-sm font-medium text-gray-500\">Last Updated</dt>\n                  <dd className=\"mt-1 text-sm text-gray-900\">{formatDate(user.updated_at)}</dd>\n                </div>\n                <div>\n                  <dt className=\"text-sm font-medium text-gray-500\">Authentication Provider</dt>\n                  <dd className=\"mt-1 text-sm text-gray-900\">Google</dd>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"border-t border-gray-200 pt-6\">\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Account Actions</h3>\n              <button\n                onClick={handleSignOut}\n                disabled={isSigningOut}\n                className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50\"\n              >\n                {isSigningOut ? (\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                ) : (\n                  <LogOut className=\"w-4 h-4 mr-2\" />\n                )}\n                {isSigningOut ? 'Signing out...' : 'Sign Out'}\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,iBAAiB,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC1E,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,cAAc;QACd,YAAY;IACd;IACA,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,MAAM;YACrB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR,YAAY;gBACV,cAAc,KAAK,YAAY,IAAI;gBACnC,YAAY,KAAK,UAAU,IAAI;YACjC;QACF;IACF,GAAG;QAAC;KAAK;IAET,MAAM,mBAAmB;QACvB,IAAI,WAAW;YACb,4BAA4B;YAC5B,YAAY;gBACV,cAAc,MAAM,gBAAgB;gBACpC,YAAY,MAAM,cAAc;YAClC;QACF;QACA,aAAa,CAAC;IAChB;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,MAAM;QAEX,YAAY;QACZ,IAAI;YACF,MAAM,kBAAkB;gBACtB,cAAc,SAAS,YAAY;gBACnC,YAAY,SAAS,UAAU,IAAI;YACrC;YAEA,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,gBAAgB;QACpB,gBAAgB;QAChB,IAAI;YACF,MAAM;YACN,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,MAAM;QACR,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,IACxB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0IAAA,CAAA,UAAU;;;;;8BACX,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQ/B;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,0IAAA,CAAA,UAAU;;;;;0BAEX,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,8OAAC;wCACC,SAAS;wCACT,WAAU;kDAET,0BACC;;8DACE,8OAAC,4LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;gDAAiB;;yEAIhC;;8DACE,8OAAC,kMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;sCAQ5C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;gDACZ,KAAK,UAAU,iBACd,8OAAC;oDACC,KAAK,KAAK,UAAU;oDACpB,KAAK,KAAK,YAAY;oDACtB,WAAU;;;;;yEAGZ,8OAAC;oDAAI,WAAU;8DACZ,YAAY,KAAK,YAAY;;;;;;gDAGjC,2BACC,8OAAC;oDAAO,WAAU;8DAChB,cAAA,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAKxB,8OAAC;4CAAI,WAAU;sDACZ,0BACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAe,WAAU;0EAA0C;;;;;;0EAGlF,8OAAC;gEACC,MAAK;gEACL,IAAG;gEACH,OAAO,SAAS,YAAY;gEAC5B,UAAU,CAAC,IAAM,YAAY;wEAAE,GAAG,QAAQ;wEAAE,cAAc,EAAE,MAAM,CAAC,KAAK;oEAAC;gEACzE,WAAU;;;;;;;;;;;;kEAGd,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAa,WAAU;0EAA0C;;;;;;0EAGhF,8OAAC;gEACC,MAAK;gEACL,IAAG;gEACH,OAAO,SAAS,UAAU;gEAC1B,UAAU,CAAC,IAAM,YAAY;wEAAE,GAAG,QAAQ;wEAAE,YAAY,EAAE,MAAM,CAAC,KAAK;oEAAC;gEACvE,WAAU;gEACV,aAAY;;;;;;;;;;;;kEAGhB,8OAAC;wDACC,SAAS;wDACT,UAAU;wDACV,WAAU;;4DAET,yBACC,8OAAC;gEAAI,WAAU;;;;;qFAEf,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAEjB,WAAW,cAAc;;;;;;;;;;;;qEAI9B,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAoC,KAAK,YAAY;;;;;;kEACnE,8OAAC;wDAAE,WAAU;;0EACX,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DACf,KAAK,KAAK;;;;;;;kEAEb,8OAAC;wDAAE,WAAU;;0EACX,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAiB;4DACvB,WAAW,KAAK,UAAU;;;;;;;;;;;;;;;;;;;;;;;;8CAOlD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAoC;;;;;;sEAClD,8OAAC;4DAAG,WAAU;sEAAwC,KAAK,EAAE;;;;;;;;;;;;8DAE/D,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAoC;;;;;;sEAClD,8OAAC;4DAAG,WAAU;sEAA8B,WAAW,KAAK,UAAU;;;;;;;;;;;;8DAExE,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAoC;;;;;;sEAClD,8OAAC;4DAAG,WAAU;sEAA8B,WAAW,KAAK,UAAU;;;;;;;;;;;;8DAExE,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAoC;;;;;;sEAClD,8OAAC;4DAAG,WAAU;sEAA6B;;;;;;;;;;;;;;;;;;;;;;;;8CAKjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,8OAAC;4CACC,SAAS;4CACT,UAAU;4CACV,WAAU;;gDAET,6BACC,8OAAC;oDAAI,WAAU;;;;;yEAEf,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAEnB,eAAe,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnD", "debugId": null}}]}