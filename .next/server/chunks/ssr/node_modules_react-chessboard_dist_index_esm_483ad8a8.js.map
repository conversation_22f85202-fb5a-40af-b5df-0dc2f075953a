{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/node_modules/react-chessboard/dist/index.esm.js"], "sourcesContent": ["import React, { useLayoutEffect, useEffect, useRef, useMemo, useCallback, useState, createContext, memo, useReducer, useContext, forwardRef, cloneElement, use, Fragment } from 'react';\nimport { unstable_batchedUpdates, createPortal } from 'react-dom';\n\nvar jsxRuntime = {exports: {}};\n\nvar reactJsxRuntime_production = {};\n\n/**\n * @license React\n * react-jsx-runtime.production.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nvar hasRequiredReactJsxRuntime_production;\n\nfunction requireReactJsxRuntime_production () {\n\tif (hasRequiredReactJsxRuntime_production) return reactJsxRuntime_production;\n\thasRequiredReactJsxRuntime_production = 1;\n\tvar REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n\t  REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\");\n\tfunction jsxProd(type, config, maybeKey) {\n\t  var key = null;\n\t  void 0 !== maybeKey && (key = \"\" + maybeKey);\n\t  void 0 !== config.key && (key = \"\" + config.key);\n\t  if (\"key\" in config) {\n\t    maybeKey = {};\n\t    for (var propName in config)\n\t      \"key\" !== propName && (maybeKey[propName] = config[propName]);\n\t  } else maybeKey = config;\n\t  config = maybeKey.ref;\n\t  return {\n\t    $$typeof: REACT_ELEMENT_TYPE,\n\t    type: type,\n\t    key: key,\n\t    ref: void 0 !== config ? config : null,\n\t    props: maybeKey\n\t  };\n\t}\n\treactJsxRuntime_production.Fragment = REACT_FRAGMENT_TYPE;\n\treactJsxRuntime_production.jsx = jsxProd;\n\treactJsxRuntime_production.jsxs = jsxProd;\n\treturn reactJsxRuntime_production;\n}\n\nvar reactJsxRuntime_development = {};\n\n/**\n * @license React\n * react-jsx-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nvar hasRequiredReactJsxRuntime_development;\n\nfunction requireReactJsxRuntime_development () {\n\tif (hasRequiredReactJsxRuntime_development) return reactJsxRuntime_development;\n\thasRequiredReactJsxRuntime_development = 1;\n\t\"production\" !== process.env.NODE_ENV &&\n\t  (function () {\n\t    function getComponentNameFromType(type) {\n\t      if (null == type) return null;\n\t      if (\"function\" === typeof type)\n\t        return type.$$typeof === REACT_CLIENT_REFERENCE$2\n\t          ? null\n\t          : type.displayName || type.name || null;\n\t      if (\"string\" === typeof type) return type;\n\t      switch (type) {\n\t        case REACT_FRAGMENT_TYPE:\n\t          return \"Fragment\";\n\t        case REACT_PORTAL_TYPE:\n\t          return \"Portal\";\n\t        case REACT_PROFILER_TYPE:\n\t          return \"Profiler\";\n\t        case REACT_STRICT_MODE_TYPE:\n\t          return \"StrictMode\";\n\t        case REACT_SUSPENSE_TYPE:\n\t          return \"Suspense\";\n\t        case REACT_SUSPENSE_LIST_TYPE:\n\t          return \"SuspenseList\";\n\t      }\n\t      if (\"object\" === typeof type)\n\t        switch (\n\t          (\"number\" === typeof type.tag &&\n\t            console.error(\n\t              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n\t            ),\n\t          type.$$typeof)\n\t        ) {\n\t          case REACT_CONTEXT_TYPE:\n\t            return (type.displayName || \"Context\") + \".Provider\";\n\t          case REACT_CONSUMER_TYPE:\n\t            return (type._context.displayName || \"Context\") + \".Consumer\";\n\t          case REACT_FORWARD_REF_TYPE:\n\t            var innerType = type.render;\n\t            type = type.displayName;\n\t            type ||\n\t              ((type = innerType.displayName || innerType.name || \"\"),\n\t              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n\t            return type;\n\t          case REACT_MEMO_TYPE:\n\t            return (\n\t              (innerType = type.displayName || null),\n\t              null !== innerType\n\t                ? innerType\n\t                : getComponentNameFromType(type.type) || \"Memo\"\n\t            );\n\t          case REACT_LAZY_TYPE:\n\t            innerType = type._payload;\n\t            type = type._init;\n\t            try {\n\t              return getComponentNameFromType(type(innerType));\n\t            } catch (x) {}\n\t        }\n\t      return null;\n\t    }\n\t    function testStringCoercion(value) {\n\t      return \"\" + value;\n\t    }\n\t    function checkKeyStringCoercion(value) {\n\t      try {\n\t        testStringCoercion(value);\n\t        var JSCompiler_inline_result = !1;\n\t      } catch (e) {\n\t        JSCompiler_inline_result = true;\n\t      }\n\t      if (JSCompiler_inline_result) {\n\t        JSCompiler_inline_result = console;\n\t        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n\t        var JSCompiler_inline_result$jscomp$0 =\n\t          (\"function\" === typeof Symbol &&\n\t            Symbol.toStringTag &&\n\t            value[Symbol.toStringTag]) ||\n\t          value.constructor.name ||\n\t          \"Object\";\n\t        JSCompiler_temp_const.call(\n\t          JSCompiler_inline_result,\n\t          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n\t          JSCompiler_inline_result$jscomp$0\n\t        );\n\t        return testStringCoercion(value);\n\t      }\n\t    }\n\t    function disabledLog() {}\n\t    function disableLogs() {\n\t      if (0 === disabledDepth) {\n\t        prevLog = console.log;\n\t        prevInfo = console.info;\n\t        prevWarn = console.warn;\n\t        prevError = console.error;\n\t        prevGroup = console.group;\n\t        prevGroupCollapsed = console.groupCollapsed;\n\t        prevGroupEnd = console.groupEnd;\n\t        var props = {\n\t          configurable: true,\n\t          enumerable: true,\n\t          value: disabledLog,\n\t          writable: true\n\t        };\n\t        Object.defineProperties(console, {\n\t          info: props,\n\t          log: props,\n\t          warn: props,\n\t          error: props,\n\t          group: props,\n\t          groupCollapsed: props,\n\t          groupEnd: props\n\t        });\n\t      }\n\t      disabledDepth++;\n\t    }\n\t    function reenableLogs() {\n\t      disabledDepth--;\n\t      if (0 === disabledDepth) {\n\t        var props = { configurable: true, enumerable: true, writable: true };\n\t        Object.defineProperties(console, {\n\t          log: assign({}, props, { value: prevLog }),\n\t          info: assign({}, props, { value: prevInfo }),\n\t          warn: assign({}, props, { value: prevWarn }),\n\t          error: assign({}, props, { value: prevError }),\n\t          group: assign({}, props, { value: prevGroup }),\n\t          groupCollapsed: assign({}, props, { value: prevGroupCollapsed }),\n\t          groupEnd: assign({}, props, { value: prevGroupEnd })\n\t        });\n\t      }\n\t      0 > disabledDepth &&\n\t        console.error(\n\t          \"disabledDepth fell below zero. This is a bug in React. Please file an issue.\"\n\t        );\n\t    }\n\t    function describeBuiltInComponentFrame(name) {\n\t      if (void 0 === prefix)\n\t        try {\n\t          throw Error();\n\t        } catch (x) {\n\t          var match = x.stack.trim().match(/\\n( *(at )?)/);\n\t          prefix = (match && match[1]) || \"\";\n\t          suffix =\n\t            -1 < x.stack.indexOf(\"\\n    at\")\n\t              ? \" (<anonymous>)\"\n\t              : -1 < x.stack.indexOf(\"@\")\n\t                ? \"@unknown:0:0\"\n\t                : \"\";\n\t        }\n\t      return \"\\n\" + prefix + name + suffix;\n\t    }\n\t    function describeNativeComponentFrame(fn, construct) {\n\t      if (!fn || reentry) return \"\";\n\t      var frame = componentFrameCache.get(fn);\n\t      if (void 0 !== frame) return frame;\n\t      reentry = true;\n\t      frame = Error.prepareStackTrace;\n\t      Error.prepareStackTrace = void 0;\n\t      var previousDispatcher = null;\n\t      previousDispatcher = ReactSharedInternals.H;\n\t      ReactSharedInternals.H = null;\n\t      disableLogs();\n\t      try {\n\t        var RunInRootFrame = {\n\t          DetermineComponentFrameRoot: function () {\n\t            try {\n\t              if (construct) {\n\t                var Fake = function () {\n\t                  throw Error();\n\t                };\n\t                Object.defineProperty(Fake.prototype, \"props\", {\n\t                  set: function () {\n\t                    throw Error();\n\t                  }\n\t                });\n\t                if (\"object\" === typeof Reflect && Reflect.construct) {\n\t                  try {\n\t                    Reflect.construct(Fake, []);\n\t                  } catch (x) {\n\t                    var control = x;\n\t                  }\n\t                  Reflect.construct(fn, [], Fake);\n\t                } else {\n\t                  try {\n\t                    Fake.call();\n\t                  } catch (x$0) {\n\t                    control = x$0;\n\t                  }\n\t                  fn.call(Fake.prototype);\n\t                }\n\t              } else {\n\t                try {\n\t                  throw Error();\n\t                } catch (x$1) {\n\t                  control = x$1;\n\t                }\n\t                (Fake = fn()) &&\n\t                  \"function\" === typeof Fake.catch &&\n\t                  Fake.catch(function () {});\n\t              }\n\t            } catch (sample) {\n\t              if (sample && control && \"string\" === typeof sample.stack)\n\t                return [sample.stack, control.stack];\n\t            }\n\t            return [null, null];\n\t          }\n\t        };\n\t        RunInRootFrame.DetermineComponentFrameRoot.displayName =\n\t          \"DetermineComponentFrameRoot\";\n\t        var namePropDescriptor = Object.getOwnPropertyDescriptor(\n\t          RunInRootFrame.DetermineComponentFrameRoot,\n\t          \"name\"\n\t        );\n\t        namePropDescriptor &&\n\t          namePropDescriptor.configurable &&\n\t          Object.defineProperty(\n\t            RunInRootFrame.DetermineComponentFrameRoot,\n\t            \"name\",\n\t            { value: \"DetermineComponentFrameRoot\" }\n\t          );\n\t        var _RunInRootFrame$Deter =\n\t            RunInRootFrame.DetermineComponentFrameRoot(),\n\t          sampleStack = _RunInRootFrame$Deter[0],\n\t          controlStack = _RunInRootFrame$Deter[1];\n\t        if (sampleStack && controlStack) {\n\t          var sampleLines = sampleStack.split(\"\\n\"),\n\t            controlLines = controlStack.split(\"\\n\");\n\t          for (\n\t            _RunInRootFrame$Deter = namePropDescriptor = 0;\n\t            namePropDescriptor < sampleLines.length &&\n\t            !sampleLines[namePropDescriptor].includes(\n\t              \"DetermineComponentFrameRoot\"\n\t            );\n\n\t          )\n\t            namePropDescriptor++;\n\t          for (\n\t            ;\n\t            _RunInRootFrame$Deter < controlLines.length &&\n\t            !controlLines[_RunInRootFrame$Deter].includes(\n\t              \"DetermineComponentFrameRoot\"\n\t            );\n\n\t          )\n\t            _RunInRootFrame$Deter++;\n\t          if (\n\t            namePropDescriptor === sampleLines.length ||\n\t            _RunInRootFrame$Deter === controlLines.length\n\t          )\n\t            for (\n\t              namePropDescriptor = sampleLines.length - 1,\n\t                _RunInRootFrame$Deter = controlLines.length - 1;\n\t              1 <= namePropDescriptor &&\n\t              0 <= _RunInRootFrame$Deter &&\n\t              sampleLines[namePropDescriptor] !==\n\t                controlLines[_RunInRootFrame$Deter];\n\n\t            )\n\t              _RunInRootFrame$Deter--;\n\t          for (\n\t            ;\n\t            1 <= namePropDescriptor && 0 <= _RunInRootFrame$Deter;\n\t            namePropDescriptor--, _RunInRootFrame$Deter--\n\t          )\n\t            if (\n\t              sampleLines[namePropDescriptor] !==\n\t              controlLines[_RunInRootFrame$Deter]\n\t            ) {\n\t              if (1 !== namePropDescriptor || 1 !== _RunInRootFrame$Deter) {\n\t                do\n\t                  if (\n\t                    (namePropDescriptor--,\n\t                    _RunInRootFrame$Deter--,\n\t                    0 > _RunInRootFrame$Deter ||\n\t                      sampleLines[namePropDescriptor] !==\n\t                        controlLines[_RunInRootFrame$Deter])\n\t                  ) {\n\t                    var _frame =\n\t                      \"\\n\" +\n\t                      sampleLines[namePropDescriptor].replace(\n\t                        \" at new \",\n\t                        \" at \"\n\t                      );\n\t                    fn.displayName &&\n\t                      _frame.includes(\"<anonymous>\") &&\n\t                      (_frame = _frame.replace(\"<anonymous>\", fn.displayName));\n\t                    \"function\" === typeof fn &&\n\t                      componentFrameCache.set(fn, _frame);\n\t                    return _frame;\n\t                  }\n\t                while (1 <= namePropDescriptor && 0 <= _RunInRootFrame$Deter);\n\t              }\n\t              break;\n\t            }\n\t        }\n\t      } finally {\n\t        (reentry = false),\n\t          (ReactSharedInternals.H = previousDispatcher),\n\t          reenableLogs(),\n\t          (Error.prepareStackTrace = frame);\n\t      }\n\t      sampleLines = (sampleLines = fn ? fn.displayName || fn.name : \"\")\n\t        ? describeBuiltInComponentFrame(sampleLines)\n\t        : \"\";\n\t      \"function\" === typeof fn && componentFrameCache.set(fn, sampleLines);\n\t      return sampleLines;\n\t    }\n\t    function describeUnknownElementTypeFrameInDEV(type) {\n\t      if (null == type) return \"\";\n\t      if (\"function\" === typeof type) {\n\t        var prototype = type.prototype;\n\t        return describeNativeComponentFrame(\n\t          type,\n\t          !(!prototype || !prototype.isReactComponent)\n\t        );\n\t      }\n\t      if (\"string\" === typeof type) return describeBuiltInComponentFrame(type);\n\t      switch (type) {\n\t        case REACT_SUSPENSE_TYPE:\n\t          return describeBuiltInComponentFrame(\"Suspense\");\n\t        case REACT_SUSPENSE_LIST_TYPE:\n\t          return describeBuiltInComponentFrame(\"SuspenseList\");\n\t      }\n\t      if (\"object\" === typeof type)\n\t        switch (type.$$typeof) {\n\t          case REACT_FORWARD_REF_TYPE:\n\t            return (type = describeNativeComponentFrame(type.render, false)), type;\n\t          case REACT_MEMO_TYPE:\n\t            return describeUnknownElementTypeFrameInDEV(type.type);\n\t          case REACT_LAZY_TYPE:\n\t            prototype = type._payload;\n\t            type = type._init;\n\t            try {\n\t              return describeUnknownElementTypeFrameInDEV(type(prototype));\n\t            } catch (x) {}\n\t        }\n\t      return \"\";\n\t    }\n\t    function getOwner() {\n\t      var dispatcher = ReactSharedInternals.A;\n\t      return null === dispatcher ? null : dispatcher.getOwner();\n\t    }\n\t    function hasValidKey(config) {\n\t      if (hasOwnProperty.call(config, \"key\")) {\n\t        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n\t        if (getter && getter.isReactWarning) return false;\n\t      }\n\t      return void 0 !== config.key;\n\t    }\n\t    function defineKeyPropWarningGetter(props, displayName) {\n\t      function warnAboutAccessingKey() {\n\t        specialPropKeyWarningShown ||\n\t          ((specialPropKeyWarningShown = true),\n\t          console.error(\n\t            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n\t            displayName\n\t          ));\n\t      }\n\t      warnAboutAccessingKey.isReactWarning = true;\n\t      Object.defineProperty(props, \"key\", {\n\t        get: warnAboutAccessingKey,\n\t        configurable: true\n\t      });\n\t    }\n\t    function elementRefGetterWithDeprecationWarning() {\n\t      var componentName = getComponentNameFromType(this.type);\n\t      didWarnAboutElementRef[componentName] ||\n\t        ((didWarnAboutElementRef[componentName] = true),\n\t        console.error(\n\t          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n\t        ));\n\t      componentName = this.props.ref;\n\t      return void 0 !== componentName ? componentName : null;\n\t    }\n\t    function ReactElement(type, key, self, source, owner, props) {\n\t      self = props.ref;\n\t      type = {\n\t        $$typeof: REACT_ELEMENT_TYPE,\n\t        type: type,\n\t        key: key,\n\t        props: props,\n\t        _owner: owner\n\t      };\n\t      null !== (void 0 !== self ? self : null)\n\t        ? Object.defineProperty(type, \"ref\", {\n\t            enumerable: false,\n\t            get: elementRefGetterWithDeprecationWarning\n\t          })\n\t        : Object.defineProperty(type, \"ref\", { enumerable: false, value: null });\n\t      type._store = {};\n\t      Object.defineProperty(type._store, \"validated\", {\n\t        configurable: false,\n\t        enumerable: false,\n\t        writable: true,\n\t        value: 0\n\t      });\n\t      Object.defineProperty(type, \"_debugInfo\", {\n\t        configurable: false,\n\t        enumerable: false,\n\t        writable: true,\n\t        value: null\n\t      });\n\t      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n\t      return type;\n\t    }\n\t    function jsxDEVImpl(\n\t      type,\n\t      config,\n\t      maybeKey,\n\t      isStaticChildren,\n\t      source,\n\t      self\n\t    ) {\n\t      if (\n\t        \"string\" === typeof type ||\n\t        \"function\" === typeof type ||\n\t        type === REACT_FRAGMENT_TYPE ||\n\t        type === REACT_PROFILER_TYPE ||\n\t        type === REACT_STRICT_MODE_TYPE ||\n\t        type === REACT_SUSPENSE_TYPE ||\n\t        type === REACT_SUSPENSE_LIST_TYPE ||\n\t        type === REACT_OFFSCREEN_TYPE ||\n\t        (\"object\" === typeof type &&\n\t          null !== type &&\n\t          (type.$$typeof === REACT_LAZY_TYPE ||\n\t            type.$$typeof === REACT_MEMO_TYPE ||\n\t            type.$$typeof === REACT_CONTEXT_TYPE ||\n\t            type.$$typeof === REACT_CONSUMER_TYPE ||\n\t            type.$$typeof === REACT_FORWARD_REF_TYPE ||\n\t            type.$$typeof === REACT_CLIENT_REFERENCE$1 ||\n\t            void 0 !== type.getModuleId))\n\t      ) {\n\t        var children = config.children;\n\t        if (void 0 !== children)\n\t          if (isStaticChildren)\n\t            if (isArrayImpl(children)) {\n\t              for (\n\t                isStaticChildren = 0;\n\t                isStaticChildren < children.length;\n\t                isStaticChildren++\n\t              )\n\t                validateChildKeys(children[isStaticChildren], type);\n\t              Object.freeze && Object.freeze(children);\n\t            } else\n\t              console.error(\n\t                \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n\t              );\n\t          else validateChildKeys(children, type);\n\t      } else {\n\t        children = \"\";\n\t        if (\n\t          void 0 === type ||\n\t          (\"object\" === typeof type &&\n\t            null !== type &&\n\t            0 === Object.keys(type).length)\n\t        )\n\t          children +=\n\t            \" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\";\n\t        null === type\n\t          ? (isStaticChildren = \"null\")\n\t          : isArrayImpl(type)\n\t            ? (isStaticChildren = \"array\")\n\t            : void 0 !== type && type.$$typeof === REACT_ELEMENT_TYPE\n\t              ? ((isStaticChildren =\n\t                  \"<\" +\n\t                  (getComponentNameFromType(type.type) || \"Unknown\") +\n\t                  \" />\"),\n\t                (children =\n\t                  \" Did you accidentally export a JSX literal instead of a component?\"))\n\t              : (isStaticChildren = typeof type);\n\t        console.error(\n\t          \"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\",\n\t          isStaticChildren,\n\t          children\n\t        );\n\t      }\n\t      if (hasOwnProperty.call(config, \"key\")) {\n\t        children = getComponentNameFromType(type);\n\t        var keys = Object.keys(config).filter(function (k) {\n\t          return \"key\" !== k;\n\t        });\n\t        isStaticChildren =\n\t          0 < keys.length\n\t            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n\t            : \"{key: someKey}\";\n\t        didWarnAboutKeySpread[children + isStaticChildren] ||\n\t          ((keys =\n\t            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n\t          console.error(\n\t            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n\t            isStaticChildren,\n\t            children,\n\t            keys,\n\t            children\n\t          ),\n\t          (didWarnAboutKeySpread[children + isStaticChildren] = true));\n\t      }\n\t      children = null;\n\t      void 0 !== maybeKey &&\n\t        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n\t      hasValidKey(config) &&\n\t        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n\t      if (\"key\" in config) {\n\t        maybeKey = {};\n\t        for (var propName in config)\n\t          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n\t      } else maybeKey = config;\n\t      children &&\n\t        defineKeyPropWarningGetter(\n\t          maybeKey,\n\t          \"function\" === typeof type\n\t            ? type.displayName || type.name || \"Unknown\"\n\t            : type\n\t        );\n\t      return ReactElement(type, children, self, source, getOwner(), maybeKey);\n\t    }\n\t    function validateChildKeys(node, parentType) {\n\t      if (\n\t        \"object\" === typeof node &&\n\t        node &&\n\t        node.$$typeof !== REACT_CLIENT_REFERENCE\n\t      )\n\t        if (isArrayImpl(node))\n\t          for (var i = 0; i < node.length; i++) {\n\t            var child = node[i];\n\t            isValidElement(child) && validateExplicitKey(child, parentType);\n\t          }\n\t        else if (isValidElement(node))\n\t          node._store && (node._store.validated = 1);\n\t        else if (\n\t          (null === node || \"object\" !== typeof node\n\t            ? (i = null)\n\t            : ((i =\n\t                (MAYBE_ITERATOR_SYMBOL && node[MAYBE_ITERATOR_SYMBOL]) ||\n\t                node[\"@@iterator\"]),\n\t              (i = \"function\" === typeof i ? i : null)),\n\t          \"function\" === typeof i &&\n\t            i !== node.entries &&\n\t            ((i = i.call(node)), i !== node))\n\t        )\n\t          for (; !(node = i.next()).done; )\n\t            isValidElement(node.value) &&\n\t              validateExplicitKey(node.value, parentType);\n\t    }\n\t    function isValidElement(object) {\n\t      return (\n\t        \"object\" === typeof object &&\n\t        null !== object &&\n\t        object.$$typeof === REACT_ELEMENT_TYPE\n\t      );\n\t    }\n\t    function validateExplicitKey(element, parentType) {\n\t      if (\n\t        element._store &&\n\t        !element._store.validated &&\n\t        null == element.key &&\n\t        ((element._store.validated = 1),\n\t        (parentType = getCurrentComponentErrorInfo(parentType)),\n\t        !ownerHasKeyUseWarning[parentType])\n\t      ) {\n\t        ownerHasKeyUseWarning[parentType] = true;\n\t        var childOwner = \"\";\n\t        element &&\n\t          null != element._owner &&\n\t          element._owner !== getOwner() &&\n\t          ((childOwner = null),\n\t          \"number\" === typeof element._owner.tag\n\t            ? (childOwner = getComponentNameFromType(element._owner.type))\n\t            : \"string\" === typeof element._owner.name &&\n\t              (childOwner = element._owner.name),\n\t          (childOwner = \" It was passed a child from \" + childOwner + \".\"));\n\t        var prevGetCurrentStack = ReactSharedInternals.getCurrentStack;\n\t        ReactSharedInternals.getCurrentStack = function () {\n\t          var stack = describeUnknownElementTypeFrameInDEV(element.type);\n\t          prevGetCurrentStack && (stack += prevGetCurrentStack() || \"\");\n\t          return stack;\n\t        };\n\t        console.error(\n\t          'Each child in a list should have a unique \"key\" prop.%s%s See https://react.dev/link/warning-keys for more information.',\n\t          parentType,\n\t          childOwner\n\t        );\n\t        ReactSharedInternals.getCurrentStack = prevGetCurrentStack;\n\t      }\n\t    }\n\t    function getCurrentComponentErrorInfo(parentType) {\n\t      var info = \"\",\n\t        owner = getOwner();\n\t      owner &&\n\t        (owner = getComponentNameFromType(owner.type)) &&\n\t        (info = \"\\n\\nCheck the render method of `\" + owner + \"`.\");\n\t      info ||\n\t        ((parentType = getComponentNameFromType(parentType)) &&\n\t          (info =\n\t            \"\\n\\nCheck the top-level render call using <\" + parentType + \">.\"));\n\t      return info;\n\t    }\n\t    var React$1 = React,\n\t      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n\t      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n\t      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n\t      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n\t      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n\t    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n\t      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n\t      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n\t      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n\t      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n\t      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n\t      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n\t      REACT_OFFSCREEN_TYPE = Symbol.for(\"react.offscreen\"),\n\t      MAYBE_ITERATOR_SYMBOL = Symbol.iterator,\n\t      REACT_CLIENT_REFERENCE$2 = Symbol.for(\"react.client.reference\"),\n\t      ReactSharedInternals =\n\t        React$1.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n\t      hasOwnProperty = Object.prototype.hasOwnProperty,\n\t      assign = Object.assign,\n\t      REACT_CLIENT_REFERENCE$1 = Symbol.for(\"react.client.reference\"),\n\t      isArrayImpl = Array.isArray,\n\t      disabledDepth = 0,\n\t      prevLog,\n\t      prevInfo,\n\t      prevWarn,\n\t      prevError,\n\t      prevGroup,\n\t      prevGroupCollapsed,\n\t      prevGroupEnd;\n\t    disabledLog.__reactDisabledLog = true;\n\t    var prefix,\n\t      suffix,\n\t      reentry = false;\n\t    var componentFrameCache = new (\n\t      \"function\" === typeof WeakMap ? WeakMap : Map\n\t    )();\n\t    var REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n\t      specialPropKeyWarningShown;\n\t    var didWarnAboutElementRef = {};\n\t    var didWarnAboutKeySpread = {},\n\t      ownerHasKeyUseWarning = {};\n\t    reactJsxRuntime_development.Fragment = REACT_FRAGMENT_TYPE;\n\t    reactJsxRuntime_development.jsx = function (type, config, maybeKey, source, self) {\n\t      return jsxDEVImpl(type, config, maybeKey, false, source, self);\n\t    };\n\t    reactJsxRuntime_development.jsxs = function (type, config, maybeKey, source, self) {\n\t      return jsxDEVImpl(type, config, maybeKey, true, source, self);\n\t    };\n\t  })();\n\treturn reactJsxRuntime_development;\n}\n\nvar hasRequiredJsxRuntime;\n\nfunction requireJsxRuntime () {\n\tif (hasRequiredJsxRuntime) return jsxRuntime.exports;\n\thasRequiredJsxRuntime = 1;\n\n\tif (process.env.NODE_ENV === 'production') {\n\t  jsxRuntime.exports = requireReactJsxRuntime_production();\n\t} else {\n\t  jsxRuntime.exports = requireReactJsxRuntime_development();\n\t}\n\treturn jsxRuntime.exports;\n}\n\nvar jsxRuntimeExports = requireJsxRuntime();\n\n// https://github.com/facebook/react/blob/master/packages/shared/ExecutionEnvironment.js\nconst canUseDOM = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined';\n\nfunction isWindow(element) {\n  const elementString = Object.prototype.toString.call(element);\n  return elementString === '[object Window]' || // In Electron context the Window object serializes to [object global]\n  elementString === '[object global]';\n}\n\nfunction isNode(node) {\n  return 'nodeType' in node;\n}\n\nfunction getWindow(target) {\n  var _target$ownerDocument, _target$ownerDocument2;\n\n  if (!target) {\n    return window;\n  }\n\n  if (isWindow(target)) {\n    return target;\n  }\n\n  if (!isNode(target)) {\n    return window;\n  }\n\n  return (_target$ownerDocument = (_target$ownerDocument2 = target.ownerDocument) == null ? void 0 : _target$ownerDocument2.defaultView) != null ? _target$ownerDocument : window;\n}\n\nfunction isDocument(node) {\n  const {\n    Document\n  } = getWindow(node);\n  return node instanceof Document;\n}\n\nfunction isHTMLElement(node) {\n  if (isWindow(node)) {\n    return false;\n  }\n\n  return node instanceof getWindow(node).HTMLElement;\n}\n\nfunction isSVGElement(node) {\n  return node instanceof getWindow(node).SVGElement;\n}\n\nfunction getOwnerDocument(target) {\n  if (!target) {\n    return document;\n  }\n\n  if (isWindow(target)) {\n    return target.document;\n  }\n\n  if (!isNode(target)) {\n    return document;\n  }\n\n  if (isDocument(target)) {\n    return target;\n  }\n\n  if (isHTMLElement(target) || isSVGElement(target)) {\n    return target.ownerDocument;\n  }\n\n  return document;\n}\n\n/**\r\n * A hook that resolves to useEffect on the server and useLayoutEffect on the client\r\n * @param callback {function} Callback function that is invoked when the dependencies of the hook change\r\n */\n\nconst useIsomorphicLayoutEffect = canUseDOM ? useLayoutEffect : useEffect;\n\nfunction useEvent(handler) {\n  const handlerRef = useRef(handler);\n  useIsomorphicLayoutEffect(() => {\n    handlerRef.current = handler;\n  });\n  return useCallback(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    return handlerRef.current == null ? void 0 : handlerRef.current(...args);\n  }, []);\n}\n\nfunction useInterval() {\n  const intervalRef = useRef(null);\n  const set = useCallback((listener, duration) => {\n    intervalRef.current = setInterval(listener, duration);\n  }, []);\n  const clear = useCallback(() => {\n    if (intervalRef.current !== null) {\n      clearInterval(intervalRef.current);\n      intervalRef.current = null;\n    }\n  }, []);\n  return [set, clear];\n}\n\nfunction useLatestValue(value, dependencies) {\n  if (dependencies === void 0) {\n    dependencies = [value];\n  }\n\n  const valueRef = useRef(value);\n  useIsomorphicLayoutEffect(() => {\n    if (valueRef.current !== value) {\n      valueRef.current = value;\n    }\n  }, dependencies);\n  return valueRef;\n}\n\nfunction useLazyMemo(callback, dependencies) {\n  const valueRef = useRef();\n  return useMemo(() => {\n    const newValue = callback(valueRef.current);\n    valueRef.current = newValue;\n    return newValue;\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [...dependencies]);\n}\n\nfunction useNodeRef(onChange) {\n  const onChangeHandler = useEvent(onChange);\n  const node = useRef(null);\n  const setNodeRef = useCallback(element => {\n    if (element !== node.current) {\n      onChangeHandler == null ? void 0 : onChangeHandler(element, node.current);\n    }\n\n    node.current = element;\n  }, //eslint-disable-next-line\n  []);\n  return [node, setNodeRef];\n}\n\nfunction usePrevious(value) {\n  const ref = useRef();\n  useEffect(() => {\n    ref.current = value;\n  }, [value]);\n  return ref.current;\n}\n\nlet ids = {};\nfunction useUniqueId(prefix, value) {\n  return useMemo(() => {\n    if (value) {\n      return value;\n    }\n\n    const id = ids[prefix] == null ? 0 : ids[prefix] + 1;\n    ids[prefix] = id;\n    return prefix + \"-\" + id;\n  }, [prefix, value]);\n}\n\nfunction createAdjustmentFn(modifier) {\n  return function (object) {\n    for (var _len = arguments.length, adjustments = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      adjustments[_key - 1] = arguments[_key];\n    }\n\n    return adjustments.reduce((accumulator, adjustment) => {\n      const entries = Object.entries(adjustment);\n\n      for (const [key, valueAdjustment] of entries) {\n        const value = accumulator[key];\n\n        if (value != null) {\n          accumulator[key] = value + modifier * valueAdjustment;\n        }\n      }\n\n      return accumulator;\n    }, { ...object\n    });\n  };\n}\n\nconst add = /*#__PURE__*/createAdjustmentFn(1);\nconst subtract = /*#__PURE__*/createAdjustmentFn(-1);\n\nfunction hasViewportRelativeCoordinates(event) {\n  return 'clientX' in event && 'clientY' in event;\n}\n\nfunction isKeyboardEvent(event) {\n  if (!event) {\n    return false;\n  }\n\n  const {\n    KeyboardEvent\n  } = getWindow(event.target);\n  return KeyboardEvent && event instanceof KeyboardEvent;\n}\n\nfunction isTouchEvent(event) {\n  if (!event) {\n    return false;\n  }\n\n  const {\n    TouchEvent\n  } = getWindow(event.target);\n  return TouchEvent && event instanceof TouchEvent;\n}\n\n/**\r\n * Returns the normalized x and y coordinates for mouse and touch events.\r\n */\n\nfunction getEventCoordinates(event) {\n  if (isTouchEvent(event)) {\n    if (event.touches && event.touches.length) {\n      const {\n        clientX: x,\n        clientY: y\n      } = event.touches[0];\n      return {\n        x,\n        y\n      };\n    } else if (event.changedTouches && event.changedTouches.length) {\n      const {\n        clientX: x,\n        clientY: y\n      } = event.changedTouches[0];\n      return {\n        x,\n        y\n      };\n    }\n  }\n\n  if (hasViewportRelativeCoordinates(event)) {\n    return {\n      x: event.clientX,\n      y: event.clientY\n    };\n  }\n\n  return null;\n}\n\nconst CSS = /*#__PURE__*/Object.freeze({\n  Translate: {\n    toString(transform) {\n      if (!transform) {\n        return;\n      }\n\n      const {\n        x,\n        y\n      } = transform;\n      return \"translate3d(\" + (x ? Math.round(x) : 0) + \"px, \" + (y ? Math.round(y) : 0) + \"px, 0)\";\n    }\n\n  },\n  Scale: {\n    toString(transform) {\n      if (!transform) {\n        return;\n      }\n\n      const {\n        scaleX,\n        scaleY\n      } = transform;\n      return \"scaleX(\" + scaleX + \") scaleY(\" + scaleY + \")\";\n    }\n\n  },\n  Transform: {\n    toString(transform) {\n      if (!transform) {\n        return;\n      }\n\n      return [CSS.Translate.toString(transform), CSS.Scale.toString(transform)].join(' ');\n    }\n\n  },\n  Transition: {\n    toString(_ref) {\n      let {\n        property,\n        duration,\n        easing\n      } = _ref;\n      return property + \" \" + duration + \"ms \" + easing;\n    }\n\n  }\n});\n\nconst SELECTOR = 'a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]';\nfunction findFirstFocusableNode(element) {\n  if (element.matches(SELECTOR)) {\n    return element;\n  }\n\n  return element.querySelector(SELECTOR);\n}\n\nconst hiddenStyles = {\n  display: 'none'\n};\nfunction HiddenText(_ref) {\n  let {\n    id,\n    value\n  } = _ref;\n  return React.createElement(\"div\", {\n    id: id,\n    style: hiddenStyles\n  }, value);\n}\n\nfunction LiveRegion(_ref) {\n  let {\n    id,\n    announcement,\n    ariaLiveType = \"assertive\"\n  } = _ref;\n  // Hide element visually but keep it readable by screen readers\n  const visuallyHidden = {\n    position: 'fixed',\n    top: 0,\n    left: 0,\n    width: 1,\n    height: 1,\n    margin: -1,\n    border: 0,\n    padding: 0,\n    overflow: 'hidden',\n    clip: 'rect(0 0 0 0)',\n    clipPath: 'inset(100%)',\n    whiteSpace: 'nowrap'\n  };\n  return React.createElement(\"div\", {\n    id: id,\n    style: visuallyHidden,\n    role: \"status\",\n    \"aria-live\": ariaLiveType,\n    \"aria-atomic\": true\n  }, announcement);\n}\n\nfunction useAnnouncement() {\n  const [announcement, setAnnouncement] = useState('');\n  const announce = useCallback(value => {\n    if (value != null) {\n      setAnnouncement(value);\n    }\n  }, []);\n  return {\n    announce,\n    announcement\n  };\n}\n\nconst DndMonitorContext = /*#__PURE__*/createContext(null);\n\nfunction useDndMonitor(listener) {\n  const registerListener = useContext(DndMonitorContext);\n  useEffect(() => {\n    if (!registerListener) {\n      throw new Error('useDndMonitor must be used within a children of <DndContext>');\n    }\n\n    const unsubscribe = registerListener(listener);\n    return unsubscribe;\n  }, [listener, registerListener]);\n}\n\nfunction useDndMonitorProvider() {\n  const [listeners] = useState(() => new Set());\n  const registerListener = useCallback(listener => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  }, [listeners]);\n  const dispatch = useCallback(_ref => {\n    let {\n      type,\n      event\n    } = _ref;\n    listeners.forEach(listener => {\n      var _listener$type;\n\n      return (_listener$type = listener[type]) == null ? void 0 : _listener$type.call(listener, event);\n    });\n  }, [listeners]);\n  return [dispatch, registerListener];\n}\n\nconst defaultScreenReaderInstructions = {\n  draggable: \"\\n    To pick up a draggable item, press the space bar.\\n    While dragging, use the arrow keys to move the item.\\n    Press space again to drop the item in its new position, or press escape to cancel.\\n  \"\n};\nconst defaultAnnouncements = {\n  onDragStart(_ref) {\n    let {\n      active\n    } = _ref;\n    return \"Picked up draggable item \" + active.id + \".\";\n  },\n\n  onDragOver(_ref2) {\n    let {\n      active,\n      over\n    } = _ref2;\n\n    if (over) {\n      return \"Draggable item \" + active.id + \" was moved over droppable area \" + over.id + \".\";\n    }\n\n    return \"Draggable item \" + active.id + \" is no longer over a droppable area.\";\n  },\n\n  onDragEnd(_ref3) {\n    let {\n      active,\n      over\n    } = _ref3;\n\n    if (over) {\n      return \"Draggable item \" + active.id + \" was dropped over droppable area \" + over.id;\n    }\n\n    return \"Draggable item \" + active.id + \" was dropped.\";\n  },\n\n  onDragCancel(_ref4) {\n    let {\n      active\n    } = _ref4;\n    return \"Dragging was cancelled. Draggable item \" + active.id + \" was dropped.\";\n  }\n\n};\n\nfunction Accessibility(_ref) {\n  let {\n    announcements = defaultAnnouncements,\n    container,\n    hiddenTextDescribedById,\n    screenReaderInstructions = defaultScreenReaderInstructions\n  } = _ref;\n  const {\n    announce,\n    announcement\n  } = useAnnouncement();\n  const liveRegionId = useUniqueId(\"DndLiveRegion\");\n  const [mounted, setMounted] = useState(false);\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n  useDndMonitor(useMemo(() => ({\n    onDragStart(_ref2) {\n      let {\n        active\n      } = _ref2;\n      announce(announcements.onDragStart({\n        active\n      }));\n    },\n\n    onDragMove(_ref3) {\n      let {\n        active,\n        over\n      } = _ref3;\n\n      if (announcements.onDragMove) {\n        announce(announcements.onDragMove({\n          active,\n          over\n        }));\n      }\n    },\n\n    onDragOver(_ref4) {\n      let {\n        active,\n        over\n      } = _ref4;\n      announce(announcements.onDragOver({\n        active,\n        over\n      }));\n    },\n\n    onDragEnd(_ref5) {\n      let {\n        active,\n        over\n      } = _ref5;\n      announce(announcements.onDragEnd({\n        active,\n        over\n      }));\n    },\n\n    onDragCancel(_ref6) {\n      let {\n        active,\n        over\n      } = _ref6;\n      announce(announcements.onDragCancel({\n        active,\n        over\n      }));\n    }\n\n  }), [announce, announcements]));\n\n  if (!mounted) {\n    return null;\n  }\n\n  const markup = React.createElement(React.Fragment, null, React.createElement(HiddenText, {\n    id: hiddenTextDescribedById,\n    value: screenReaderInstructions.draggable\n  }), React.createElement(LiveRegion, {\n    id: liveRegionId,\n    announcement: announcement\n  }));\n  return container ? createPortal(markup, container) : markup;\n}\n\nvar Action;\n\n(function (Action) {\n  Action[\"DragStart\"] = \"dragStart\";\n  Action[\"DragMove\"] = \"dragMove\";\n  Action[\"DragEnd\"] = \"dragEnd\";\n  Action[\"DragCancel\"] = \"dragCancel\";\n  Action[\"DragOver\"] = \"dragOver\";\n  Action[\"RegisterDroppable\"] = \"registerDroppable\";\n  Action[\"SetDroppableDisabled\"] = \"setDroppableDisabled\";\n  Action[\"UnregisterDroppable\"] = \"unregisterDroppable\";\n})(Action || (Action = {}));\n\nfunction noop() {}\n\nfunction useSensor(sensor, options) {\n  return useMemo(() => ({\n    sensor,\n    options: options != null ? options : {}\n  }), // eslint-disable-next-line react-hooks/exhaustive-deps\n  [sensor, options]);\n}\n\nfunction useSensors() {\n  for (var _len = arguments.length, sensors = new Array(_len), _key = 0; _key < _len; _key++) {\n    sensors[_key] = arguments[_key];\n  }\n\n  return useMemo(() => [...sensors].filter(sensor => sensor != null), // eslint-disable-next-line react-hooks/exhaustive-deps\n  [...sensors]);\n}\n\nconst defaultCoordinates = /*#__PURE__*/Object.freeze({\n  x: 0,\n  y: 0\n});\n\n/**\r\n * Returns the distance between two points\r\n */\nfunction distanceBetween(p1, p2) {\n  return Math.sqrt(Math.pow(p1.x - p2.x, 2) + Math.pow(p1.y - p2.y, 2));\n}\n\nfunction getRelativeTransformOrigin(event, rect) {\n  const eventCoordinates = getEventCoordinates(event);\n\n  if (!eventCoordinates) {\n    return '0 0';\n  }\n\n  const transformOrigin = {\n    x: (eventCoordinates.x - rect.left) / rect.width * 100,\n    y: (eventCoordinates.y - rect.top) / rect.height * 100\n  };\n  return transformOrigin.x + \"% \" + transformOrigin.y + \"%\";\n}\n\n/**\r\n * Sort collisions from smallest to greatest value\r\n */\nfunction sortCollisionsAsc(_ref, _ref2) {\n  let {\n    data: {\n      value: a\n    }\n  } = _ref;\n  let {\n    data: {\n      value: b\n    }\n  } = _ref2;\n  return a - b;\n}\n/**\r\n * Sort collisions from greatest to smallest value\r\n */\n\nfunction sortCollisionsDesc(_ref3, _ref4) {\n  let {\n    data: {\n      value: a\n    }\n  } = _ref3;\n  let {\n    data: {\n      value: b\n    }\n  } = _ref4;\n  return b - a;\n}\n/**\r\n * Returns the coordinates of the corners of a given rectangle:\r\n * [TopLeft {x, y}, TopRight {x, y}, BottomLeft {x, y}, BottomRight {x, y}]\r\n */\n\nfunction cornersOfRectangle(_ref5) {\n  let {\n    left,\n    top,\n    height,\n    width\n  } = _ref5;\n  return [{\n    x: left,\n    y: top\n  }, {\n    x: left + width,\n    y: top\n  }, {\n    x: left,\n    y: top + height\n  }, {\n    x: left + width,\n    y: top + height\n  }];\n}\nfunction getFirstCollision(collisions, property) {\n  if (!collisions || collisions.length === 0) {\n    return null;\n  }\n\n  const [firstCollision] = collisions;\n  return firstCollision[property] ;\n}\n\n/**\r\n * Returns the intersecting rectangle area between two rectangles\r\n */\n\nfunction getIntersectionRatio(entry, target) {\n  const top = Math.max(target.top, entry.top);\n  const left = Math.max(target.left, entry.left);\n  const right = Math.min(target.left + target.width, entry.left + entry.width);\n  const bottom = Math.min(target.top + target.height, entry.top + entry.height);\n  const width = right - left;\n  const height = bottom - top;\n\n  if (left < right && top < bottom) {\n    const targetArea = target.width * target.height;\n    const entryArea = entry.width * entry.height;\n    const intersectionArea = width * height;\n    const intersectionRatio = intersectionArea / (targetArea + entryArea - intersectionArea);\n    return Number(intersectionRatio.toFixed(4));\n  } // Rectangles do not overlap, or overlap has an area of zero (edge/corner overlap)\n\n\n  return 0;\n}\n/**\r\n * Returns the rectangles that has the greatest intersection area with a given\r\n * rectangle in an array of rectangles.\r\n */\n\nconst rectIntersection = _ref => {\n  let {\n    collisionRect,\n    droppableRects,\n    droppableContainers\n  } = _ref;\n  const collisions = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {\n      id\n    } = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const intersectionRatio = getIntersectionRatio(rect, collisionRect);\n\n      if (intersectionRatio > 0) {\n        collisions.push({\n          id,\n          data: {\n            droppableContainer,\n            value: intersectionRatio\n          }\n        });\n      }\n    }\n  }\n\n  return collisions.sort(sortCollisionsDesc);\n};\n\n/**\r\n * Check if a given point is contained within a bounding rectangle\r\n */\n\nfunction isPointWithinRect(point, rect) {\n  const {\n    top,\n    left,\n    bottom,\n    right\n  } = rect;\n  return top <= point.y && point.y <= bottom && left <= point.x && point.x <= right;\n}\n/**\r\n * Returns the rectangles that the pointer is hovering over\r\n */\n\n\nconst pointerWithin = _ref => {\n  let {\n    droppableContainers,\n    droppableRects,\n    pointerCoordinates\n  } = _ref;\n\n  if (!pointerCoordinates) {\n    return [];\n  }\n\n  const collisions = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {\n      id\n    } = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect && isPointWithinRect(pointerCoordinates, rect)) {\n      /* There may be more than a single rectangle intersecting\r\n       * with the pointer coordinates. In order to sort the\r\n       * colliding rectangles, we measure the distance between\r\n       * the pointer and the corners of the intersecting rectangle\r\n       */\n      const corners = cornersOfRectangle(rect);\n      const distances = corners.reduce((accumulator, corner) => {\n        return accumulator + distanceBetween(pointerCoordinates, corner);\n      }, 0);\n      const effectiveDistance = Number((distances / 4).toFixed(4));\n      collisions.push({\n        id,\n        data: {\n          droppableContainer,\n          value: effectiveDistance\n        }\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n\nfunction adjustScale(transform, rect1, rect2) {\n  return { ...transform,\n    scaleX: rect1 && rect2 ? rect1.width / rect2.width : 1,\n    scaleY: rect1 && rect2 ? rect1.height / rect2.height : 1\n  };\n}\n\nfunction getRectDelta(rect1, rect2) {\n  return rect1 && rect2 ? {\n    x: rect1.left - rect2.left,\n    y: rect1.top - rect2.top\n  } : defaultCoordinates;\n}\n\nfunction createRectAdjustmentFn(modifier) {\n  return function adjustClientRect(rect) {\n    for (var _len = arguments.length, adjustments = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      adjustments[_key - 1] = arguments[_key];\n    }\n\n    return adjustments.reduce((acc, adjustment) => ({ ...acc,\n      top: acc.top + modifier * adjustment.y,\n      bottom: acc.bottom + modifier * adjustment.y,\n      left: acc.left + modifier * adjustment.x,\n      right: acc.right + modifier * adjustment.x\n    }), { ...rect\n    });\n  };\n}\nconst getAdjustedRect = /*#__PURE__*/createRectAdjustmentFn(1);\n\nfunction parseTransform(transform) {\n  if (transform.startsWith('matrix3d(')) {\n    const transformArray = transform.slice(9, -1).split(/, /);\n    return {\n      x: +transformArray[12],\n      y: +transformArray[13],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[5]\n    };\n  } else if (transform.startsWith('matrix(')) {\n    const transformArray = transform.slice(7, -1).split(/, /);\n    return {\n      x: +transformArray[4],\n      y: +transformArray[5],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[3]\n    };\n  }\n\n  return null;\n}\n\nfunction inverseTransform(rect, transform, transformOrigin) {\n  const parsedTransform = parseTransform(transform);\n\n  if (!parsedTransform) {\n    return rect;\n  }\n\n  const {\n    scaleX,\n    scaleY,\n    x: translateX,\n    y: translateY\n  } = parsedTransform;\n  const x = rect.left - translateX - (1 - scaleX) * parseFloat(transformOrigin);\n  const y = rect.top - translateY - (1 - scaleY) * parseFloat(transformOrigin.slice(transformOrigin.indexOf(' ') + 1));\n  const w = scaleX ? rect.width / scaleX : rect.width;\n  const h = scaleY ? rect.height / scaleY : rect.height;\n  return {\n    width: w,\n    height: h,\n    top: y,\n    right: x + w,\n    bottom: y + h,\n    left: x\n  };\n}\n\nconst defaultOptions = {\n  ignoreTransform: false\n};\n/**\r\n * Returns the bounding client rect of an element relative to the viewport.\r\n */\n\nfunction getClientRect(element, options) {\n  if (options === void 0) {\n    options = defaultOptions;\n  }\n\n  let rect = element.getBoundingClientRect();\n\n  if (options.ignoreTransform) {\n    const {\n      transform,\n      transformOrigin\n    } = getWindow(element).getComputedStyle(element);\n\n    if (transform) {\n      rect = inverseTransform(rect, transform, transformOrigin);\n    }\n  }\n\n  const {\n    top,\n    left,\n    width,\n    height,\n    bottom,\n    right\n  } = rect;\n  return {\n    top,\n    left,\n    width,\n    height,\n    bottom,\n    right\n  };\n}\n/**\r\n * Returns the bounding client rect of an element relative to the viewport.\r\n *\r\n * @remarks\r\n * The ClientRect returned by this method does not take into account transforms\r\n * applied to the element it measures.\r\n *\r\n */\n\nfunction getTransformAgnosticClientRect(element) {\n  return getClientRect(element, {\n    ignoreTransform: true\n  });\n}\n\nfunction getWindowClientRect(element) {\n  const width = element.innerWidth;\n  const height = element.innerHeight;\n  return {\n    top: 0,\n    left: 0,\n    right: width,\n    bottom: height,\n    width,\n    height\n  };\n}\n\nfunction isFixed(node, computedStyle) {\n  if (computedStyle === void 0) {\n    computedStyle = getWindow(node).getComputedStyle(node);\n  }\n\n  return computedStyle.position === 'fixed';\n}\n\nfunction isScrollable(element, computedStyle) {\n  if (computedStyle === void 0) {\n    computedStyle = getWindow(element).getComputedStyle(element);\n  }\n\n  const overflowRegex = /(auto|scroll|overlay)/;\n  const properties = ['overflow', 'overflowX', 'overflowY'];\n  return properties.some(property => {\n    const value = computedStyle[property];\n    return typeof value === 'string' ? overflowRegex.test(value) : false;\n  });\n}\n\nfunction getScrollableAncestors(element, limit) {\n  const scrollParents = [];\n\n  function findScrollableAncestors(node) {\n    if (limit != null && scrollParents.length >= limit) {\n      return scrollParents;\n    }\n\n    if (!node) {\n      return scrollParents;\n    }\n\n    if (isDocument(node) && node.scrollingElement != null && !scrollParents.includes(node.scrollingElement)) {\n      scrollParents.push(node.scrollingElement);\n      return scrollParents;\n    }\n\n    if (!isHTMLElement(node) || isSVGElement(node)) {\n      return scrollParents;\n    }\n\n    if (scrollParents.includes(node)) {\n      return scrollParents;\n    }\n\n    const computedStyle = getWindow(element).getComputedStyle(node);\n\n    if (node !== element) {\n      if (isScrollable(node, computedStyle)) {\n        scrollParents.push(node);\n      }\n    }\n\n    if (isFixed(node, computedStyle)) {\n      return scrollParents;\n    }\n\n    return findScrollableAncestors(node.parentNode);\n  }\n\n  if (!element) {\n    return scrollParents;\n  }\n\n  return findScrollableAncestors(element);\n}\nfunction getFirstScrollableAncestor(node) {\n  const [firstScrollableAncestor] = getScrollableAncestors(node, 1);\n  return firstScrollableAncestor != null ? firstScrollableAncestor : null;\n}\n\nfunction getScrollableElement(element) {\n  if (!canUseDOM || !element) {\n    return null;\n  }\n\n  if (isWindow(element)) {\n    return element;\n  }\n\n  if (!isNode(element)) {\n    return null;\n  }\n\n  if (isDocument(element) || element === getOwnerDocument(element).scrollingElement) {\n    return window;\n  }\n\n  if (isHTMLElement(element)) {\n    return element;\n  }\n\n  return null;\n}\n\nfunction getScrollXCoordinate(element) {\n  if (isWindow(element)) {\n    return element.scrollX;\n  }\n\n  return element.scrollLeft;\n}\nfunction getScrollYCoordinate(element) {\n  if (isWindow(element)) {\n    return element.scrollY;\n  }\n\n  return element.scrollTop;\n}\nfunction getScrollCoordinates(element) {\n  return {\n    x: getScrollXCoordinate(element),\n    y: getScrollYCoordinate(element)\n  };\n}\n\nvar Direction;\n\n(function (Direction) {\n  Direction[Direction[\"Forward\"] = 1] = \"Forward\";\n  Direction[Direction[\"Backward\"] = -1] = \"Backward\";\n})(Direction || (Direction = {}));\n\nfunction isDocumentScrollingElement(element) {\n  if (!canUseDOM || !element) {\n    return false;\n  }\n\n  return element === document.scrollingElement;\n}\n\nfunction getScrollPosition(scrollingContainer) {\n  const minScroll = {\n    x: 0,\n    y: 0\n  };\n  const dimensions = isDocumentScrollingElement(scrollingContainer) ? {\n    height: window.innerHeight,\n    width: window.innerWidth\n  } : {\n    height: scrollingContainer.clientHeight,\n    width: scrollingContainer.clientWidth\n  };\n  const maxScroll = {\n    x: scrollingContainer.scrollWidth - dimensions.width,\n    y: scrollingContainer.scrollHeight - dimensions.height\n  };\n  const isTop = scrollingContainer.scrollTop <= minScroll.y;\n  const isLeft = scrollingContainer.scrollLeft <= minScroll.x;\n  const isBottom = scrollingContainer.scrollTop >= maxScroll.y;\n  const isRight = scrollingContainer.scrollLeft >= maxScroll.x;\n  return {\n    isTop,\n    isLeft,\n    isBottom,\n    isRight,\n    maxScroll,\n    minScroll\n  };\n}\n\nconst defaultThreshold = {\n  x: 0.2,\n  y: 0.2\n};\nfunction getScrollDirectionAndSpeed(scrollContainer, scrollContainerRect, _ref, acceleration, thresholdPercentage) {\n  let {\n    top,\n    left,\n    right,\n    bottom\n  } = _ref;\n\n  if (acceleration === void 0) {\n    acceleration = 10;\n  }\n\n  if (thresholdPercentage === void 0) {\n    thresholdPercentage = defaultThreshold;\n  }\n\n  const {\n    isTop,\n    isBottom,\n    isLeft,\n    isRight\n  } = getScrollPosition(scrollContainer);\n  const direction = {\n    x: 0,\n    y: 0\n  };\n  const speed = {\n    x: 0,\n    y: 0\n  };\n  const threshold = {\n    height: scrollContainerRect.height * thresholdPercentage.y,\n    width: scrollContainerRect.width * thresholdPercentage.x\n  };\n\n  if (!isTop && top <= scrollContainerRect.top + threshold.height) {\n    // Scroll Up\n    direction.y = Direction.Backward;\n    speed.y = acceleration * Math.abs((scrollContainerRect.top + threshold.height - top) / threshold.height);\n  } else if (!isBottom && bottom >= scrollContainerRect.bottom - threshold.height) {\n    // Scroll Down\n    direction.y = Direction.Forward;\n    speed.y = acceleration * Math.abs((scrollContainerRect.bottom - threshold.height - bottom) / threshold.height);\n  }\n\n  if (!isRight && right >= scrollContainerRect.right - threshold.width) {\n    // Scroll Right\n    direction.x = Direction.Forward;\n    speed.x = acceleration * Math.abs((scrollContainerRect.right - threshold.width - right) / threshold.width);\n  } else if (!isLeft && left <= scrollContainerRect.left + threshold.width) {\n    // Scroll Left\n    direction.x = Direction.Backward;\n    speed.x = acceleration * Math.abs((scrollContainerRect.left + threshold.width - left) / threshold.width);\n  }\n\n  return {\n    direction,\n    speed\n  };\n}\n\nfunction getScrollElementRect(element) {\n  if (element === document.scrollingElement) {\n    const {\n      innerWidth,\n      innerHeight\n    } = window;\n    return {\n      top: 0,\n      left: 0,\n      right: innerWidth,\n      bottom: innerHeight,\n      width: innerWidth,\n      height: innerHeight\n    };\n  }\n\n  const {\n    top,\n    left,\n    right,\n    bottom\n  } = element.getBoundingClientRect();\n  return {\n    top,\n    left,\n    right,\n    bottom,\n    width: element.clientWidth,\n    height: element.clientHeight\n  };\n}\n\nfunction getScrollOffsets(scrollableAncestors) {\n  return scrollableAncestors.reduce((acc, node) => {\n    return add(acc, getScrollCoordinates(node));\n  }, defaultCoordinates);\n}\nfunction getScrollXOffset(scrollableAncestors) {\n  return scrollableAncestors.reduce((acc, node) => {\n    return acc + getScrollXCoordinate(node);\n  }, 0);\n}\nfunction getScrollYOffset(scrollableAncestors) {\n  return scrollableAncestors.reduce((acc, node) => {\n    return acc + getScrollYCoordinate(node);\n  }, 0);\n}\n\nfunction scrollIntoViewIfNeeded(element, measure) {\n  if (measure === void 0) {\n    measure = getClientRect;\n  }\n\n  if (!element) {\n    return;\n  }\n\n  const {\n    top,\n    left,\n    bottom,\n    right\n  } = measure(element);\n  const firstScrollableAncestor = getFirstScrollableAncestor(element);\n\n  if (!firstScrollableAncestor) {\n    return;\n  }\n\n  if (bottom <= 0 || right <= 0 || top >= window.innerHeight || left >= window.innerWidth) {\n    element.scrollIntoView({\n      block: 'center',\n      inline: 'center'\n    });\n  }\n}\n\nconst properties = [['x', ['left', 'right'], getScrollXOffset], ['y', ['top', 'bottom'], getScrollYOffset]];\nclass Rect {\n  constructor(rect, element) {\n    this.rect = void 0;\n    this.width = void 0;\n    this.height = void 0;\n    this.top = void 0;\n    this.bottom = void 0;\n    this.right = void 0;\n    this.left = void 0;\n    const scrollableAncestors = getScrollableAncestors(element);\n    const scrollOffsets = getScrollOffsets(scrollableAncestors);\n    this.rect = { ...rect\n    };\n    this.width = rect.width;\n    this.height = rect.height;\n\n    for (const [axis, keys, getScrollOffset] of properties) {\n      for (const key of keys) {\n        Object.defineProperty(this, key, {\n          get: () => {\n            const currentOffsets = getScrollOffset(scrollableAncestors);\n            const scrollOffsetsDeltla = scrollOffsets[axis] - currentOffsets;\n            return this.rect[key] + scrollOffsetsDeltla;\n          },\n          enumerable: true\n        });\n      }\n    }\n\n    Object.defineProperty(this, 'rect', {\n      enumerable: false\n    });\n  }\n\n}\n\nclass Listeners {\n  constructor(target) {\n    this.target = void 0;\n    this.listeners = [];\n\n    this.removeAll = () => {\n      this.listeners.forEach(listener => {\n        var _this$target;\n\n        return (_this$target = this.target) == null ? void 0 : _this$target.removeEventListener(...listener);\n      });\n    };\n\n    this.target = target;\n  }\n\n  add(eventName, handler, options) {\n    var _this$target2;\n\n    (_this$target2 = this.target) == null ? void 0 : _this$target2.addEventListener(eventName, handler, options);\n    this.listeners.push([eventName, handler, options]);\n  }\n\n}\n\nfunction getEventListenerTarget(target) {\n  // If the `event.target` element is removed from the document events will still be targeted\n  // at it, and hence won't always bubble up to the window or document anymore.\n  // If there is any risk of an element being removed while it is being dragged,\n  // the best practice is to attach the event listeners directly to the target.\n  // https://developer.mozilla.org/en-US/docs/Web/API/EventTarget\n  const {\n    EventTarget\n  } = getWindow(target);\n  return target instanceof EventTarget ? target : getOwnerDocument(target);\n}\n\nfunction hasExceededDistance(delta, measurement) {\n  const dx = Math.abs(delta.x);\n  const dy = Math.abs(delta.y);\n\n  if (typeof measurement === 'number') {\n    return Math.sqrt(dx ** 2 + dy ** 2) > measurement;\n  }\n\n  if ('x' in measurement && 'y' in measurement) {\n    return dx > measurement.x && dy > measurement.y;\n  }\n\n  if ('x' in measurement) {\n    return dx > measurement.x;\n  }\n\n  if ('y' in measurement) {\n    return dy > measurement.y;\n  }\n\n  return false;\n}\n\nvar EventName;\n\n(function (EventName) {\n  EventName[\"Click\"] = \"click\";\n  EventName[\"DragStart\"] = \"dragstart\";\n  EventName[\"Keydown\"] = \"keydown\";\n  EventName[\"ContextMenu\"] = \"contextmenu\";\n  EventName[\"Resize\"] = \"resize\";\n  EventName[\"SelectionChange\"] = \"selectionchange\";\n  EventName[\"VisibilityChange\"] = \"visibilitychange\";\n})(EventName || (EventName = {}));\n\nfunction preventDefault(event) {\n  event.preventDefault();\n}\nfunction stopPropagation(event) {\n  event.stopPropagation();\n}\n\nvar KeyboardCode;\n\n(function (KeyboardCode) {\n  KeyboardCode[\"Space\"] = \"Space\";\n  KeyboardCode[\"Down\"] = \"ArrowDown\";\n  KeyboardCode[\"Right\"] = \"ArrowRight\";\n  KeyboardCode[\"Left\"] = \"ArrowLeft\";\n  KeyboardCode[\"Up\"] = \"ArrowUp\";\n  KeyboardCode[\"Esc\"] = \"Escape\";\n  KeyboardCode[\"Enter\"] = \"Enter\";\n  KeyboardCode[\"Tab\"] = \"Tab\";\n})(KeyboardCode || (KeyboardCode = {}));\n\nconst defaultKeyboardCodes = {\n  start: [KeyboardCode.Space, KeyboardCode.Enter],\n  cancel: [KeyboardCode.Esc],\n  end: [KeyboardCode.Space, KeyboardCode.Enter, KeyboardCode.Tab]\n};\nconst defaultKeyboardCoordinateGetter = (event, _ref) => {\n  let {\n    currentCoordinates\n  } = _ref;\n\n  switch (event.code) {\n    case KeyboardCode.Right:\n      return { ...currentCoordinates,\n        x: currentCoordinates.x + 25\n      };\n\n    case KeyboardCode.Left:\n      return { ...currentCoordinates,\n        x: currentCoordinates.x - 25\n      };\n\n    case KeyboardCode.Down:\n      return { ...currentCoordinates,\n        y: currentCoordinates.y + 25\n      };\n\n    case KeyboardCode.Up:\n      return { ...currentCoordinates,\n        y: currentCoordinates.y - 25\n      };\n  }\n\n  return undefined;\n};\n\nclass KeyboardSensor {\n  constructor(props) {\n    this.props = void 0;\n    this.autoScrollEnabled = false;\n    this.referenceCoordinates = void 0;\n    this.listeners = void 0;\n    this.windowListeners = void 0;\n    this.props = props;\n    const {\n      event: {\n        target\n      }\n    } = props;\n    this.props = props;\n    this.listeners = new Listeners(getOwnerDocument(target));\n    this.windowListeners = new Listeners(getWindow(target));\n    this.handleKeyDown = this.handleKeyDown.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n    this.attach();\n  }\n\n  attach() {\n    this.handleStart();\n    this.windowListeners.add(EventName.Resize, this.handleCancel);\n    this.windowListeners.add(EventName.VisibilityChange, this.handleCancel);\n    setTimeout(() => this.listeners.add(EventName.Keydown, this.handleKeyDown));\n  }\n\n  handleStart() {\n    const {\n      activeNode,\n      onStart\n    } = this.props;\n    const node = activeNode.node.current;\n\n    if (node) {\n      scrollIntoViewIfNeeded(node);\n    }\n\n    onStart(defaultCoordinates);\n  }\n\n  handleKeyDown(event) {\n    if (isKeyboardEvent(event)) {\n      const {\n        active,\n        context,\n        options\n      } = this.props;\n      const {\n        keyboardCodes = defaultKeyboardCodes,\n        coordinateGetter = defaultKeyboardCoordinateGetter,\n        scrollBehavior = 'smooth'\n      } = options;\n      const {\n        code\n      } = event;\n\n      if (keyboardCodes.end.includes(code)) {\n        this.handleEnd(event);\n        return;\n      }\n\n      if (keyboardCodes.cancel.includes(code)) {\n        this.handleCancel(event);\n        return;\n      }\n\n      const {\n        collisionRect\n      } = context.current;\n      const currentCoordinates = collisionRect ? {\n        x: collisionRect.left,\n        y: collisionRect.top\n      } : defaultCoordinates;\n\n      if (!this.referenceCoordinates) {\n        this.referenceCoordinates = currentCoordinates;\n      }\n\n      const newCoordinates = coordinateGetter(event, {\n        active,\n        context: context.current,\n        currentCoordinates\n      });\n\n      if (newCoordinates) {\n        const coordinatesDelta = subtract(newCoordinates, currentCoordinates);\n        const scrollDelta = {\n          x: 0,\n          y: 0\n        };\n        const {\n          scrollableAncestors\n        } = context.current;\n\n        for (const scrollContainer of scrollableAncestors) {\n          const direction = event.code;\n          const {\n            isTop,\n            isRight,\n            isLeft,\n            isBottom,\n            maxScroll,\n            minScroll\n          } = getScrollPosition(scrollContainer);\n          const scrollElementRect = getScrollElementRect(scrollContainer);\n          const clampedCoordinates = {\n            x: Math.min(direction === KeyboardCode.Right ? scrollElementRect.right - scrollElementRect.width / 2 : scrollElementRect.right, Math.max(direction === KeyboardCode.Right ? scrollElementRect.left : scrollElementRect.left + scrollElementRect.width / 2, newCoordinates.x)),\n            y: Math.min(direction === KeyboardCode.Down ? scrollElementRect.bottom - scrollElementRect.height / 2 : scrollElementRect.bottom, Math.max(direction === KeyboardCode.Down ? scrollElementRect.top : scrollElementRect.top + scrollElementRect.height / 2, newCoordinates.y))\n          };\n          const canScrollX = direction === KeyboardCode.Right && !isRight || direction === KeyboardCode.Left && !isLeft;\n          const canScrollY = direction === KeyboardCode.Down && !isBottom || direction === KeyboardCode.Up && !isTop;\n\n          if (canScrollX && clampedCoordinates.x !== newCoordinates.x) {\n            const newScrollCoordinates = scrollContainer.scrollLeft + coordinatesDelta.x;\n            const canScrollToNewCoordinates = direction === KeyboardCode.Right && newScrollCoordinates <= maxScroll.x || direction === KeyboardCode.Left && newScrollCoordinates >= minScroll.x;\n\n            if (canScrollToNewCoordinates && !coordinatesDelta.y) {\n              // We don't need to update coordinates, the scroll adjustment alone will trigger\n              // logic to auto-detect the new container we are over\n              scrollContainer.scrollTo({\n                left: newScrollCoordinates,\n                behavior: scrollBehavior\n              });\n              return;\n            }\n\n            if (canScrollToNewCoordinates) {\n              scrollDelta.x = scrollContainer.scrollLeft - newScrollCoordinates;\n            } else {\n              scrollDelta.x = direction === KeyboardCode.Right ? scrollContainer.scrollLeft - maxScroll.x : scrollContainer.scrollLeft - minScroll.x;\n            }\n\n            if (scrollDelta.x) {\n              scrollContainer.scrollBy({\n                left: -scrollDelta.x,\n                behavior: scrollBehavior\n              });\n            }\n\n            break;\n          } else if (canScrollY && clampedCoordinates.y !== newCoordinates.y) {\n            const newScrollCoordinates = scrollContainer.scrollTop + coordinatesDelta.y;\n            const canScrollToNewCoordinates = direction === KeyboardCode.Down && newScrollCoordinates <= maxScroll.y || direction === KeyboardCode.Up && newScrollCoordinates >= minScroll.y;\n\n            if (canScrollToNewCoordinates && !coordinatesDelta.x) {\n              // We don't need to update coordinates, the scroll adjustment alone will trigger\n              // logic to auto-detect the new container we are over\n              scrollContainer.scrollTo({\n                top: newScrollCoordinates,\n                behavior: scrollBehavior\n              });\n              return;\n            }\n\n            if (canScrollToNewCoordinates) {\n              scrollDelta.y = scrollContainer.scrollTop - newScrollCoordinates;\n            } else {\n              scrollDelta.y = direction === KeyboardCode.Down ? scrollContainer.scrollTop - maxScroll.y : scrollContainer.scrollTop - minScroll.y;\n            }\n\n            if (scrollDelta.y) {\n              scrollContainer.scrollBy({\n                top: -scrollDelta.y,\n                behavior: scrollBehavior\n              });\n            }\n\n            break;\n          }\n        }\n\n        this.handleMove(event, add(subtract(newCoordinates, this.referenceCoordinates), scrollDelta));\n      }\n    }\n  }\n\n  handleMove(event, coordinates) {\n    const {\n      onMove\n    } = this.props;\n    event.preventDefault();\n    onMove(coordinates);\n  }\n\n  handleEnd(event) {\n    const {\n      onEnd\n    } = this.props;\n    event.preventDefault();\n    this.detach();\n    onEnd();\n  }\n\n  handleCancel(event) {\n    const {\n      onCancel\n    } = this.props;\n    event.preventDefault();\n    this.detach();\n    onCancel();\n  }\n\n  detach() {\n    this.listeners.removeAll();\n    this.windowListeners.removeAll();\n  }\n\n}\nKeyboardSensor.activators = [{\n  eventName: 'onKeyDown',\n  handler: (event, _ref, _ref2) => {\n    let {\n      keyboardCodes = defaultKeyboardCodes,\n      onActivation\n    } = _ref;\n    let {\n      active\n    } = _ref2;\n    const {\n      code\n    } = event.nativeEvent;\n\n    if (keyboardCodes.start.includes(code)) {\n      const activator = active.activatorNode.current;\n\n      if (activator && event.target !== activator) {\n        return false;\n      }\n\n      event.preventDefault();\n      onActivation == null ? void 0 : onActivation({\n        event: event.nativeEvent\n      });\n      return true;\n    }\n\n    return false;\n  }\n}];\n\nfunction isDistanceConstraint(constraint) {\n  return Boolean(constraint && 'distance' in constraint);\n}\n\nfunction isDelayConstraint(constraint) {\n  return Boolean(constraint && 'delay' in constraint);\n}\n\nclass AbstractPointerSensor {\n  constructor(props, events, listenerTarget) {\n    var _getEventCoordinates;\n\n    if (listenerTarget === void 0) {\n      listenerTarget = getEventListenerTarget(props.event.target);\n    }\n\n    this.props = void 0;\n    this.events = void 0;\n    this.autoScrollEnabled = true;\n    this.document = void 0;\n    this.activated = false;\n    this.initialCoordinates = void 0;\n    this.timeoutId = null;\n    this.listeners = void 0;\n    this.documentListeners = void 0;\n    this.windowListeners = void 0;\n    this.props = props;\n    this.events = events;\n    const {\n      event\n    } = props;\n    const {\n      target\n    } = event;\n    this.props = props;\n    this.events = events;\n    this.document = getOwnerDocument(target);\n    this.documentListeners = new Listeners(this.document);\n    this.listeners = new Listeners(listenerTarget);\n    this.windowListeners = new Listeners(getWindow(target));\n    this.initialCoordinates = (_getEventCoordinates = getEventCoordinates(event)) != null ? _getEventCoordinates : defaultCoordinates;\n    this.handleStart = this.handleStart.bind(this);\n    this.handleMove = this.handleMove.bind(this);\n    this.handleEnd = this.handleEnd.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n    this.handleKeydown = this.handleKeydown.bind(this);\n    this.removeTextSelection = this.removeTextSelection.bind(this);\n    this.attach();\n  }\n\n  attach() {\n    const {\n      events,\n      props: {\n        options: {\n          activationConstraint,\n          bypassActivationConstraint\n        }\n      }\n    } = this;\n    this.listeners.add(events.move.name, this.handleMove, {\n      passive: false\n    });\n    this.listeners.add(events.end.name, this.handleEnd);\n\n    if (events.cancel) {\n      this.listeners.add(events.cancel.name, this.handleCancel);\n    }\n\n    this.windowListeners.add(EventName.Resize, this.handleCancel);\n    this.windowListeners.add(EventName.DragStart, preventDefault);\n    this.windowListeners.add(EventName.VisibilityChange, this.handleCancel);\n    this.windowListeners.add(EventName.ContextMenu, preventDefault);\n    this.documentListeners.add(EventName.Keydown, this.handleKeydown);\n\n    if (activationConstraint) {\n      if (bypassActivationConstraint != null && bypassActivationConstraint({\n        event: this.props.event,\n        activeNode: this.props.activeNode,\n        options: this.props.options\n      })) {\n        return this.handleStart();\n      }\n\n      if (isDelayConstraint(activationConstraint)) {\n        this.timeoutId = setTimeout(this.handleStart, activationConstraint.delay);\n        this.handlePending(activationConstraint);\n        return;\n      }\n\n      if (isDistanceConstraint(activationConstraint)) {\n        this.handlePending(activationConstraint);\n        return;\n      }\n    }\n\n    this.handleStart();\n  }\n\n  detach() {\n    this.listeners.removeAll();\n    this.windowListeners.removeAll(); // Wait until the next event loop before removing document listeners\n    // This is necessary because we listen for `click` and `selection` events on the document\n\n    setTimeout(this.documentListeners.removeAll, 50);\n\n    if (this.timeoutId !== null) {\n      clearTimeout(this.timeoutId);\n      this.timeoutId = null;\n    }\n  }\n\n  handlePending(constraint, offset) {\n    const {\n      active,\n      onPending\n    } = this.props;\n    onPending(active, constraint, this.initialCoordinates, offset);\n  }\n\n  handleStart() {\n    const {\n      initialCoordinates\n    } = this;\n    const {\n      onStart\n    } = this.props;\n\n    if (initialCoordinates) {\n      this.activated = true; // Stop propagation of click events once activation constraints are met\n\n      this.documentListeners.add(EventName.Click, stopPropagation, {\n        capture: true\n      }); // Remove any text selection from the document\n\n      this.removeTextSelection(); // Prevent further text selection while dragging\n\n      this.documentListeners.add(EventName.SelectionChange, this.removeTextSelection);\n      onStart(initialCoordinates);\n    }\n  }\n\n  handleMove(event) {\n    var _getEventCoordinates2;\n\n    const {\n      activated,\n      initialCoordinates,\n      props\n    } = this;\n    const {\n      onMove,\n      options: {\n        activationConstraint\n      }\n    } = props;\n\n    if (!initialCoordinates) {\n      return;\n    }\n\n    const coordinates = (_getEventCoordinates2 = getEventCoordinates(event)) != null ? _getEventCoordinates2 : defaultCoordinates;\n    const delta = subtract(initialCoordinates, coordinates); // Constraint validation\n\n    if (!activated && activationConstraint) {\n      if (isDistanceConstraint(activationConstraint)) {\n        if (activationConstraint.tolerance != null && hasExceededDistance(delta, activationConstraint.tolerance)) {\n          return this.handleCancel();\n        }\n\n        if (hasExceededDistance(delta, activationConstraint.distance)) {\n          return this.handleStart();\n        }\n      }\n\n      if (isDelayConstraint(activationConstraint)) {\n        if (hasExceededDistance(delta, activationConstraint.tolerance)) {\n          return this.handleCancel();\n        }\n      }\n\n      this.handlePending(activationConstraint, delta);\n      return;\n    }\n\n    if (event.cancelable) {\n      event.preventDefault();\n    }\n\n    onMove(coordinates);\n  }\n\n  handleEnd() {\n    const {\n      onAbort,\n      onEnd\n    } = this.props;\n    this.detach();\n\n    if (!this.activated) {\n      onAbort(this.props.active);\n    }\n\n    onEnd();\n  }\n\n  handleCancel() {\n    const {\n      onAbort,\n      onCancel\n    } = this.props;\n    this.detach();\n\n    if (!this.activated) {\n      onAbort(this.props.active);\n    }\n\n    onCancel();\n  }\n\n  handleKeydown(event) {\n    if (event.code === KeyboardCode.Esc) {\n      this.handleCancel();\n    }\n  }\n\n  removeTextSelection() {\n    var _this$document$getSel;\n\n    (_this$document$getSel = this.document.getSelection()) == null ? void 0 : _this$document$getSel.removeAllRanges();\n  }\n\n}\n\nconst events = {\n  cancel: {\n    name: 'pointercancel'\n  },\n  move: {\n    name: 'pointermove'\n  },\n  end: {\n    name: 'pointerup'\n  }\n};\nclass PointerSensor extends AbstractPointerSensor {\n  constructor(props) {\n    const {\n      event\n    } = props; // Pointer events stop firing if the target is unmounted while dragging\n    // Therefore we attach listeners to the owner document instead\n\n    const listenerTarget = getOwnerDocument(event.target);\n    super(props, events, listenerTarget);\n  }\n\n}\nPointerSensor.activators = [{\n  eventName: 'onPointerDown',\n  handler: (_ref, _ref2) => {\n    let {\n      nativeEvent: event\n    } = _ref;\n    let {\n      onActivation\n    } = _ref2;\n\n    if (!event.isPrimary || event.button !== 0) {\n      return false;\n    }\n\n    onActivation == null ? void 0 : onActivation({\n      event\n    });\n    return true;\n  }\n}];\n\nconst events$1 = {\n  move: {\n    name: 'mousemove'\n  },\n  end: {\n    name: 'mouseup'\n  }\n};\nvar MouseButton;\n\n(function (MouseButton) {\n  MouseButton[MouseButton[\"RightClick\"] = 2] = \"RightClick\";\n})(MouseButton || (MouseButton = {}));\n\nclass MouseSensor extends AbstractPointerSensor {\n  constructor(props) {\n    super(props, events$1, getOwnerDocument(props.event.target));\n  }\n\n}\nMouseSensor.activators = [{\n  eventName: 'onMouseDown',\n  handler: (_ref, _ref2) => {\n    let {\n      nativeEvent: event\n    } = _ref;\n    let {\n      onActivation\n    } = _ref2;\n\n    if (event.button === MouseButton.RightClick) {\n      return false;\n    }\n\n    onActivation == null ? void 0 : onActivation({\n      event\n    });\n    return true;\n  }\n}];\n\nconst events$2 = {\n  cancel: {\n    name: 'touchcancel'\n  },\n  move: {\n    name: 'touchmove'\n  },\n  end: {\n    name: 'touchend'\n  }\n};\nclass TouchSensor extends AbstractPointerSensor {\n  constructor(props) {\n    super(props, events$2);\n  }\n\n  static setup() {\n    // Adding a non-capture and non-passive `touchmove` listener in order\n    // to force `event.preventDefault()` calls to work in dynamically added\n    // touchmove event handlers. This is required for iOS Safari.\n    window.addEventListener(events$2.move.name, noop, {\n      capture: false,\n      passive: false\n    });\n    return function teardown() {\n      window.removeEventListener(events$2.move.name, noop);\n    }; // We create a new handler because the teardown function of another sensor\n    // could remove our event listener if we use a referentially equal listener.\n\n    function noop() {}\n  }\n\n}\nTouchSensor.activators = [{\n  eventName: 'onTouchStart',\n  handler: (_ref, _ref2) => {\n    let {\n      nativeEvent: event\n    } = _ref;\n    let {\n      onActivation\n    } = _ref2;\n    const {\n      touches\n    } = event;\n\n    if (touches.length > 1) {\n      return false;\n    }\n\n    onActivation == null ? void 0 : onActivation({\n      event\n    });\n    return true;\n  }\n}];\n\nvar AutoScrollActivator;\n\n(function (AutoScrollActivator) {\n  AutoScrollActivator[AutoScrollActivator[\"Pointer\"] = 0] = \"Pointer\";\n  AutoScrollActivator[AutoScrollActivator[\"DraggableRect\"] = 1] = \"DraggableRect\";\n})(AutoScrollActivator || (AutoScrollActivator = {}));\n\nvar TraversalOrder;\n\n(function (TraversalOrder) {\n  TraversalOrder[TraversalOrder[\"TreeOrder\"] = 0] = \"TreeOrder\";\n  TraversalOrder[TraversalOrder[\"ReversedTreeOrder\"] = 1] = \"ReversedTreeOrder\";\n})(TraversalOrder || (TraversalOrder = {}));\n\nfunction useAutoScroller(_ref) {\n  let {\n    acceleration,\n    activator = AutoScrollActivator.Pointer,\n    canScroll,\n    draggingRect,\n    enabled,\n    interval = 5,\n    order = TraversalOrder.TreeOrder,\n    pointerCoordinates,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    delta,\n    threshold\n  } = _ref;\n  const scrollIntent = useScrollIntent({\n    delta,\n    disabled: !enabled\n  });\n  const [setAutoScrollInterval, clearAutoScrollInterval] = useInterval();\n  const scrollSpeed = useRef({\n    x: 0,\n    y: 0\n  });\n  const scrollDirection = useRef({\n    x: 0,\n    y: 0\n  });\n  const rect = useMemo(() => {\n    switch (activator) {\n      case AutoScrollActivator.Pointer:\n        return pointerCoordinates ? {\n          top: pointerCoordinates.y,\n          bottom: pointerCoordinates.y,\n          left: pointerCoordinates.x,\n          right: pointerCoordinates.x\n        } : null;\n\n      case AutoScrollActivator.DraggableRect:\n        return draggingRect;\n    }\n  }, [activator, draggingRect, pointerCoordinates]);\n  const scrollContainerRef = useRef(null);\n  const autoScroll = useCallback(() => {\n    const scrollContainer = scrollContainerRef.current;\n\n    if (!scrollContainer) {\n      return;\n    }\n\n    const scrollLeft = scrollSpeed.current.x * scrollDirection.current.x;\n    const scrollTop = scrollSpeed.current.y * scrollDirection.current.y;\n    scrollContainer.scrollBy(scrollLeft, scrollTop);\n  }, []);\n  const sortedScrollableAncestors = useMemo(() => order === TraversalOrder.TreeOrder ? [...scrollableAncestors].reverse() : scrollableAncestors, [order, scrollableAncestors]);\n  useEffect(() => {\n    if (!enabled || !scrollableAncestors.length || !rect) {\n      clearAutoScrollInterval();\n      return;\n    }\n\n    for (const scrollContainer of sortedScrollableAncestors) {\n      if ((canScroll == null ? void 0 : canScroll(scrollContainer)) === false) {\n        continue;\n      }\n\n      const index = scrollableAncestors.indexOf(scrollContainer);\n      const scrollContainerRect = scrollableAncestorRects[index];\n\n      if (!scrollContainerRect) {\n        continue;\n      }\n\n      const {\n        direction,\n        speed\n      } = getScrollDirectionAndSpeed(scrollContainer, scrollContainerRect, rect, acceleration, threshold);\n\n      for (const axis of ['x', 'y']) {\n        if (!scrollIntent[axis][direction[axis]]) {\n          speed[axis] = 0;\n          direction[axis] = 0;\n        }\n      }\n\n      if (speed.x > 0 || speed.y > 0) {\n        clearAutoScrollInterval();\n        scrollContainerRef.current = scrollContainer;\n        setAutoScrollInterval(autoScroll, interval);\n        scrollSpeed.current = speed;\n        scrollDirection.current = direction;\n        return;\n      }\n    }\n\n    scrollSpeed.current = {\n      x: 0,\n      y: 0\n    };\n    scrollDirection.current = {\n      x: 0,\n      y: 0\n    };\n    clearAutoScrollInterval();\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [acceleration, autoScroll, canScroll, clearAutoScrollInterval, enabled, interval, // eslint-disable-next-line react-hooks/exhaustive-deps\n  JSON.stringify(rect), // eslint-disable-next-line react-hooks/exhaustive-deps\n  JSON.stringify(scrollIntent), setAutoScrollInterval, scrollableAncestors, sortedScrollableAncestors, scrollableAncestorRects, // eslint-disable-next-line react-hooks/exhaustive-deps\n  JSON.stringify(threshold)]);\n}\nconst defaultScrollIntent = {\n  x: {\n    [Direction.Backward]: false,\n    [Direction.Forward]: false\n  },\n  y: {\n    [Direction.Backward]: false,\n    [Direction.Forward]: false\n  }\n};\n\nfunction useScrollIntent(_ref2) {\n  let {\n    delta,\n    disabled\n  } = _ref2;\n  const previousDelta = usePrevious(delta);\n  return useLazyMemo(previousIntent => {\n    if (disabled || !previousDelta || !previousIntent) {\n      // Reset scroll intent tracking when auto-scrolling is disabled\n      return defaultScrollIntent;\n    }\n\n    const direction = {\n      x: Math.sign(delta.x - previousDelta.x),\n      y: Math.sign(delta.y - previousDelta.y)\n    }; // Keep track of the user intent to scroll in each direction for both axis\n\n    return {\n      x: {\n        [Direction.Backward]: previousIntent.x[Direction.Backward] || direction.x === -1,\n        [Direction.Forward]: previousIntent.x[Direction.Forward] || direction.x === 1\n      },\n      y: {\n        [Direction.Backward]: previousIntent.y[Direction.Backward] || direction.y === -1,\n        [Direction.Forward]: previousIntent.y[Direction.Forward] || direction.y === 1\n      }\n    };\n  }, [disabled, delta, previousDelta]);\n}\n\nfunction useCachedNode(draggableNodes, id) {\n  const draggableNode = id != null ? draggableNodes.get(id) : undefined;\n  const node = draggableNode ? draggableNode.node.current : null;\n  return useLazyMemo(cachedNode => {\n    var _ref;\n\n    if (id == null) {\n      return null;\n    } // In some cases, the draggable node can unmount while dragging\n    // This is the case for virtualized lists. In those situations,\n    // we fall back to the last known value for that node.\n\n\n    return (_ref = node != null ? node : cachedNode) != null ? _ref : null;\n  }, [node, id]);\n}\n\nfunction useCombineActivators(sensors, getSyntheticHandler) {\n  return useMemo(() => sensors.reduce((accumulator, sensor) => {\n    const {\n      sensor: Sensor\n    } = sensor;\n    const sensorActivators = Sensor.activators.map(activator => ({\n      eventName: activator.eventName,\n      handler: getSyntheticHandler(activator.handler, sensor)\n    }));\n    return [...accumulator, ...sensorActivators];\n  }, []), [sensors, getSyntheticHandler]);\n}\n\nvar MeasuringStrategy;\n\n(function (MeasuringStrategy) {\n  MeasuringStrategy[MeasuringStrategy[\"Always\"] = 0] = \"Always\";\n  MeasuringStrategy[MeasuringStrategy[\"BeforeDragging\"] = 1] = \"BeforeDragging\";\n  MeasuringStrategy[MeasuringStrategy[\"WhileDragging\"] = 2] = \"WhileDragging\";\n})(MeasuringStrategy || (MeasuringStrategy = {}));\n\nvar MeasuringFrequency;\n\n(function (MeasuringFrequency) {\n  MeasuringFrequency[\"Optimized\"] = \"optimized\";\n})(MeasuringFrequency || (MeasuringFrequency = {}));\n\nconst defaultValue = /*#__PURE__*/new Map();\nfunction useDroppableMeasuring(containers, _ref) {\n  let {\n    dragging,\n    dependencies,\n    config\n  } = _ref;\n  const [queue, setQueue] = useState(null);\n  const {\n    frequency,\n    measure,\n    strategy\n  } = config;\n  const containersRef = useRef(containers);\n  const disabled = isDisabled();\n  const disabledRef = useLatestValue(disabled);\n  const measureDroppableContainers = useCallback(function (ids) {\n    if (ids === void 0) {\n      ids = [];\n    }\n\n    if (disabledRef.current) {\n      return;\n    }\n\n    setQueue(value => {\n      if (value === null) {\n        return ids;\n      }\n\n      return value.concat(ids.filter(id => !value.includes(id)));\n    });\n  }, [disabledRef]);\n  const timeoutId = useRef(null);\n  const droppableRects = useLazyMemo(previousValue => {\n    if (disabled && !dragging) {\n      return defaultValue;\n    }\n\n    if (!previousValue || previousValue === defaultValue || containersRef.current !== containers || queue != null) {\n      const map = new Map();\n\n      for (let container of containers) {\n        if (!container) {\n          continue;\n        }\n\n        if (queue && queue.length > 0 && !queue.includes(container.id) && container.rect.current) {\n          // This container does not need to be re-measured\n          map.set(container.id, container.rect.current);\n          continue;\n        }\n\n        const node = container.node.current;\n        const rect = node ? new Rect(measure(node), node) : null;\n        container.rect.current = rect;\n\n        if (rect) {\n          map.set(container.id, rect);\n        }\n      }\n\n      return map;\n    }\n\n    return previousValue;\n  }, [containers, queue, dragging, disabled, measure]);\n  useEffect(() => {\n    containersRef.current = containers;\n  }, [containers]);\n  useEffect(() => {\n    if (disabled) {\n      return;\n    }\n\n    measureDroppableContainers();\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [dragging, disabled]);\n  useEffect(() => {\n    if (queue && queue.length > 0) {\n      setQueue(null);\n    }\n  }, //eslint-disable-next-line react-hooks/exhaustive-deps\n  [JSON.stringify(queue)]);\n  useEffect(() => {\n    if (disabled || typeof frequency !== 'number' || timeoutId.current !== null) {\n      return;\n    }\n\n    timeoutId.current = setTimeout(() => {\n      measureDroppableContainers();\n      timeoutId.current = null;\n    }, frequency);\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [frequency, disabled, measureDroppableContainers, ...dependencies]);\n  return {\n    droppableRects,\n    measureDroppableContainers,\n    measuringScheduled: queue != null\n  };\n\n  function isDisabled() {\n    switch (strategy) {\n      case MeasuringStrategy.Always:\n        return false;\n\n      case MeasuringStrategy.BeforeDragging:\n        return dragging;\n\n      default:\n        return !dragging;\n    }\n  }\n}\n\nfunction useInitialValue(value, computeFn) {\n  return useLazyMemo(previousValue => {\n    if (!value) {\n      return null;\n    }\n\n    if (previousValue) {\n      return previousValue;\n    }\n\n    return typeof computeFn === 'function' ? computeFn(value) : value;\n  }, [computeFn, value]);\n}\n\nfunction useInitialRect(node, measure) {\n  return useInitialValue(node, measure);\n}\n\n/**\r\n * Returns a new MutationObserver instance.\r\n * If `MutationObserver` is undefined in the execution environment, returns `undefined`.\r\n */\n\nfunction useMutationObserver(_ref) {\n  let {\n    callback,\n    disabled\n  } = _ref;\n  const handleMutations = useEvent(callback);\n  const mutationObserver = useMemo(() => {\n    if (disabled || typeof window === 'undefined' || typeof window.MutationObserver === 'undefined') {\n      return undefined;\n    }\n\n    const {\n      MutationObserver\n    } = window;\n    return new MutationObserver(handleMutations);\n  }, [handleMutations, disabled]);\n  useEffect(() => {\n    return () => mutationObserver == null ? void 0 : mutationObserver.disconnect();\n  }, [mutationObserver]);\n  return mutationObserver;\n}\n\n/**\r\n * Returns a new ResizeObserver instance bound to the `onResize` callback.\r\n * If `ResizeObserver` is undefined in the execution environment, returns `undefined`.\r\n */\n\nfunction useResizeObserver(_ref) {\n  let {\n    callback,\n    disabled\n  } = _ref;\n  const handleResize = useEvent(callback);\n  const resizeObserver = useMemo(() => {\n    if (disabled || typeof window === 'undefined' || typeof window.ResizeObserver === 'undefined') {\n      return undefined;\n    }\n\n    const {\n      ResizeObserver\n    } = window;\n    return new ResizeObserver(handleResize);\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [disabled]);\n  useEffect(() => {\n    return () => resizeObserver == null ? void 0 : resizeObserver.disconnect();\n  }, [resizeObserver]);\n  return resizeObserver;\n}\n\nfunction defaultMeasure(element) {\n  return new Rect(getClientRect(element), element);\n}\n\nfunction useRect(element, measure, fallbackRect) {\n  if (measure === void 0) {\n    measure = defaultMeasure;\n  }\n\n  const [rect, setRect] = useState(null);\n\n  function measureRect() {\n    setRect(currentRect => {\n      if (!element) {\n        return null;\n      }\n\n      if (element.isConnected === false) {\n        var _ref;\n\n        // Fall back to last rect we measured if the element is\n        // no longer connected to the DOM.\n        return (_ref = currentRect != null ? currentRect : fallbackRect) != null ? _ref : null;\n      }\n\n      const newRect = measure(element);\n\n      if (JSON.stringify(currentRect) === JSON.stringify(newRect)) {\n        return currentRect;\n      }\n\n      return newRect;\n    });\n  }\n\n  const mutationObserver = useMutationObserver({\n    callback(records) {\n      if (!element) {\n        return;\n      }\n\n      for (const record of records) {\n        const {\n          type,\n          target\n        } = record;\n\n        if (type === 'childList' && target instanceof HTMLElement && target.contains(element)) {\n          measureRect();\n          break;\n        }\n      }\n    }\n\n  });\n  const resizeObserver = useResizeObserver({\n    callback: measureRect\n  });\n  useIsomorphicLayoutEffect(() => {\n    measureRect();\n\n    if (element) {\n      resizeObserver == null ? void 0 : resizeObserver.observe(element);\n      mutationObserver == null ? void 0 : mutationObserver.observe(document.body, {\n        childList: true,\n        subtree: true\n      });\n    } else {\n      resizeObserver == null ? void 0 : resizeObserver.disconnect();\n      mutationObserver == null ? void 0 : mutationObserver.disconnect();\n    }\n  }, [element]);\n  return rect;\n}\n\nfunction useRectDelta(rect) {\n  const initialRect = useInitialValue(rect);\n  return getRectDelta(rect, initialRect);\n}\n\nconst defaultValue$1 = [];\nfunction useScrollableAncestors(node) {\n  const previousNode = useRef(node);\n  const ancestors = useLazyMemo(previousValue => {\n    if (!node) {\n      return defaultValue$1;\n    }\n\n    if (previousValue && previousValue !== defaultValue$1 && node && previousNode.current && node.parentNode === previousNode.current.parentNode) {\n      return previousValue;\n    }\n\n    return getScrollableAncestors(node);\n  }, [node]);\n  useEffect(() => {\n    previousNode.current = node;\n  }, [node]);\n  return ancestors;\n}\n\nfunction useScrollOffsets(elements) {\n  const [scrollCoordinates, setScrollCoordinates] = useState(null);\n  const prevElements = useRef(elements); // To-do: Throttle the handleScroll callback\n\n  const handleScroll = useCallback(event => {\n    const scrollingElement = getScrollableElement(event.target);\n\n    if (!scrollingElement) {\n      return;\n    }\n\n    setScrollCoordinates(scrollCoordinates => {\n      if (!scrollCoordinates) {\n        return null;\n      }\n\n      scrollCoordinates.set(scrollingElement, getScrollCoordinates(scrollingElement));\n      return new Map(scrollCoordinates);\n    });\n  }, []);\n  useEffect(() => {\n    const previousElements = prevElements.current;\n\n    if (elements !== previousElements) {\n      cleanup(previousElements);\n      const entries = elements.map(element => {\n        const scrollableElement = getScrollableElement(element);\n\n        if (scrollableElement) {\n          scrollableElement.addEventListener('scroll', handleScroll, {\n            passive: true\n          });\n          return [scrollableElement, getScrollCoordinates(scrollableElement)];\n        }\n\n        return null;\n      }).filter(entry => entry != null);\n      setScrollCoordinates(entries.length ? new Map(entries) : null);\n      prevElements.current = elements;\n    }\n\n    return () => {\n      cleanup(elements);\n      cleanup(previousElements);\n    };\n\n    function cleanup(elements) {\n      elements.forEach(element => {\n        const scrollableElement = getScrollableElement(element);\n        scrollableElement == null ? void 0 : scrollableElement.removeEventListener('scroll', handleScroll);\n      });\n    }\n  }, [handleScroll, elements]);\n  return useMemo(() => {\n    if (elements.length) {\n      return scrollCoordinates ? Array.from(scrollCoordinates.values()).reduce((acc, coordinates) => add(acc, coordinates), defaultCoordinates) : getScrollOffsets(elements);\n    }\n\n    return defaultCoordinates;\n  }, [elements, scrollCoordinates]);\n}\n\nfunction useScrollOffsetsDelta(scrollOffsets, dependencies) {\n  if (dependencies === void 0) {\n    dependencies = [];\n  }\n\n  const initialScrollOffsets = useRef(null);\n  useEffect(() => {\n    initialScrollOffsets.current = null;\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  dependencies);\n  useEffect(() => {\n    const hasScrollOffsets = scrollOffsets !== defaultCoordinates;\n\n    if (hasScrollOffsets && !initialScrollOffsets.current) {\n      initialScrollOffsets.current = scrollOffsets;\n    }\n\n    if (!hasScrollOffsets && initialScrollOffsets.current) {\n      initialScrollOffsets.current = null;\n    }\n  }, [scrollOffsets]);\n  return initialScrollOffsets.current ? subtract(scrollOffsets, initialScrollOffsets.current) : defaultCoordinates;\n}\n\nfunction useSensorSetup(sensors) {\n  useEffect(() => {\n    if (!canUseDOM) {\n      return;\n    }\n\n    const teardownFns = sensors.map(_ref => {\n      let {\n        sensor\n      } = _ref;\n      return sensor.setup == null ? void 0 : sensor.setup();\n    });\n    return () => {\n      for (const teardown of teardownFns) {\n        teardown == null ? void 0 : teardown();\n      }\n    };\n  }, // TO-DO: Sensors length could theoretically change which would not be a valid dependency\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  sensors.map(_ref2 => {\n    let {\n      sensor\n    } = _ref2;\n    return sensor;\n  }));\n}\n\nfunction useSyntheticListeners(listeners, id) {\n  return useMemo(() => {\n    return listeners.reduce((acc, _ref) => {\n      let {\n        eventName,\n        handler\n      } = _ref;\n\n      acc[eventName] = event => {\n        handler(event, id);\n      };\n\n      return acc;\n    }, {});\n  }, [listeners, id]);\n}\n\nfunction useWindowRect(element) {\n  return useMemo(() => element ? getWindowClientRect(element) : null, [element]);\n}\n\nconst defaultValue$2 = [];\nfunction useRects(elements, measure) {\n  if (measure === void 0) {\n    measure = getClientRect;\n  }\n\n  const [firstElement] = elements;\n  const windowRect = useWindowRect(firstElement ? getWindow(firstElement) : null);\n  const [rects, setRects] = useState(defaultValue$2);\n\n  function measureRects() {\n    setRects(() => {\n      if (!elements.length) {\n        return defaultValue$2;\n      }\n\n      return elements.map(element => isDocumentScrollingElement(element) ? windowRect : new Rect(measure(element), element));\n    });\n  }\n\n  const resizeObserver = useResizeObserver({\n    callback: measureRects\n  });\n  useIsomorphicLayoutEffect(() => {\n    resizeObserver == null ? void 0 : resizeObserver.disconnect();\n    measureRects();\n    elements.forEach(element => resizeObserver == null ? void 0 : resizeObserver.observe(element));\n  }, [elements]);\n  return rects;\n}\n\nfunction getMeasurableNode(node) {\n  if (!node) {\n    return null;\n  }\n\n  if (node.children.length > 1) {\n    return node;\n  }\n\n  const firstChild = node.children[0];\n  return isHTMLElement(firstChild) ? firstChild : node;\n}\n\nfunction useDragOverlayMeasuring(_ref) {\n  let {\n    measure\n  } = _ref;\n  const [rect, setRect] = useState(null);\n  const handleResize = useCallback(entries => {\n    for (const {\n      target\n    } of entries) {\n      if (isHTMLElement(target)) {\n        setRect(rect => {\n          const newRect = measure(target);\n          return rect ? { ...rect,\n            width: newRect.width,\n            height: newRect.height\n          } : newRect;\n        });\n        break;\n      }\n    }\n  }, [measure]);\n  const resizeObserver = useResizeObserver({\n    callback: handleResize\n  });\n  const handleNodeChange = useCallback(element => {\n    const node = getMeasurableNode(element);\n    resizeObserver == null ? void 0 : resizeObserver.disconnect();\n\n    if (node) {\n      resizeObserver == null ? void 0 : resizeObserver.observe(node);\n    }\n\n    setRect(node ? measure(node) : null);\n  }, [measure, resizeObserver]);\n  const [nodeRef, setRef] = useNodeRef(handleNodeChange);\n  return useMemo(() => ({\n    nodeRef,\n    rect,\n    setRef\n  }), [rect, nodeRef, setRef]);\n}\n\nconst defaultSensors = [{\n  sensor: PointerSensor,\n  options: {}\n}, {\n  sensor: KeyboardSensor,\n  options: {}\n}];\nconst defaultData = {\n  current: {}\n};\nconst defaultMeasuringConfiguration = {\n  draggable: {\n    measure: getTransformAgnosticClientRect\n  },\n  droppable: {\n    measure: getTransformAgnosticClientRect,\n    strategy: MeasuringStrategy.WhileDragging,\n    frequency: MeasuringFrequency.Optimized\n  },\n  dragOverlay: {\n    measure: getClientRect\n  }\n};\n\nclass DroppableContainersMap extends Map {\n  get(id) {\n    var _super$get;\n\n    return id != null ? (_super$get = super.get(id)) != null ? _super$get : undefined : undefined;\n  }\n\n  toArray() {\n    return Array.from(this.values());\n  }\n\n  getEnabled() {\n    return this.toArray().filter(_ref => {\n      let {\n        disabled\n      } = _ref;\n      return !disabled;\n    });\n  }\n\n  getNodeFor(id) {\n    var _this$get$node$curren, _this$get;\n\n    return (_this$get$node$curren = (_this$get = this.get(id)) == null ? void 0 : _this$get.node.current) != null ? _this$get$node$curren : undefined;\n  }\n\n}\n\nconst defaultPublicContext = {\n  activatorEvent: null,\n  active: null,\n  activeNode: null,\n  activeNodeRect: null,\n  collisions: null,\n  containerNodeRect: null,\n  draggableNodes: /*#__PURE__*/new Map(),\n  droppableRects: /*#__PURE__*/new Map(),\n  droppableContainers: /*#__PURE__*/new DroppableContainersMap(),\n  over: null,\n  dragOverlay: {\n    nodeRef: {\n      current: null\n    },\n    rect: null,\n    setRef: noop\n  },\n  scrollableAncestors: [],\n  scrollableAncestorRects: [],\n  measuringConfiguration: defaultMeasuringConfiguration,\n  measureDroppableContainers: noop,\n  windowRect: null,\n  measuringScheduled: false\n};\nconst defaultInternalContext = {\n  activatorEvent: null,\n  activators: [],\n  active: null,\n  activeNodeRect: null,\n  ariaDescribedById: {\n    draggable: ''\n  },\n  dispatch: noop,\n  draggableNodes: /*#__PURE__*/new Map(),\n  over: null,\n  measureDroppableContainers: noop\n};\nconst InternalContext = /*#__PURE__*/createContext(defaultInternalContext);\nconst PublicContext = /*#__PURE__*/createContext(defaultPublicContext);\n\nfunction getInitialState() {\n  return {\n    draggable: {\n      active: null,\n      initialCoordinates: {\n        x: 0,\n        y: 0\n      },\n      nodes: new Map(),\n      translate: {\n        x: 0,\n        y: 0\n      }\n    },\n    droppable: {\n      containers: new DroppableContainersMap()\n    }\n  };\n}\nfunction reducer(state, action) {\n  switch (action.type) {\n    case Action.DragStart:\n      return { ...state,\n        draggable: { ...state.draggable,\n          initialCoordinates: action.initialCoordinates,\n          active: action.active\n        }\n      };\n\n    case Action.DragMove:\n      if (state.draggable.active == null) {\n        return state;\n      }\n\n      return { ...state,\n        draggable: { ...state.draggable,\n          translate: {\n            x: action.coordinates.x - state.draggable.initialCoordinates.x,\n            y: action.coordinates.y - state.draggable.initialCoordinates.y\n          }\n        }\n      };\n\n    case Action.DragEnd:\n    case Action.DragCancel:\n      return { ...state,\n        draggable: { ...state.draggable,\n          active: null,\n          initialCoordinates: {\n            x: 0,\n            y: 0\n          },\n          translate: {\n            x: 0,\n            y: 0\n          }\n        }\n      };\n\n    case Action.RegisterDroppable:\n      {\n        const {\n          element\n        } = action;\n        const {\n          id\n        } = element;\n        const containers = new DroppableContainersMap(state.droppable.containers);\n        containers.set(id, element);\n        return { ...state,\n          droppable: { ...state.droppable,\n            containers\n          }\n        };\n      }\n\n    case Action.SetDroppableDisabled:\n      {\n        const {\n          id,\n          key,\n          disabled\n        } = action;\n        const element = state.droppable.containers.get(id);\n\n        if (!element || key !== element.key) {\n          return state;\n        }\n\n        const containers = new DroppableContainersMap(state.droppable.containers);\n        containers.set(id, { ...element,\n          disabled\n        });\n        return { ...state,\n          droppable: { ...state.droppable,\n            containers\n          }\n        };\n      }\n\n    case Action.UnregisterDroppable:\n      {\n        const {\n          id,\n          key\n        } = action;\n        const element = state.droppable.containers.get(id);\n\n        if (!element || key !== element.key) {\n          return state;\n        }\n\n        const containers = new DroppableContainersMap(state.droppable.containers);\n        containers.delete(id);\n        return { ...state,\n          droppable: { ...state.droppable,\n            containers\n          }\n        };\n      }\n\n    default:\n      {\n        return state;\n      }\n  }\n}\n\nfunction RestoreFocus(_ref) {\n  let {\n    disabled\n  } = _ref;\n  const {\n    active,\n    activatorEvent,\n    draggableNodes\n  } = useContext(InternalContext);\n  const previousActivatorEvent = usePrevious(activatorEvent);\n  const previousActiveId = usePrevious(active == null ? void 0 : active.id); // Restore keyboard focus on the activator node\n\n  useEffect(() => {\n    if (disabled) {\n      return;\n    }\n\n    if (!activatorEvent && previousActivatorEvent && previousActiveId != null) {\n      if (!isKeyboardEvent(previousActivatorEvent)) {\n        return;\n      }\n\n      if (document.activeElement === previousActivatorEvent.target) {\n        // No need to restore focus\n        return;\n      }\n\n      const draggableNode = draggableNodes.get(previousActiveId);\n\n      if (!draggableNode) {\n        return;\n      }\n\n      const {\n        activatorNode,\n        node\n      } = draggableNode;\n\n      if (!activatorNode.current && !node.current) {\n        return;\n      }\n\n      requestAnimationFrame(() => {\n        for (const element of [activatorNode.current, node.current]) {\n          if (!element) {\n            continue;\n          }\n\n          const focusableNode = findFirstFocusableNode(element);\n\n          if (focusableNode) {\n            focusableNode.focus();\n            break;\n          }\n        }\n      });\n    }\n  }, [activatorEvent, disabled, draggableNodes, previousActiveId, previousActivatorEvent]);\n  return null;\n}\n\nfunction applyModifiers(modifiers, _ref) {\n  let {\n    transform,\n    ...args\n  } = _ref;\n  return modifiers != null && modifiers.length ? modifiers.reduce((accumulator, modifier) => {\n    return modifier({\n      transform: accumulator,\n      ...args\n    });\n  }, transform) : transform;\n}\n\nfunction useMeasuringConfiguration(config) {\n  return useMemo(() => ({\n    draggable: { ...defaultMeasuringConfiguration.draggable,\n      ...(config == null ? void 0 : config.draggable)\n    },\n    droppable: { ...defaultMeasuringConfiguration.droppable,\n      ...(config == null ? void 0 : config.droppable)\n    },\n    dragOverlay: { ...defaultMeasuringConfiguration.dragOverlay,\n      ...(config == null ? void 0 : config.dragOverlay)\n    }\n  }), // eslint-disable-next-line react-hooks/exhaustive-deps\n  [config == null ? void 0 : config.draggable, config == null ? void 0 : config.droppable, config == null ? void 0 : config.dragOverlay]);\n}\n\nfunction useLayoutShiftScrollCompensation(_ref) {\n  let {\n    activeNode,\n    measure,\n    initialRect,\n    config = true\n  } = _ref;\n  const initialized = useRef(false);\n  const {\n    x,\n    y\n  } = typeof config === 'boolean' ? {\n    x: config,\n    y: config\n  } : config;\n  useIsomorphicLayoutEffect(() => {\n    const disabled = !x && !y;\n\n    if (disabled || !activeNode) {\n      initialized.current = false;\n      return;\n    }\n\n    if (initialized.current || !initialRect) {\n      // Return early if layout shift scroll compensation was already attempted\n      // or if there is no initialRect to compare to.\n      return;\n    } // Get the most up to date node ref for the active draggable\n\n\n    const node = activeNode == null ? void 0 : activeNode.node.current;\n\n    if (!node || node.isConnected === false) {\n      // Return early if there is no attached node ref or if the node is\n      // disconnected from the document.\n      return;\n    }\n\n    const rect = measure(node);\n    const rectDelta = getRectDelta(rect, initialRect);\n\n    if (!x) {\n      rectDelta.x = 0;\n    }\n\n    if (!y) {\n      rectDelta.y = 0;\n    } // Only perform layout shift scroll compensation once\n\n\n    initialized.current = true;\n\n    if (Math.abs(rectDelta.x) > 0 || Math.abs(rectDelta.y) > 0) {\n      const firstScrollableAncestor = getFirstScrollableAncestor(node);\n\n      if (firstScrollableAncestor) {\n        firstScrollableAncestor.scrollBy({\n          top: rectDelta.y,\n          left: rectDelta.x\n        });\n      }\n    }\n  }, [activeNode, x, y, initialRect, measure]);\n}\n\nconst ActiveDraggableContext = /*#__PURE__*/createContext({ ...defaultCoordinates,\n  scaleX: 1,\n  scaleY: 1\n});\nvar Status;\n\n(function (Status) {\n  Status[Status[\"Uninitialized\"] = 0] = \"Uninitialized\";\n  Status[Status[\"Initializing\"] = 1] = \"Initializing\";\n  Status[Status[\"Initialized\"] = 2] = \"Initialized\";\n})(Status || (Status = {}));\n\nconst DndContext = /*#__PURE__*/memo(function DndContext(_ref) {\n  var _sensorContext$curren, _dragOverlay$nodeRef$, _dragOverlay$rect, _over$rect;\n\n  let {\n    id,\n    accessibility,\n    autoScroll = true,\n    children,\n    sensors = defaultSensors,\n    collisionDetection = rectIntersection,\n    measuring,\n    modifiers,\n    ...props\n  } = _ref;\n  const store = useReducer(reducer, undefined, getInitialState);\n  const [state, dispatch] = store;\n  const [dispatchMonitorEvent, registerMonitorListener] = useDndMonitorProvider();\n  const [status, setStatus] = useState(Status.Uninitialized);\n  const isInitialized = status === Status.Initialized;\n  const {\n    draggable: {\n      active: activeId,\n      nodes: draggableNodes,\n      translate\n    },\n    droppable: {\n      containers: droppableContainers\n    }\n  } = state;\n  const node = activeId != null ? draggableNodes.get(activeId) : null;\n  const activeRects = useRef({\n    initial: null,\n    translated: null\n  });\n  const active = useMemo(() => {\n    var _node$data;\n\n    return activeId != null ? {\n      id: activeId,\n      // It's possible for the active node to unmount while dragging\n      data: (_node$data = node == null ? void 0 : node.data) != null ? _node$data : defaultData,\n      rect: activeRects\n    } : null;\n  }, [activeId, node]);\n  const activeRef = useRef(null);\n  const [activeSensor, setActiveSensor] = useState(null);\n  const [activatorEvent, setActivatorEvent] = useState(null);\n  const latestProps = useLatestValue(props, Object.values(props));\n  const draggableDescribedById = useUniqueId(\"DndDescribedBy\", id);\n  const enabledDroppableContainers = useMemo(() => droppableContainers.getEnabled(), [droppableContainers]);\n  const measuringConfiguration = useMeasuringConfiguration(measuring);\n  const {\n    droppableRects,\n    measureDroppableContainers,\n    measuringScheduled\n  } = useDroppableMeasuring(enabledDroppableContainers, {\n    dragging: isInitialized,\n    dependencies: [translate.x, translate.y],\n    config: measuringConfiguration.droppable\n  });\n  const activeNode = useCachedNode(draggableNodes, activeId);\n  const activationCoordinates = useMemo(() => activatorEvent ? getEventCoordinates(activatorEvent) : null, [activatorEvent]);\n  const autoScrollOptions = getAutoScrollerOptions();\n  const initialActiveNodeRect = useInitialRect(activeNode, measuringConfiguration.draggable.measure);\n  useLayoutShiftScrollCompensation({\n    activeNode: activeId != null ? draggableNodes.get(activeId) : null,\n    config: autoScrollOptions.layoutShiftCompensation,\n    initialRect: initialActiveNodeRect,\n    measure: measuringConfiguration.draggable.measure\n  });\n  const activeNodeRect = useRect(activeNode, measuringConfiguration.draggable.measure, initialActiveNodeRect);\n  const containerNodeRect = useRect(activeNode ? activeNode.parentElement : null);\n  const sensorContext = useRef({\n    activatorEvent: null,\n    active: null,\n    activeNode,\n    collisionRect: null,\n    collisions: null,\n    droppableRects,\n    draggableNodes,\n    draggingNode: null,\n    draggingNodeRect: null,\n    droppableContainers,\n    over: null,\n    scrollableAncestors: [],\n    scrollAdjustedTranslate: null\n  });\n  const overNode = droppableContainers.getNodeFor((_sensorContext$curren = sensorContext.current.over) == null ? void 0 : _sensorContext$curren.id);\n  const dragOverlay = useDragOverlayMeasuring({\n    measure: measuringConfiguration.dragOverlay.measure\n  }); // Use the rect of the drag overlay if it is mounted\n\n  const draggingNode = (_dragOverlay$nodeRef$ = dragOverlay.nodeRef.current) != null ? _dragOverlay$nodeRef$ : activeNode;\n  const draggingNodeRect = isInitialized ? (_dragOverlay$rect = dragOverlay.rect) != null ? _dragOverlay$rect : activeNodeRect : null;\n  const usesDragOverlay = Boolean(dragOverlay.nodeRef.current && dragOverlay.rect); // The delta between the previous and new position of the draggable node\n  // is only relevant when there is no drag overlay\n\n  const nodeRectDelta = useRectDelta(usesDragOverlay ? null : activeNodeRect); // Get the window rect of the dragging node\n\n  const windowRect = useWindowRect(draggingNode ? getWindow(draggingNode) : null); // Get scrollable ancestors of the dragging node\n\n  const scrollableAncestors = useScrollableAncestors(isInitialized ? overNode != null ? overNode : activeNode : null);\n  const scrollableAncestorRects = useRects(scrollableAncestors); // Apply modifiers\n\n  const modifiedTranslate = applyModifiers(modifiers, {\n    transform: {\n      x: translate.x - nodeRectDelta.x,\n      y: translate.y - nodeRectDelta.y,\n      scaleX: 1,\n      scaleY: 1\n    },\n    activatorEvent,\n    active,\n    activeNodeRect,\n    containerNodeRect,\n    draggingNodeRect,\n    over: sensorContext.current.over,\n    overlayNodeRect: dragOverlay.rect,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    windowRect\n  });\n  const pointerCoordinates = activationCoordinates ? add(activationCoordinates, translate) : null;\n  const scrollOffsets = useScrollOffsets(scrollableAncestors); // Represents the scroll delta since dragging was initiated\n\n  const scrollAdjustment = useScrollOffsetsDelta(scrollOffsets); // Represents the scroll delta since the last time the active node rect was measured\n\n  const activeNodeScrollDelta = useScrollOffsetsDelta(scrollOffsets, [activeNodeRect]);\n  const scrollAdjustedTranslate = add(modifiedTranslate, scrollAdjustment);\n  const collisionRect = draggingNodeRect ? getAdjustedRect(draggingNodeRect, modifiedTranslate) : null;\n  const collisions = active && collisionRect ? collisionDetection({\n    active,\n    collisionRect,\n    droppableRects,\n    droppableContainers: enabledDroppableContainers,\n    pointerCoordinates\n  }) : null;\n  const overId = getFirstCollision(collisions, 'id');\n  const [over, setOver] = useState(null); // When there is no drag overlay used, we need to account for the\n  // window scroll delta\n\n  const appliedTranslate = usesDragOverlay ? modifiedTranslate : add(modifiedTranslate, activeNodeScrollDelta);\n  const transform = adjustScale(appliedTranslate, (_over$rect = over == null ? void 0 : over.rect) != null ? _over$rect : null, activeNodeRect);\n  const activeSensorRef = useRef(null);\n  const instantiateSensor = useCallback((event, _ref2) => {\n    let {\n      sensor: Sensor,\n      options\n    } = _ref2;\n\n    if (activeRef.current == null) {\n      return;\n    }\n\n    const activeNode = draggableNodes.get(activeRef.current);\n\n    if (!activeNode) {\n      return;\n    }\n\n    const activatorEvent = event.nativeEvent;\n    const sensorInstance = new Sensor({\n      active: activeRef.current,\n      activeNode,\n      event: activatorEvent,\n      options,\n      // Sensors need to be instantiated with refs for arguments that change over time\n      // otherwise they are frozen in time with the stale arguments\n      context: sensorContext,\n\n      onAbort(id) {\n        const draggableNode = draggableNodes.get(id);\n\n        if (!draggableNode) {\n          return;\n        }\n\n        const {\n          onDragAbort\n        } = latestProps.current;\n        const event = {\n          id\n        };\n        onDragAbort == null ? void 0 : onDragAbort(event);\n        dispatchMonitorEvent({\n          type: 'onDragAbort',\n          event\n        });\n      },\n\n      onPending(id, constraint, initialCoordinates, offset) {\n        const draggableNode = draggableNodes.get(id);\n\n        if (!draggableNode) {\n          return;\n        }\n\n        const {\n          onDragPending\n        } = latestProps.current;\n        const event = {\n          id,\n          constraint,\n          initialCoordinates,\n          offset\n        };\n        onDragPending == null ? void 0 : onDragPending(event);\n        dispatchMonitorEvent({\n          type: 'onDragPending',\n          event\n        });\n      },\n\n      onStart(initialCoordinates) {\n        const id = activeRef.current;\n\n        if (id == null) {\n          return;\n        }\n\n        const draggableNode = draggableNodes.get(id);\n\n        if (!draggableNode) {\n          return;\n        }\n\n        const {\n          onDragStart\n        } = latestProps.current;\n        const event = {\n          activatorEvent,\n          active: {\n            id,\n            data: draggableNode.data,\n            rect: activeRects\n          }\n        };\n        unstable_batchedUpdates(() => {\n          onDragStart == null ? void 0 : onDragStart(event);\n          setStatus(Status.Initializing);\n          dispatch({\n            type: Action.DragStart,\n            initialCoordinates,\n            active: id\n          });\n          dispatchMonitorEvent({\n            type: 'onDragStart',\n            event\n          });\n          setActiveSensor(activeSensorRef.current);\n          setActivatorEvent(activatorEvent);\n        });\n      },\n\n      onMove(coordinates) {\n        dispatch({\n          type: Action.DragMove,\n          coordinates\n        });\n      },\n\n      onEnd: createHandler(Action.DragEnd),\n      onCancel: createHandler(Action.DragCancel)\n    });\n    activeSensorRef.current = sensorInstance;\n\n    function createHandler(type) {\n      return async function handler() {\n        const {\n          active,\n          collisions,\n          over,\n          scrollAdjustedTranslate\n        } = sensorContext.current;\n        let event = null;\n\n        if (active && scrollAdjustedTranslate) {\n          const {\n            cancelDrop\n          } = latestProps.current;\n          event = {\n            activatorEvent,\n            active: active,\n            collisions,\n            delta: scrollAdjustedTranslate,\n            over\n          };\n\n          if (type === Action.DragEnd && typeof cancelDrop === 'function') {\n            const shouldCancel = await Promise.resolve(cancelDrop(event));\n\n            if (shouldCancel) {\n              type = Action.DragCancel;\n            }\n          }\n        }\n\n        activeRef.current = null;\n        unstable_batchedUpdates(() => {\n          dispatch({\n            type\n          });\n          setStatus(Status.Uninitialized);\n          setOver(null);\n          setActiveSensor(null);\n          setActivatorEvent(null);\n          activeSensorRef.current = null;\n          const eventName = type === Action.DragEnd ? 'onDragEnd' : 'onDragCancel';\n\n          if (event) {\n            const handler = latestProps.current[eventName];\n            handler == null ? void 0 : handler(event);\n            dispatchMonitorEvent({\n              type: eventName,\n              event\n            });\n          }\n        });\n      };\n    }\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [draggableNodes]);\n  const bindActivatorToSensorInstantiator = useCallback((handler, sensor) => {\n    return (event, active) => {\n      const nativeEvent = event.nativeEvent;\n      const activeDraggableNode = draggableNodes.get(active);\n\n      if ( // Another sensor is already instantiating\n      activeRef.current !== null || // No active draggable\n      !activeDraggableNode || // Event has already been captured\n      nativeEvent.dndKit || nativeEvent.defaultPrevented) {\n        return;\n      }\n\n      const activationContext = {\n        active: activeDraggableNode\n      };\n      const shouldActivate = handler(event, sensor.options, activationContext);\n\n      if (shouldActivate === true) {\n        nativeEvent.dndKit = {\n          capturedBy: sensor.sensor\n        };\n        activeRef.current = active;\n        instantiateSensor(event, sensor);\n      }\n    };\n  }, [draggableNodes, instantiateSensor]);\n  const activators = useCombineActivators(sensors, bindActivatorToSensorInstantiator);\n  useSensorSetup(sensors);\n  useIsomorphicLayoutEffect(() => {\n    if (activeNodeRect && status === Status.Initializing) {\n      setStatus(Status.Initialized);\n    }\n  }, [activeNodeRect, status]);\n  useEffect(() => {\n    const {\n      onDragMove\n    } = latestProps.current;\n    const {\n      active,\n      activatorEvent,\n      collisions,\n      over\n    } = sensorContext.current;\n\n    if (!active || !activatorEvent) {\n      return;\n    }\n\n    const event = {\n      active,\n      activatorEvent,\n      collisions,\n      delta: {\n        x: scrollAdjustedTranslate.x,\n        y: scrollAdjustedTranslate.y\n      },\n      over\n    };\n    unstable_batchedUpdates(() => {\n      onDragMove == null ? void 0 : onDragMove(event);\n      dispatchMonitorEvent({\n        type: 'onDragMove',\n        event\n      });\n    });\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [scrollAdjustedTranslate.x, scrollAdjustedTranslate.y]);\n  useEffect(() => {\n    const {\n      active,\n      activatorEvent,\n      collisions,\n      droppableContainers,\n      scrollAdjustedTranslate\n    } = sensorContext.current;\n\n    if (!active || activeRef.current == null || !activatorEvent || !scrollAdjustedTranslate) {\n      return;\n    }\n\n    const {\n      onDragOver\n    } = latestProps.current;\n    const overContainer = droppableContainers.get(overId);\n    const over = overContainer && overContainer.rect.current ? {\n      id: overContainer.id,\n      rect: overContainer.rect.current,\n      data: overContainer.data,\n      disabled: overContainer.disabled\n    } : null;\n    const event = {\n      active,\n      activatorEvent,\n      collisions,\n      delta: {\n        x: scrollAdjustedTranslate.x,\n        y: scrollAdjustedTranslate.y\n      },\n      over\n    };\n    unstable_batchedUpdates(() => {\n      setOver(over);\n      onDragOver == null ? void 0 : onDragOver(event);\n      dispatchMonitorEvent({\n        type: 'onDragOver',\n        event\n      });\n    });\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [overId]);\n  useIsomorphicLayoutEffect(() => {\n    sensorContext.current = {\n      activatorEvent,\n      active,\n      activeNode,\n      collisionRect,\n      collisions,\n      droppableRects,\n      draggableNodes,\n      draggingNode,\n      draggingNodeRect,\n      droppableContainers,\n      over,\n      scrollableAncestors,\n      scrollAdjustedTranslate\n    };\n    activeRects.current = {\n      initial: draggingNodeRect,\n      translated: collisionRect\n    };\n  }, [active, activeNode, collisions, collisionRect, draggableNodes, draggingNode, draggingNodeRect, droppableRects, droppableContainers, over, scrollableAncestors, scrollAdjustedTranslate]);\n  useAutoScroller({ ...autoScrollOptions,\n    delta: translate,\n    draggingRect: collisionRect,\n    pointerCoordinates,\n    scrollableAncestors,\n    scrollableAncestorRects\n  });\n  const publicContext = useMemo(() => {\n    const context = {\n      active,\n      activeNode,\n      activeNodeRect,\n      activatorEvent,\n      collisions,\n      containerNodeRect,\n      dragOverlay,\n      draggableNodes,\n      droppableContainers,\n      droppableRects,\n      over,\n      measureDroppableContainers,\n      scrollableAncestors,\n      scrollableAncestorRects,\n      measuringConfiguration,\n      measuringScheduled,\n      windowRect\n    };\n    return context;\n  }, [active, activeNode, activeNodeRect, activatorEvent, collisions, containerNodeRect, dragOverlay, draggableNodes, droppableContainers, droppableRects, over, measureDroppableContainers, scrollableAncestors, scrollableAncestorRects, measuringConfiguration, measuringScheduled, windowRect]);\n  const internalContext = useMemo(() => {\n    const context = {\n      activatorEvent,\n      activators,\n      active,\n      activeNodeRect,\n      ariaDescribedById: {\n        draggable: draggableDescribedById\n      },\n      dispatch,\n      draggableNodes,\n      over,\n      measureDroppableContainers\n    };\n    return context;\n  }, [activatorEvent, activators, active, activeNodeRect, dispatch, draggableDescribedById, draggableNodes, over, measureDroppableContainers]);\n  return React.createElement(DndMonitorContext.Provider, {\n    value: registerMonitorListener\n  }, React.createElement(InternalContext.Provider, {\n    value: internalContext\n  }, React.createElement(PublicContext.Provider, {\n    value: publicContext\n  }, React.createElement(ActiveDraggableContext.Provider, {\n    value: transform\n  }, children)), React.createElement(RestoreFocus, {\n    disabled: (accessibility == null ? void 0 : accessibility.restoreFocus) === false\n  })), React.createElement(Accessibility, { ...accessibility,\n    hiddenTextDescribedById: draggableDescribedById\n  }));\n\n  function getAutoScrollerOptions() {\n    const activeSensorDisablesAutoscroll = (activeSensor == null ? void 0 : activeSensor.autoScrollEnabled) === false;\n    const autoScrollGloballyDisabled = typeof autoScroll === 'object' ? autoScroll.enabled === false : autoScroll === false;\n    const enabled = isInitialized && !activeSensorDisablesAutoscroll && !autoScrollGloballyDisabled;\n\n    if (typeof autoScroll === 'object') {\n      return { ...autoScroll,\n        enabled\n      };\n    }\n\n    return {\n      enabled\n    };\n  }\n});\n\nconst NullContext = /*#__PURE__*/createContext(null);\nconst defaultRole = 'button';\nconst ID_PREFIX = 'Draggable';\nfunction useDraggable(_ref) {\n  let {\n    id,\n    data,\n    disabled = false,\n    attributes\n  } = _ref;\n  const key = useUniqueId(ID_PREFIX);\n  const {\n    activators,\n    activatorEvent,\n    active,\n    activeNodeRect,\n    ariaDescribedById,\n    draggableNodes,\n    over\n  } = useContext(InternalContext);\n  const {\n    role = defaultRole,\n    roleDescription = 'draggable',\n    tabIndex = 0\n  } = attributes != null ? attributes : {};\n  const isDragging = (active == null ? void 0 : active.id) === id;\n  const transform = useContext(isDragging ? ActiveDraggableContext : NullContext);\n  const [node, setNodeRef] = useNodeRef();\n  const [activatorNode, setActivatorNodeRef] = useNodeRef();\n  const listeners = useSyntheticListeners(activators, id);\n  const dataRef = useLatestValue(data);\n  useIsomorphicLayoutEffect(() => {\n    draggableNodes.set(id, {\n      id,\n      key,\n      node,\n      activatorNode,\n      data: dataRef\n    });\n    return () => {\n      const node = draggableNodes.get(id);\n\n      if (node && node.key === key) {\n        draggableNodes.delete(id);\n      }\n    };\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [draggableNodes, id]);\n  const memoizedAttributes = useMemo(() => ({\n    role,\n    tabIndex,\n    'aria-disabled': disabled,\n    'aria-pressed': isDragging && role === defaultRole ? true : undefined,\n    'aria-roledescription': roleDescription,\n    'aria-describedby': ariaDescribedById.draggable\n  }), [disabled, role, tabIndex, isDragging, roleDescription, ariaDescribedById.draggable]);\n  return {\n    active,\n    activatorEvent,\n    activeNodeRect,\n    attributes: memoizedAttributes,\n    isDragging,\n    listeners: disabled ? undefined : listeners,\n    node,\n    over,\n    setNodeRef,\n    setActivatorNodeRef,\n    transform\n  };\n}\n\nfunction useDndContext() {\n  return useContext(PublicContext);\n}\n\nconst ID_PREFIX$1 = 'Droppable';\nconst defaultResizeObserverConfig = {\n  timeout: 25\n};\nfunction useDroppable(_ref) {\n  let {\n    data,\n    disabled = false,\n    id,\n    resizeObserverConfig\n  } = _ref;\n  const key = useUniqueId(ID_PREFIX$1);\n  const {\n    active,\n    dispatch,\n    over,\n    measureDroppableContainers\n  } = useContext(InternalContext);\n  const previous = useRef({\n    disabled\n  });\n  const resizeObserverConnected = useRef(false);\n  const rect = useRef(null);\n  const callbackId = useRef(null);\n  const {\n    disabled: resizeObserverDisabled,\n    updateMeasurementsFor,\n    timeout: resizeObserverTimeout\n  } = { ...defaultResizeObserverConfig,\n    ...resizeObserverConfig\n  };\n  const ids = useLatestValue(updateMeasurementsFor != null ? updateMeasurementsFor : id);\n  const handleResize = useCallback(() => {\n    if (!resizeObserverConnected.current) {\n      // ResizeObserver invokes the `handleResize` callback as soon as `observe` is called,\n      // assuming the element is rendered and displayed.\n      resizeObserverConnected.current = true;\n      return;\n    }\n\n    if (callbackId.current != null) {\n      clearTimeout(callbackId.current);\n    }\n\n    callbackId.current = setTimeout(() => {\n      measureDroppableContainers(Array.isArray(ids.current) ? ids.current : [ids.current]);\n      callbackId.current = null;\n    }, resizeObserverTimeout);\n  }, //eslint-disable-next-line react-hooks/exhaustive-deps\n  [resizeObserverTimeout]);\n  const resizeObserver = useResizeObserver({\n    callback: handleResize,\n    disabled: resizeObserverDisabled || !active\n  });\n  const handleNodeChange = useCallback((newElement, previousElement) => {\n    if (!resizeObserver) {\n      return;\n    }\n\n    if (previousElement) {\n      resizeObserver.unobserve(previousElement);\n      resizeObserverConnected.current = false;\n    }\n\n    if (newElement) {\n      resizeObserver.observe(newElement);\n    }\n  }, [resizeObserver]);\n  const [nodeRef, setNodeRef] = useNodeRef(handleNodeChange);\n  const dataRef = useLatestValue(data);\n  useEffect(() => {\n    if (!resizeObserver || !nodeRef.current) {\n      return;\n    }\n\n    resizeObserver.disconnect();\n    resizeObserverConnected.current = false;\n    resizeObserver.observe(nodeRef.current);\n  }, [nodeRef, resizeObserver]);\n  useEffect(() => {\n    dispatch({\n      type: Action.RegisterDroppable,\n      element: {\n        id,\n        key,\n        disabled,\n        node: nodeRef,\n        rect,\n        data: dataRef\n      }\n    });\n    return () => dispatch({\n      type: Action.UnregisterDroppable,\n      key,\n      id\n    });\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [id]);\n  useEffect(() => {\n    if (disabled !== previous.current.disabled) {\n      dispatch({\n        type: Action.SetDroppableDisabled,\n        id,\n        key,\n        disabled\n      });\n      previous.current.disabled = disabled;\n    }\n  }, [id, key, disabled, dispatch]);\n  return {\n    active,\n    rect,\n    isOver: (over == null ? void 0 : over.id) === id,\n    node: nodeRef,\n    over,\n    setNodeRef\n  };\n}\n\nfunction AnimationManager(_ref) {\n  let {\n    animation,\n    children\n  } = _ref;\n  const [clonedChildren, setClonedChildren] = useState(null);\n  const [element, setElement] = useState(null);\n  const previousChildren = usePrevious(children);\n\n  if (!children && !clonedChildren && previousChildren) {\n    setClonedChildren(previousChildren);\n  }\n\n  useIsomorphicLayoutEffect(() => {\n    if (!element) {\n      return;\n    }\n\n    const key = clonedChildren == null ? void 0 : clonedChildren.key;\n    const id = clonedChildren == null ? void 0 : clonedChildren.props.id;\n\n    if (key == null || id == null) {\n      setClonedChildren(null);\n      return;\n    }\n\n    Promise.resolve(animation(id, element)).then(() => {\n      setClonedChildren(null);\n    });\n  }, [animation, clonedChildren, element]);\n  return React.createElement(React.Fragment, null, children, clonedChildren ? cloneElement(clonedChildren, {\n    ref: setElement\n  }) : null);\n}\n\nconst defaultTransform = {\n  x: 0,\n  y: 0,\n  scaleX: 1,\n  scaleY: 1\n};\nfunction NullifiedContextProvider(_ref) {\n  let {\n    children\n  } = _ref;\n  return React.createElement(InternalContext.Provider, {\n    value: defaultInternalContext\n  }, React.createElement(ActiveDraggableContext.Provider, {\n    value: defaultTransform\n  }, children));\n}\n\nconst baseStyles = {\n  position: 'fixed',\n  touchAction: 'none'\n};\n\nconst defaultTransition = activatorEvent => {\n  const isKeyboardActivator = isKeyboardEvent(activatorEvent);\n  return isKeyboardActivator ? 'transform 250ms ease' : undefined;\n};\n\nconst PositionedOverlay = /*#__PURE__*/forwardRef((_ref, ref) => {\n  let {\n    as,\n    activatorEvent,\n    adjustScale,\n    children,\n    className,\n    rect,\n    style,\n    transform,\n    transition = defaultTransition\n  } = _ref;\n\n  if (!rect) {\n    return null;\n  }\n\n  const scaleAdjustedTransform = adjustScale ? transform : { ...transform,\n    scaleX: 1,\n    scaleY: 1\n  };\n  const styles = { ...baseStyles,\n    width: rect.width,\n    height: rect.height,\n    top: rect.top,\n    left: rect.left,\n    transform: CSS.Transform.toString(scaleAdjustedTransform),\n    transformOrigin: adjustScale && activatorEvent ? getRelativeTransformOrigin(activatorEvent, rect) : undefined,\n    transition: typeof transition === 'function' ? transition(activatorEvent) : transition,\n    ...style\n  };\n  return React.createElement(as, {\n    className,\n    style: styles,\n    ref\n  }, children);\n});\n\nconst defaultDropAnimationSideEffects = options => _ref => {\n  let {\n    active,\n    dragOverlay\n  } = _ref;\n  const originalStyles = {};\n  const {\n    styles,\n    className\n  } = options;\n\n  if (styles != null && styles.active) {\n    for (const [key, value] of Object.entries(styles.active)) {\n      if (value === undefined) {\n        continue;\n      }\n\n      originalStyles[key] = active.node.style.getPropertyValue(key);\n      active.node.style.setProperty(key, value);\n    }\n  }\n\n  if (styles != null && styles.dragOverlay) {\n    for (const [key, value] of Object.entries(styles.dragOverlay)) {\n      if (value === undefined) {\n        continue;\n      }\n\n      dragOverlay.node.style.setProperty(key, value);\n    }\n  }\n\n  if (className != null && className.active) {\n    active.node.classList.add(className.active);\n  }\n\n  if (className != null && className.dragOverlay) {\n    dragOverlay.node.classList.add(className.dragOverlay);\n  }\n\n  return function cleanup() {\n    for (const [key, value] of Object.entries(originalStyles)) {\n      active.node.style.setProperty(key, value);\n    }\n\n    if (className != null && className.active) {\n      active.node.classList.remove(className.active);\n    }\n  };\n};\n\nconst defaultKeyframeResolver = _ref2 => {\n  let {\n    transform: {\n      initial,\n      final\n    }\n  } = _ref2;\n  return [{\n    transform: CSS.Transform.toString(initial)\n  }, {\n    transform: CSS.Transform.toString(final)\n  }];\n};\n\nconst defaultDropAnimationConfiguration = {\n  duration: 250,\n  easing: 'ease',\n  keyframes: defaultKeyframeResolver,\n  sideEffects: /*#__PURE__*/defaultDropAnimationSideEffects({\n    styles: {\n      active: {\n        opacity: '0'\n      }\n    }\n  })\n};\nfunction useDropAnimation(_ref3) {\n  let {\n    config,\n    draggableNodes,\n    droppableContainers,\n    measuringConfiguration\n  } = _ref3;\n  return useEvent((id, node) => {\n    if (config === null) {\n      return;\n    }\n\n    const activeDraggable = draggableNodes.get(id);\n\n    if (!activeDraggable) {\n      return;\n    }\n\n    const activeNode = activeDraggable.node.current;\n\n    if (!activeNode) {\n      return;\n    }\n\n    const measurableNode = getMeasurableNode(node);\n\n    if (!measurableNode) {\n      return;\n    }\n\n    const {\n      transform\n    } = getWindow(node).getComputedStyle(node);\n    const parsedTransform = parseTransform(transform);\n\n    if (!parsedTransform) {\n      return;\n    }\n\n    const animation = typeof config === 'function' ? config : createDefaultDropAnimation(config);\n    scrollIntoViewIfNeeded(activeNode, measuringConfiguration.draggable.measure);\n    return animation({\n      active: {\n        id,\n        data: activeDraggable.data,\n        node: activeNode,\n        rect: measuringConfiguration.draggable.measure(activeNode)\n      },\n      draggableNodes,\n      dragOverlay: {\n        node,\n        rect: measuringConfiguration.dragOverlay.measure(measurableNode)\n      },\n      droppableContainers,\n      measuringConfiguration,\n      transform: parsedTransform\n    });\n  });\n}\n\nfunction createDefaultDropAnimation(options) {\n  const {\n    duration,\n    easing,\n    sideEffects,\n    keyframes\n  } = { ...defaultDropAnimationConfiguration,\n    ...options\n  };\n  return _ref4 => {\n    let {\n      active,\n      dragOverlay,\n      transform,\n      ...rest\n    } = _ref4;\n\n    if (!duration) {\n      // Do not animate if animation duration is zero.\n      return;\n    }\n\n    const delta = {\n      x: dragOverlay.rect.left - active.rect.left,\n      y: dragOverlay.rect.top - active.rect.top\n    };\n    const scale = {\n      scaleX: transform.scaleX !== 1 ? active.rect.width * transform.scaleX / dragOverlay.rect.width : 1,\n      scaleY: transform.scaleY !== 1 ? active.rect.height * transform.scaleY / dragOverlay.rect.height : 1\n    };\n    const finalTransform = {\n      x: transform.x - delta.x,\n      y: transform.y - delta.y,\n      ...scale\n    };\n    const animationKeyframes = keyframes({ ...rest,\n      active,\n      dragOverlay,\n      transform: {\n        initial: transform,\n        final: finalTransform\n      }\n    });\n    const [firstKeyframe] = animationKeyframes;\n    const lastKeyframe = animationKeyframes[animationKeyframes.length - 1];\n\n    if (JSON.stringify(firstKeyframe) === JSON.stringify(lastKeyframe)) {\n      // The start and end keyframes are the same, infer that there is no animation needed.\n      return;\n    }\n\n    const cleanup = sideEffects == null ? void 0 : sideEffects({\n      active,\n      dragOverlay,\n      ...rest\n    });\n    const animation = dragOverlay.node.animate(animationKeyframes, {\n      duration,\n      easing,\n      fill: 'forwards'\n    });\n    return new Promise(resolve => {\n      animation.onfinish = () => {\n        cleanup == null ? void 0 : cleanup();\n        resolve();\n      };\n    });\n  };\n}\n\nlet key = 0;\nfunction useKey(id) {\n  return useMemo(() => {\n    if (id == null) {\n      return;\n    }\n\n    key++;\n    return key;\n  }, [id]);\n}\n\nconst DragOverlay = /*#__PURE__*/React.memo(_ref => {\n  let {\n    adjustScale = false,\n    children,\n    dropAnimation: dropAnimationConfig,\n    style,\n    transition,\n    modifiers,\n    wrapperElement = 'div',\n    className,\n    zIndex = 999\n  } = _ref;\n  const {\n    activatorEvent,\n    active,\n    activeNodeRect,\n    containerNodeRect,\n    draggableNodes,\n    droppableContainers,\n    dragOverlay,\n    over,\n    measuringConfiguration,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    windowRect\n  } = useDndContext();\n  const transform = useContext(ActiveDraggableContext);\n  const key = useKey(active == null ? void 0 : active.id);\n  const modifiedTransform = applyModifiers(modifiers, {\n    activatorEvent,\n    active,\n    activeNodeRect,\n    containerNodeRect,\n    draggingNodeRect: dragOverlay.rect,\n    over,\n    overlayNodeRect: dragOverlay.rect,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    transform,\n    windowRect\n  });\n  const initialRect = useInitialValue(activeNodeRect);\n  const dropAnimation = useDropAnimation({\n    config: dropAnimationConfig,\n    draggableNodes,\n    droppableContainers,\n    measuringConfiguration\n  }); // We need to wait for the active node to be measured before connecting the drag overlay ref\n  // otherwise collisions can be computed against a mispositioned drag overlay\n\n  const ref = initialRect ? dragOverlay.setRef : undefined;\n  return React.createElement(NullifiedContextProvider, null, React.createElement(AnimationManager, {\n    animation: dropAnimation\n  }, active && key ? React.createElement(PositionedOverlay, {\n    key: key,\n    id: active.id,\n    ref: ref,\n    as: wrapperElement,\n    activatorEvent: activatorEvent,\n    adjustScale: adjustScale,\n    className: className,\n    transition: transition,\n    rect: initialRect,\n    style: {\n      zIndex,\n      ...style\n    },\n    transform: modifiedTransform\n  }, children) : null));\n});\n\nconst snapCenterToCursor = _ref => {\n  let {\n    activatorEvent,\n    draggingNodeRect,\n    transform\n  } = _ref;\n\n  if (draggingNodeRect && activatorEvent) {\n    const activatorCoordinates = getEventCoordinates(activatorEvent);\n\n    if (!activatorCoordinates) {\n      return transform;\n    }\n\n    const offsetX = activatorCoordinates.x - draggingNodeRect.left;\n    const offsetY = activatorCoordinates.y - draggingNodeRect.top;\n    return { ...transform,\n      x: transform.x + offsetX - draggingNodeRect.width / 2,\n      y: transform.y + offsetY - draggingNodeRect.height / 2\n    };\n  }\n\n  return transform;\n};\n\nfunction generateBoard(noOfRows, noOfColumns, boardOrientation) {\n    const board = Array.from(Array(noOfRows), () => new Array(noOfColumns));\n    for (let row = 0; row < noOfRows; row++) {\n        for (let column = 0; column < noOfColumns; column++) {\n            board[row][column] = {\n                squareId: `${columnIndexToChessColumn(column, noOfColumns, boardOrientation)}${rowIndexToChessRow(row, noOfRows, boardOrientation)}`, // e.g. \"a8\" for row 0, column 0 in white orientation\n                isLightSquare: (row + column) % 2 === 0,\n            };\n        }\n    }\n    return board;\n}\nfunction rowIndexToChessRow(row, noOfRows, boardOrientation) {\n    return boardOrientation === 'white'\n        ? (noOfRows - row).toString()\n        : (row + 1).toString();\n}\nfunction columnIndexToChessColumn(column, noOfColumns, boardOrientation) {\n    return boardOrientation === 'white'\n        ? String.fromCharCode(97 + column)\n        : String.fromCharCode(97 + noOfColumns - column - 1);\n}\nfunction chessColumnToColumnIndex(column, noOfColumns, boardOrientation) {\n    return boardOrientation === 'white'\n        ? column.charCodeAt(0) - 97\n        : noOfColumns - (column.charCodeAt(0) - 97) - 1;\n}\nfunction chessRowToRowIndex(row, noOfRows, boardOrientation) {\n    return boardOrientation === 'white'\n        ? noOfRows - Number(row)\n        : Number(row) - 1;\n}\nfunction fenStringToPositionObject(fen, noOfRows, noOfColumns) {\n    const positionObject = {};\n    const rows = fen.split(' ')[0].split('/');\n    // rows start from top of the board (black rank) in white orientation, and bottom of the board (white rank) in black orientation\n    for (let row = 0; row < rows.length; row++) {\n        let column = 0;\n        for (const char of rows[row]) {\n            // if char is a letter, it is a piece\n            if (isNaN(Number(char))) {\n                // force orientation to flip fen string when black orientation used\n                const position = `${columnIndexToChessColumn(column, noOfColumns, 'white')}${rowIndexToChessRow(row, noOfRows, 'white')}`;\n                // set piece at position (e.g. 0-0 for a8 on a normal board)\n                positionObject[position] = {\n                    pieceType: fenToPieceCode(char),\n                };\n                // increment column for next piece\n                column++;\n            }\n            else {\n                // if char is a number, it is empty squares, skip that many columns\n                column += Number(char);\n            }\n        }\n    }\n    return positionObject;\n}\n/**\n * Convert fen piece code (e.g. p, N) to camel case notation (e.g. bP, wK).\n */\nfunction fenToPieceCode(piece) {\n    // lower case is black piece\n    if (piece.toLowerCase() === piece) {\n        return 'b' + piece.toUpperCase();\n    }\n    // upper case is white piece\n    return 'w' + piece.toUpperCase();\n}\n// todo: if already in updates, find next candidate\n/**\n * Return an object with the pieces that have moved from the old position to the new position.\n * The keys are the source square names (e.g. \"e2\") and the values are the new square positions (e.g. \"e4\"), indicating that the piece in square \"e2\" has moved to square \"e4\".\n */\nfunction getPositionUpdates(oldPosition, newPosition, noOfColumns, boardOrientation) {\n    const updates = {};\n    for (const newSquare in newPosition) {\n        const candidateSquares = [];\n        // the piece hasn't moved, so we don't need to do anything\n        if (oldPosition[newSquare]?.pieceType === newPosition[newSquare].pieceType) {\n            continue;\n        }\n        for (const oldSquare in oldPosition) {\n            // if the piece type is the same, and the new square is not the old square, and the piece has moved, then we have found a candidate for the new position\n            if (oldPosition[oldSquare].pieceType === newPosition[newSquare].pieceType &&\n                oldSquare !== newSquare &&\n                oldPosition[oldSquare].pieceType !== newPosition[oldSquare]?.pieceType) {\n                candidateSquares.push(oldSquare);\n            }\n        }\n        if (candidateSquares.length === 1) {\n            // if there is only one candidate, we can just return it\n            updates[candidateSquares[0]] = newSquare;\n        }\n        else {\n            // if there are multiple candidates, we need to find the one that is correct to the best of our ability by standard chess rules\n            for (const candidateSquare of candidateSquares) {\n                // get the piece type of the candidate e.g. 'P', 'N', 'B', 'R', 'Q', 'K'\n                const candidatePieceType = oldPosition[candidateSquare].pieceType[1];\n                const columnDifference = Math.abs(chessColumnToColumnIndex(candidateSquare.match(/^[a-z]+/)?.[0] ?? '', noOfColumns, boardOrientation) -\n                    chessColumnToColumnIndex(newSquare.match(/^[a-z]+/)?.[0] ?? '', noOfColumns, boardOrientation));\n                const rowDifference = Math.abs(Number(candidateSquare.match(/\\d+$/)?.[0] ?? '') -\n                    Number(newSquare.match(/\\d+$/)?.[0] ?? ''));\n                const isOldSquareLight = (chessColumnToColumnIndex(candidateSquare.match(/^[a-z]+/)?.[0] ?? '', noOfColumns, boardOrientation) +\n                    Number(candidateSquare.match(/\\d+$/)?.[0] ?? '')) %\n                    2 ===\n                    0;\n                const isNewSquareLight = (chessColumnToColumnIndex(newSquare.match(/^[a-z]+/)?.[0] ?? '', noOfColumns, boardOrientation) +\n                    Number(newSquare.match(/\\d+$/)?.[0] ?? '')) %\n                    2 ===\n                    0;\n                // prioritise pawns on same file\n                if (candidatePieceType === 'P') {\n                    if (candidateSquare.match(/^[a-z]+/)?.[0] ===\n                        newSquare.match(/^[a-z]+/)?.[0]) {\n                        updates[candidateSquare] = newSquare;\n                        break;\n                    }\n                }\n                // prioritise knights by euclidean distance\n                if (candidatePieceType === 'N') {\n                    if ((columnDifference === 2 && rowDifference === 1) ||\n                        (columnDifference === 1 && rowDifference === 2)) {\n                        updates[candidateSquare] = newSquare;\n                        break;\n                    }\n                }\n                // prioritise bishops that have moved diagonally and are on the same color square\n                if (candidatePieceType === 'B') {\n                    if (columnDifference === rowDifference &&\n                        isOldSquareLight === isNewSquareLight) {\n                        updates[candidateSquare] = newSquare;\n                        break;\n                    }\n                }\n                // prioritise rooks that have moved horizontally or vertically\n                if (candidatePieceType === 'R') {\n                    if (columnDifference === 0 || rowDifference === 0) {\n                        updates[candidateSquare] = newSquare;\n                        break;\n                    }\n                }\n                // prioritise queens that have moved diagonally, horizontally or vertically\n                if (candidatePieceType === 'Q') {\n                    if (columnDifference === 0 ||\n                        rowDifference === 0 ||\n                        columnDifference === rowDifference) {\n                        updates[candidateSquare] = newSquare;\n                        break;\n                    }\n                }\n                // prioritise kings that have moved one square in any direction\n                if (candidatePieceType === 'K') {\n                    if (columnDifference <= 1 && rowDifference <= 1) {\n                        updates[candidateSquare] = newSquare;\n                        break;\n                    }\n                }\n            }\n            // if we still don't have a candidate, use the first candidate that has not been used yet\n            if (!Object.values(updates).includes(newSquare) &&\n                candidateSquares.length > 0) {\n                for (const candidateSquare of candidateSquares) {\n                    if (!Object.keys(updates).includes(candidateSquare)) {\n                        updates[candidateSquare] = newSquare;\n                        break;\n                    }\n                }\n            }\n        }\n    }\n    return updates;\n}\n/**\n * Retrieves the coordinates at the centre of the requested square, relative to the top left of the board (0, 0).\n */\nfunction getRelativeCoords(boardOrientation, boardWidth, chessboardColumns, chessboardRows, square) {\n    const squareWidth = boardWidth / chessboardColumns;\n    const x = chessColumnToColumnIndex(square.match(/^[a-z]+/)?.[0] ?? '', chessboardColumns, boardOrientation) *\n        squareWidth +\n        squareWidth / 2;\n    const y = chessRowToRowIndex(square.match(/\\d+$/)?.[0] ?? '', chessboardRows, boardOrientation) *\n        squareWidth +\n        squareWidth / 2;\n    return { x, y };\n}\n\nconst defaultPieces = {\n    wP: (props) => (jsxRuntimeExports.jsx(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", version: \"1.1\", viewBox: \"0 0 45 45\", width: \"100%\", height: \"100%\", style: props?.svgStyle, children: jsxRuntimeExports.jsx(\"path\", { d: \"m 22.5,9 c -2.21,0 -4,1.79 -4,4 0,0.89 0.29,1.71 0.78,2.38 C 17.33,16.5 16,18.59 16,21 c 0,2.03 0.94,3.84 2.41,5.03 C 15.41,27.09 11,31.58 11,39.5 H 34 C 34,31.58 29.59,27.09 26.59,26.03 28.06,24.84 29,23.03 29,21 29,18.59 27.67,16.5 25.72,15.38 26.21,14.71 26.5,13.89 26.5,13 c 0,-2.21 -1.79,-4 -4,-4 z\", style: {\n                opacity: '1',\n                fill: props?.fill ?? '#ffffff',\n                fillOpacity: '1',\n                fillRule: 'nonzero',\n                stroke: '#000000',\n                strokeWidth: '1.5',\n                strokeLinecap: 'round',\n                strokeLinejoin: 'miter',\n                strokeMiterlimit: '4',\n                strokeDasharray: 'none',\n                strokeOpacity: '1',\n            } }) })),\n    wR: (props) => (jsxRuntimeExports.jsx(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", version: \"1.1\", viewBox: \"0 0 45 45\", width: \"100%\", height: \"100%\", style: props?.svgStyle, children: jsxRuntimeExports.jsxs(\"g\", { style: {\n                opacity: '1',\n                fill: props?.fill ?? '#ffffff',\n                fillOpacity: '1',\n                fillRule: 'evenodd',\n                stroke: '#000000',\n                strokeWidth: '1.5',\n                strokeLinecap: 'round',\n                strokeLinejoin: 'round',\n                strokeMiterlimit: '4',\n                strokeDasharray: 'none',\n                strokeOpacity: '1',\n            }, children: [jsxRuntimeExports.jsx(\"path\", { d: \"M 9,39 L 36,39 L 36,36 L 9,36 L 9,39 z \", style: { strokeLinecap: 'butt' } }), jsxRuntimeExports.jsx(\"path\", { d: \"M 12,36 L 12,32 L 33,32 L 33,36 L 12,36 z \", style: { strokeLinecap: 'butt' } }), jsxRuntimeExports.jsx(\"path\", { d: \"M 11,14 L 11,9 L 15,9 L 15,11 L 20,11 L 20,9 L 25,9 L 25,11 L 30,11 L 30,9 L 34,9 L 34,14\", style: { strokeLinecap: 'butt' } }), jsxRuntimeExports.jsx(\"path\", { d: \"M 34,14 L 31,17 L 14,17 L 11,14\" }), jsxRuntimeExports.jsx(\"path\", { d: \"M 31,17 L 31,29.5 L 14,29.5 L 14,17\", style: { strokeLinecap: 'butt', strokeLinejoin: 'miter' } }), jsxRuntimeExports.jsx(\"path\", { d: \"M 31,29.5 L 32.5,32 L 12.5,32 L 14,29.5\" }), jsxRuntimeExports.jsx(\"path\", { d: \"M 11,14 L 34,14\", style: { fill: 'none', stroke: '#000000', strokeLinejoin: 'miter' } })] }) })),\n    wN: (props) => (jsxRuntimeExports.jsx(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", version: \"1.1\", viewBox: \"0 0 45 45\", width: \"100%\", height: \"100%\", style: props?.svgStyle, children: jsxRuntimeExports.jsxs(\"g\", { style: {\n                opacity: '1',\n                fill: 'none',\n                fillOpacity: '1',\n                fillRule: 'evenodd',\n                stroke: '#000000',\n                strokeWidth: '1.5',\n                strokeLinecap: 'round',\n                strokeLinejoin: 'round',\n                strokeMiterlimit: '4',\n                strokeDasharray: 'none',\n                strokeOpacity: '1',\n            }, children: [jsxRuntimeExports.jsx(\"path\", { d: \"M 22,10 C 32.5,11 38.5,18 38,39 L 15,39 C 15,30 25,32.5 23,18\", style: { fill: props?.fill ?? '#ffffff', stroke: '#000000' } }), jsxRuntimeExports.jsx(\"path\", { d: \"M 24,18 C 24.38,20.91 18.45,25.37 16,27 C 13,29 13.18,31.34 11,31 C 9.958,30.06 12.41,27.96 11,28 C 10,28 11.19,29.23 10,30 C 9,30 5.997,31 6,26 C 6,24 12,14 12,14 C 12,14 13.89,12.1 14,10.5 C 13.27,9.506 13.5,8.5 13.5,7.5 C 14.5,6.5 16.5,10 16.5,10 L 18.5,10 C 18.5,10 19.28,8.008 21,7 C 22,7 22,10 22,10\", style: { fill: props?.fill ?? '#ffffff', stroke: '#000000' } }), jsxRuntimeExports.jsx(\"path\", { d: \"M 9.5 25.5 A 0.5 0.5 0 1 1 8.5,25.5 A 0.5 0.5 0 1 1 9.5 25.5 z\", style: { fill: '#000000', stroke: '#000000' } }), jsxRuntimeExports.jsx(\"path\", { d: \"M 15 15.5 A 0.5 1.5 0 1 1  14,15.5 A 0.5 1.5 0 1 1  15 15.5 z\", transform: \"matrix(0.866,0.5,-0.5,0.866,9.693,-5.173)\", style: { fill: '#000000', stroke: '#000000' } })] }) })),\n    wB: (props) => (jsxRuntimeExports.jsx(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", version: \"1.1\", viewBox: \"0 0 45 45\", width: \"100%\", height: \"100%\", style: props?.svgStyle, children: jsxRuntimeExports.jsxs(\"g\", { style: {\n                opacity: '1',\n                fill: 'none',\n                fillRule: 'evenodd',\n                fillOpacity: '1',\n                stroke: '#000000',\n                strokeWidth: '1.5',\n                strokeLinecap: 'round',\n                strokeLinejoin: 'round',\n                strokeMiterlimit: '4',\n                strokeDasharray: 'none',\n                strokeOpacity: '1',\n            }, children: [jsxRuntimeExports.jsxs(\"g\", { style: {\n                        fill: props?.fill ?? '#ffffff',\n                        stroke: '#000000',\n                        strokeLinecap: 'butt',\n                    }, children: [jsxRuntimeExports.jsx(\"path\", { d: \"M 9,36 C 12.39,35.03 19.11,36.43 22.5,34 C 25.89,36.43 32.61,35.03 36,36 C 36,36 37.65,36.54 39,38 C 38.32,38.97 37.35,38.99 36,38.5 C 32.61,37.53 25.89,38.96 22.5,37.5 C 19.11,38.96 12.39,37.53 9,38.5 C 7.65,38.99 6.68,38.97 6,38 C 7.35,36.54 9,36 9,36 z\" }), jsxRuntimeExports.jsx(\"path\", { d: \"M 15,32 C 17.5,34.5 27.5,34.5 30,32 C 30.5,30.5 30,30 30,30 C 30,27.5 27.5,26 27.5,26 C 33,24.5 33.5,14.5 22.5,10.5 C 11.5,14.5 12,24.5 17.5,26 C 17.5,26 15,27.5 15,30 C 15,30 14.5,30.5 15,32 z\" }), jsxRuntimeExports.jsx(\"path\", { d: \"M 25 8 A 2.5 2.5 0 1 1  20,8 A 2.5 2.5 0 1 1  25 8 z\" })] }), jsxRuntimeExports.jsx(\"path\", { d: \"M 17.5,26 L 27.5,26 M 15,30 L 30,30 M 22.5,15.5 L 22.5,20.5 M 20,18 L 25,18\", style: { fill: 'none', stroke: '#000000', strokeLinejoin: 'miter' } })] }) })),\n    wQ: (props) => (jsxRuntimeExports.jsx(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", version: \"1.1\", viewBox: \"0 0 45 45\", width: \"100%\", height: \"100%\", style: props?.svgStyle, children: jsxRuntimeExports.jsxs(\"g\", { style: {\n                fill: props?.fill ?? '#ffffff',\n                stroke: '#000000',\n                strokeWidth: '1.5',\n                strokeLinejoin: 'round',\n            }, children: [jsxRuntimeExports.jsx(\"path\", { d: \"M 9,26 C 17.5,24.5 30,24.5 36,26 L 38.5,13.5 L 31,25 L 30.7,10.9 L 25.5,24.5 L 22.5,10 L 19.5,24.5 L 14.3,10.9 L 14,25 L 6.5,13.5 L 9,26 z\" }), jsxRuntimeExports.jsx(\"path\", { d: \"M 9,26 C 9,28 10.5,28 11.5,30 C 12.5,31.5 12.5,31 12,33.5 C 10.5,34.5 11,36 11,36 C 9.5,37.5 11,38.5 11,38.5 C 17.5,39.5 27.5,39.5 34,38.5 C 34,38.5 35.5,37.5 34,36 C 34,36 34.5,34.5 33,33.5 C 32.5,31 32.5,31.5 33.5,30 C 34.5,28 36,28 36,26 C 27.5,24.5 17.5,24.5 9,26 z\" }), jsxRuntimeExports.jsx(\"path\", { d: \"M 11.5,30 C 15,29 30,29 33.5,30\", style: { fill: 'none' } }), jsxRuntimeExports.jsx(\"path\", { d: \"M 12,33.5 C 18,32.5 27,32.5 33,33.5\", style: { fill: 'none' } }), jsxRuntimeExports.jsx(\"circle\", { cx: \"6\", cy: \"12\", r: \"2\" }), jsxRuntimeExports.jsx(\"circle\", { cx: \"14\", cy: \"9\", r: \"2\" }), jsxRuntimeExports.jsx(\"circle\", { cx: \"22.5\", cy: \"8\", r: \"2\" }), jsxRuntimeExports.jsx(\"circle\", { cx: \"31\", cy: \"9\", r: \"2\" }), jsxRuntimeExports.jsx(\"circle\", { cx: \"39\", cy: \"12\", r: \"2\" })] }) })),\n    wK: (props) => (jsxRuntimeExports.jsx(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", version: \"1.1\", viewBox: \"0 0 45 45\", width: \"100%\", height: \"100%\", style: props?.svgStyle, children: jsxRuntimeExports.jsxs(\"g\", { style: {\n                fill: 'none',\n                fillOpacity: '1',\n                fillRule: 'evenodd',\n                stroke: '#000000',\n                strokeWidth: '1.5',\n                strokeLinecap: 'round',\n                strokeLinejoin: 'round',\n                strokeMiterlimit: '4',\n                strokeDasharray: 'none',\n                strokeOpacity: '1',\n            }, children: [jsxRuntimeExports.jsx(\"path\", { d: \"M 22.5,11.63 L 22.5,6\", style: { fill: 'none', stroke: '#000000', strokeLinejoin: 'miter' } }), jsxRuntimeExports.jsx(\"path\", { d: \"M 20,8 L 25,8\", style: { fill: 'none', stroke: '#000000', strokeLinejoin: 'miter' } }), jsxRuntimeExports.jsx(\"path\", { d: \"M 22.5,25 C 22.5,25 27,17.5 25.5,14.5 C 25.5,14.5 24.5,12 22.5,12 C 20.5,12 19.5,14.5 19.5,14.5 C 18,17.5 22.5,25 22.5,25\", style: {\n                        fill: props?.fill ?? '#ffffff',\n                        stroke: '#000000',\n                        strokeLinecap: 'butt',\n                        strokeLinejoin: 'miter',\n                    } }), jsxRuntimeExports.jsx(\"path\", { d: \"M 12.5,37 C 18,40.5 27,40.5 32.5,37 L 32.5,30 C 32.5,30 41.5,25.5 38.5,19.5 C 34.5,13 25,16 22.5,23.5 L 22.5,27 L 22.5,23.5 C 20,16 10.5,13 6.5,19.5 C 3.5,25.5 12.5,30 12.5,30 L 12.5,37\", style: { fill: props?.fill ?? '#ffffff', stroke: '#000000' } }), jsxRuntimeExports.jsx(\"path\", { d: \"M 12.5,30 C 18,27 27,27 32.5,30\", style: { fill: 'none', stroke: '#000000' } }), jsxRuntimeExports.jsx(\"path\", { d: \"M 12.5,33.5 C 18,30.5 27,30.5 32.5,33.5\", style: { fill: 'none', stroke: '#000000' } }), jsxRuntimeExports.jsx(\"path\", { d: \"M 12.5,37 C 18,34 27,34 32.5,37\", style: { fill: 'none', stroke: '#000000' } })] }) })),\n    bP: (props) => (jsxRuntimeExports.jsx(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", version: \"1.1\", viewBox: \"0 0 45 45\", width: \"100%\", height: \"100%\", style: props?.svgStyle, children: jsxRuntimeExports.jsx(\"path\", { d: \"m 22.5,9 c -2.21,0 -4,1.79 -4,4 0,0.89 0.29,1.71 0.78,2.38 C 17.33,16.5 16,18.59 16,21 c 0,2.03 0.94,3.84 2.41,5.03 C 15.41,27.09 11,31.58 11,39.5 H 34 C 34,31.58 29.59,27.09 26.59,26.03 28.06,24.84 29,23.03 29,21 29,18.59 27.67,16.5 25.72,15.38 26.21,14.71 26.5,13.89 26.5,13 c 0,-2.21 -1.79,-4 -4,-4 z\", style: {\n                opacity: '1',\n                fill: props?.fill ?? '#000000',\n                fillOpacity: '1',\n                fillRule: 'nonzero',\n                stroke: '#000000',\n                strokeWidth: '1.5',\n                strokeLinecap: 'round',\n                strokeLinejoin: 'miter',\n                strokeMiterlimit: '4',\n                strokeDasharray: 'none',\n                strokeOpacity: '1',\n            } }) })),\n    bR: (props) => (jsxRuntimeExports.jsx(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", version: \"1.1\", viewBox: \"0 0 45 45\", width: \"100%\", height: \"100%\", style: props?.svgStyle, children: jsxRuntimeExports.jsxs(\"g\", { style: {\n                opacity: '1',\n                fill: props?.fill ?? '#000000',\n                fillOpacity: '1',\n                fillRule: 'evenodd',\n                stroke: '#000000',\n                strokeWidth: '1.5',\n                strokeLinecap: 'round',\n                strokeLinejoin: 'round',\n                strokeMiterlimit: '4',\n                strokeDasharray: 'none',\n                strokeOpacity: '1',\n            }, children: [jsxRuntimeExports.jsx(\"path\", { d: \"M 9,39 L 36,39 L 36,36 L 9,36 L 9,39 z \", style: { strokeLinecap: 'butt' } }), jsxRuntimeExports.jsx(\"path\", { d: \"M 12.5,32 L 14,29.5 L 31,29.5 L 32.5,32 L 12.5,32 z \", style: { strokeLinecap: 'butt' } }), jsxRuntimeExports.jsx(\"path\", { d: \"M 12,36 L 12,32 L 33,32 L 33,36 L 12,36 z \", style: { strokeLinecap: 'butt' } }), jsxRuntimeExports.jsx(\"path\", { d: \"M 14,29.5 L 14,16.5 L 31,16.5 L 31,29.5 L 14,29.5 z \", style: { strokeLinecap: 'butt', strokeLinejoin: 'miter' } }), jsxRuntimeExports.jsx(\"path\", { d: \"M 14,16.5 L 11,14 L 34,14 L 31,16.5 L 14,16.5 z \", style: { strokeLinecap: 'butt' } }), jsxRuntimeExports.jsx(\"path\", { d: \"M 11,14 L 11,9 L 15,9 L 15,11 L 20,11 L 20,9 L 25,9 L 25,11 L 30,11 L 30,9 L 34,9 L 34,14 L 11,14 z \", style: { strokeLinecap: 'butt' } }), jsxRuntimeExports.jsx(\"path\", { d: \"M 12,35.5 L 33,35.5 L 33,35.5\", style: {\n                        fill: 'none',\n                        stroke: '#ffffff',\n                        strokeWidth: '1',\n                        strokeLinejoin: 'miter',\n                    } }), jsxRuntimeExports.jsx(\"path\", { d: \"M 13,31.5 L 32,31.5\", style: {\n                        fill: 'none',\n                        stroke: '#ffffff',\n                        strokeWidth: '1',\n                        strokeLinejoin: 'miter',\n                    } }), jsxRuntimeExports.jsx(\"path\", { d: \"M 14,29.5 L 31,29.5\", style: {\n                        fill: 'none',\n                        stroke: '#ffffff',\n                        strokeWidth: '1',\n                        strokeLinejoin: 'miter',\n                    } }), jsxRuntimeExports.jsx(\"path\", { d: \"M 14,16.5 L 31,16.5\", style: {\n                        fill: 'none',\n                        stroke: '#ffffff',\n                        strokeWidth: '1',\n                        strokeLinejoin: 'miter',\n                    } }), jsxRuntimeExports.jsx(\"path\", { d: \"M 11,14 L 34,14\", style: {\n                        fill: 'none',\n                        stroke: '#ffffff',\n                        strokeWidth: '1',\n                        strokeLinejoin: 'miter',\n                    } })] }) })),\n    bN: (props) => (jsxRuntimeExports.jsx(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", version: \"1.1\", viewBox: \"0 0 45 45\", width: \"100%\", height: \"100%\", style: props?.svgStyle, children: jsxRuntimeExports.jsxs(\"g\", { style: {\n                opacity: '1',\n                fill: 'none',\n                fillOpacity: '1',\n                fillRule: 'evenodd',\n                stroke: '#000000',\n                strokeWidth: '1.5',\n                strokeLinecap: 'round',\n                strokeLinejoin: 'round',\n                strokeMiterlimit: '4',\n                strokeDasharray: 'none',\n                strokeOpacity: '1',\n            }, children: [jsxRuntimeExports.jsx(\"path\", { d: \"M 22,10 C 32.5,11 38.5,18 38,39 L 15,39 C 15,30 25,32.5 23,18\", style: { fill: props?.fill ?? '#000000', stroke: '#000000' } }), jsxRuntimeExports.jsx(\"path\", { d: \"M 24,18 C 24.38,20.91 18.45,25.37 16,27 C 13,29 13.18,31.34 11,31 C 9.958,30.06 12.41,27.96 11,28 C 10,28 11.19,29.23 10,30 C 9,30 5.997,31 6,26 C 6,24 12,14 12,14 C 12,14 13.89,12.1 14,10.5 C 13.27,9.506 13.5,8.5 13.5,7.5 C 14.5,6.5 16.5,10 16.5,10 L 18.5,10 C 18.5,10 19.28,8.008 21,7 C 22,7 22,10 22,10\", style: { fill: props?.fill ?? '#000000', stroke: '#000000' } }), jsxRuntimeExports.jsx(\"path\", { d: \"M 9.5 25.5 A 0.5 0.5 0 1 1 8.5,25.5 A 0.5 0.5 0 1 1 9.5 25.5 z\", style: { fill: '#ffffff', stroke: '#ffffff' } }), jsxRuntimeExports.jsx(\"path\", { d: \"M 15 15.5 A 0.5 1.5 0 1 1  14,15.5 A 0.5 1.5 0 1 1  15 15.5 z\", transform: \"matrix(0.866,0.5,-0.5,0.866,9.693,-5.173)\", style: { fill: '#ffffff', stroke: '#ffffff' } }), jsxRuntimeExports.jsx(\"path\", { d: \"M 24.55,10.4 L 24.1,11.85 L 24.6,12 C 27.75,13 30.25,14.49 32.5,18.75 C 34.75,23.01 35.75,29.06 35.25,39 L 35.2,39.5 L 37.45,39.5 L 37.5,39 C 38,28.94 36.62,22.15 34.25,17.66 C 31.88,13.17 28.46,11.02 25.06,10.5 L 24.55,10.4 z \", style: { fill: '#ffffff', stroke: 'none' } })] }) })),\n    bB: (props) => (jsxRuntimeExports.jsx(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", version: \"1.1\", viewBox: \"0 0 45 45\", width: \"100%\", height: \"100%\", style: props?.svgStyle, children: jsxRuntimeExports.jsxs(\"g\", { style: {\n                opacity: '1',\n                fill: 'none',\n                fillRule: 'evenodd',\n                fillOpacity: '1',\n                stroke: '#000000',\n                strokeWidth: '1.5',\n                strokeLinecap: 'round',\n                strokeLinejoin: 'round',\n                strokeMiterlimit: '4',\n                strokeDasharray: 'none',\n                strokeOpacity: '1',\n            }, children: [jsxRuntimeExports.jsxs(\"g\", { style: {\n                        fill: props?.fill ?? '#000000',\n                        stroke: '#000000',\n                        strokeLinecap: 'butt',\n                    }, children: [jsxRuntimeExports.jsx(\"path\", { d: \"M 9,36 C 12.39,35.03 19.11,36.43 22.5,34 C 25.89,36.43 32.61,35.03 36,36 C 36,36 37.65,36.54 39,38 C 38.32,38.97 37.35,38.99 36,38.5 C 32.61,37.53 25.89,38.96 22.5,37.5 C 19.11,38.96 12.39,37.53 9,38.5 C 7.65,38.99 6.68,38.97 6,38 C 7.35,36.54 9,36 9,36 z\" }), jsxRuntimeExports.jsx(\"path\", { d: \"M 15,32 C 17.5,34.5 27.5,34.5 30,32 C 30.5,30.5 30,30 30,30 C 30,27.5 27.5,26 27.5,26 C 33,24.5 33.5,14.5 22.5,10.5 C 11.5,14.5 12,24.5 17.5,26 C 17.5,26 15,27.5 15,30 C 15,30 14.5,30.5 15,32 z\" }), jsxRuntimeExports.jsx(\"path\", { d: \"M 25 8 A 2.5 2.5 0 1 1  20,8 A 2.5 2.5 0 1 1  25 8 z\" })] }), jsxRuntimeExports.jsx(\"path\", { d: \"M 17.5,26 L 27.5,26 M 15,30 L 30,30 M 22.5,15.5 L 22.5,20.5 M 20,18 L 25,18\", style: { fill: 'none', stroke: '#ffffff', strokeLinejoin: 'miter' } })] }) })),\n    bQ: (props) => (jsxRuntimeExports.jsx(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", version: \"1.1\", viewBox: \"0 0 45 45\", width: \"100%\", height: \"100%\", style: props?.svgStyle, children: jsxRuntimeExports.jsxs(\"g\", { style: {\n                fill: props?.fill ?? '#000000',\n                stroke: '#000000',\n                strokeWidth: '1.5',\n                strokeLinecap: 'round',\n                strokeLinejoin: 'round',\n            }, children: [jsxRuntimeExports.jsx(\"path\", { d: \"M 9,26 C 17.5,24.5 30,24.5 36,26 L 38.5,13.5 L 31,25 L 30.7,10.9 L 25.5,24.5 L 22.5,10 L 19.5,24.5 L 14.3,10.9 L 14,25 L 6.5,13.5 L 9,26 z\", style: { strokeLinecap: 'butt', fill: props?.fill ?? '#000000' } }), jsxRuntimeExports.jsx(\"path\", { d: \"m 9,26 c 0,2 1.5,2 2.5,4 1,1.5 1,1 0.5,3.5 -1.5,1 -1,2.5 -1,2.5 -1.5,1.5 0,2.5 0,2.5 6.5,1 16.5,1 23,0 0,0 1.5,-1 0,-2.5 0,0 0.5,-1.5 -1,-2.5 -0.5,-2.5 -0.5,-2 0.5,-3.5 1,-2 2.5,-2 2.5,-4 -8.5,-1.5 -18.5,-1.5 -27,0 z\" }), jsxRuntimeExports.jsx(\"path\", { d: \"M 11.5,30 C 15,29 30,29 33.5,30\" }), jsxRuntimeExports.jsx(\"path\", { d: \"m 12,33.5 c 6,-1 15,-1 21,0\" }), jsxRuntimeExports.jsx(\"circle\", { cx: \"6\", cy: \"12\", r: \"2\" }), jsxRuntimeExports.jsx(\"circle\", { cx: \"14\", cy: \"9\", r: \"2\" }), jsxRuntimeExports.jsx(\"circle\", { cx: \"22.5\", cy: \"8\", r: \"2\" }), jsxRuntimeExports.jsx(\"circle\", { cx: \"31\", cy: \"9\", r: \"2\" }), jsxRuntimeExports.jsx(\"circle\", { cx: \"39\", cy: \"12\", r: \"2\" }), jsxRuntimeExports.jsx(\"path\", { d: \"M 11,38.5 A 35,35 1 0 0 34,38.5\", style: { fill: 'none', stroke: '#000000', strokeLinecap: 'butt' } }), jsxRuntimeExports.jsxs(\"g\", { style: { fill: 'none', stroke: '#ffffff' }, children: [jsxRuntimeExports.jsx(\"path\", { d: \"M 11,29 A 35,35 1 0 1 34,29\" }), jsxRuntimeExports.jsx(\"path\", { d: \"M 12.5,31.5 L 32.5,31.5\" }), jsxRuntimeExports.jsx(\"path\", { d: \"M 11.5,34.5 A 35,35 1 0 0 33.5,34.5\" }), jsxRuntimeExports.jsx(\"path\", { d: \"M 10.5,37.5 A 35,35 1 0 0 34.5,37.5\" })] })] }) })),\n    bK: (props) => (jsxRuntimeExports.jsx(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", version: \"1.1\", viewBox: \"0 0 45 45\", width: \"100%\", height: \"100%\", style: props?.svgStyle, children: jsxRuntimeExports.jsxs(\"g\", { style: {\n                fill: 'none',\n                fillOpacity: '1',\n                fillRule: 'evenodd',\n                stroke: '#000000',\n                strokeWidth: '1.5',\n                strokeLinecap: 'round',\n                strokeLinejoin: 'round',\n                strokeMiterlimit: '4',\n                strokeDasharray: 'none',\n                strokeOpacity: '1',\n            }, children: [jsxRuntimeExports.jsx(\"path\", { d: \"M 22.5,11.63 L 22.5,6\", style: { fill: 'none', stroke: '#000000', strokeLinejoin: 'miter' }, id: \"path6570\" }), jsxRuntimeExports.jsx(\"path\", { d: \"M 22.5,25 C 22.5,25 27,17.5 25.5,14.5 C 25.5,14.5 24.5,12 22.5,12 C 20.5,12 19.5,14.5 19.5,14.5 C 18,17.5 22.5,25 22.5,25\", style: {\n                        fill: props?.fill ?? '#000000',\n                        fillOpacity: '1',\n                        strokeLinecap: 'butt',\n                        strokeLinejoin: 'miter',\n                    } }), jsxRuntimeExports.jsx(\"path\", { d: \"M 12.5,37 C 18,40.5 27,40.5 32.5,37 L 32.5,30 C 32.5,30 41.5,25.5 38.5,19.5 C 34.5,13 25,16 22.5,23.5 L 22.5,27 L 22.5,23.5 C 20,16 10.5,13 6.5,19.5 C 3.5,25.5 12.5,30 12.5,30 L 12.5,37\", style: { fill: props?.fill ?? '#000000', stroke: '#000000' } }), jsxRuntimeExports.jsx(\"path\", { d: \"M 20,8 L 25,8\", style: { fill: 'none', stroke: '#000000', strokeLinejoin: 'miter' } }), jsxRuntimeExports.jsx(\"path\", { d: \"M 32,29.5 C 32,29.5 40.5,25.5 38.03,19.85 C 34.15,14 25,18 22.5,24.5 L 22.5,26.6 L 22.5,24.5 C 20,18 10.85,14 6.97,19.85 C 4.5,25.5 13,29.5 13,29.5\", style: { fill: 'none', stroke: '#ffffff' } }), jsxRuntimeExports.jsx(\"path\", { d: \"M 12.5,30 C 18,27 27,27 32.5,30 M 12.5,33.5 C 18,30.5 27,30.5 32.5,33.5 M 12.5,37 C 18,34 27,34 32.5,37\", style: { fill: 'none', stroke: '#ffffff' } })] }) })),\n};\n\nfunction defaultBoardStyle(chessboardColumns) {\n    return {\n        display: 'grid',\n        gridTemplateColumns: `repeat(${chessboardColumns}, 1fr)`,\n        overflow: 'hidden',\n        width: '100%',\n        height: '100%',\n        position: 'relative',\n    };\n}\nconst defaultSquareStyle = {\n    aspectRatio: '1/1',\n    display: 'flex',\n    justifyContent: 'center',\n    alignItems: 'center',\n    position: 'relative',\n};\nconst defaultDarkSquareStyle = {\n    backgroundColor: '#B58863',\n};\nconst defaultLightSquareStyle = {\n    backgroundColor: '#F0D9B5',\n};\nconst defaultDropSquareStyle = {\n    boxShadow: 'inset 0px 0px 0px 1px black',\n};\nconst defaultDarkSquareNotationStyle = {\n    color: '#F0D9B5',\n};\nconst defaultLightSquareNotationStyle = {\n    color: '#B58863',\n};\nconst defaultAlphaNotationStyle = {\n    fontSize: '13px',\n    position: 'absolute',\n    bottom: 1,\n    right: 4,\n    userSelect: 'none',\n};\nconst defaultNumericNotationStyle = {\n    fontSize: '13px',\n    position: 'absolute',\n    top: 2,\n    left: 2,\n    userSelect: 'none',\n};\nconst defaultDraggingPieceStyle = {\n    transform: 'scale(1.2)',\n};\nconst defaultDraggingPieceGhostStyle = {\n    opacity: 0.5,\n};\nconst defaultArrowOptions = {\n    color: '#ffaa00', // color if no modifiers are held down when drawing an arrow\n    secondaryColor: '#4caf50', // color if shift is held down when drawing an arrow\n    tertiaryColor: '#f44336', // color if control is held down when drawing an arrow\n    arrowLengthReducerDenominator: 8, // the lower the denominator, the greater the arrow length reduction (e.g. 8 = 1/8 of a square width removed, 4 = 1/4 of a square width removed)\n    sameTargetArrowLengthReducerDenominator: 4, // as above but for arrows targeting the same square (a greater reduction is used to avoid overlaps)\n    arrowWidthDenominator: 5, // the lower the denominator, the greater the arrow width (e.g. 5 = 1/5 of a square width, 10 = 1/10 of a square width)\n    activeArrowWidthMultiplier: 0.9, // the multiplier for the arrow width when it is being drawn\n    opacity: 0.65, // opacity of arrow when not being drawn\n    activeOpacity: 0.5, // opacity of arrow when it is being drawn\n};\n\nconst ChessboardContext = createContext(null);\nconst useChessboardContext = () => use(ChessboardContext);\nfunction ChessboardProvider({ children, options, }) {\n    const { \n    // id\n    id = 'chessboard', \n    // pieces and position\n    pieces = defaultPieces, position = 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR', \n    // board dimensions and orientation\n    boardOrientation = 'white', chessboardRows = 8, chessboardColumns = 8, \n    // board and squares styles\n    boardStyle = defaultBoardStyle(chessboardColumns), squareStyle = defaultSquareStyle, squareStyles = {}, darkSquareStyle = defaultDarkSquareStyle, lightSquareStyle = defaultLightSquareStyle, dropSquareStyle = defaultDropSquareStyle, draggingPieceStyle = defaultDraggingPieceStyle, draggingPieceGhostStyle = defaultDraggingPieceGhostStyle, \n    // notation\n    darkSquareNotationStyle = defaultDarkSquareNotationStyle, lightSquareNotationStyle = defaultLightSquareNotationStyle, alphaNotationStyle = defaultAlphaNotationStyle, numericNotationStyle = defaultNumericNotationStyle, showNotation = true, \n    // animation\n    animationDurationInMs = 300, showAnimations = true, \n    // drag and drop\n    allowDragging = true, allowDragOffBoard = true, dragActivationDistance = 1, \n    // arrows\n    allowDrawingArrows = true, arrows = [], arrowOptions = defaultArrowOptions, clearArrowsOnClick = true, \n    // handlers\n    canDragPiece, onMouseOutSquare, onMouseOverSquare, onPieceClick, onPieceDrag, onPieceDrop, onSquareClick, onSquareRightClick, squareRenderer, } = options || {};\n    // the piece currently being dragged\n    const [draggingPiece, setDraggingPiece] = useState(null);\n    // the current position of pieces on the chessboard\n    const [currentPosition, setCurrentPosition] = useState(typeof position === 'string'\n        ? fenStringToPositionObject(position, chessboardRows, chessboardColumns)\n        : position);\n    // calculated differences between current and incoming positions\n    const [positionDifferences, setPositionDifferences] = useState({});\n    // if the latest move was a manual drop\n    const [manuallyDroppedPieceAndSquare, setManuallyDroppedPieceAndSquare] = useState(null);\n    // arrows\n    const [newArrowStartSquare, setNewArrowStartSquare] = useState(null);\n    const [newArrowOverSquare, setNewArrowOverSquare] = useState(null);\n    const [internalArrows, setInternalArrows] = useState([]);\n    // position we are animating to, if a new position comes in before the animation completes, we will use this to set the new position\n    const [waitingForAnimationPosition, setWaitingForAnimationPosition] = useState(null);\n    // the animation timeout whilst waiting for animation to complete\n    const animationTimeoutRef = useRef(null);\n    // if the position changes, we need to recreate the pieces array\n    useEffect(() => {\n        const newPosition = typeof position === 'string'\n            ? fenStringToPositionObject(position, chessboardRows, chessboardColumns)\n            : position;\n        // if no animation, just set the position\n        if (!showAnimations) {\n            setCurrentPosition(newPosition);\n            return;\n        }\n        // save copy of the waiting for animation position so we can use it later but clear it from state so we don't use it in the next animation\n        const currentWaitingForAnimationPosition = waitingForAnimationPosition;\n        // if we are waiting for an animation to complete from a previous move, set the saved position to immediately end the animation\n        if (currentWaitingForAnimationPosition) {\n            setCurrentPosition(currentWaitingForAnimationPosition);\n            setWaitingForAnimationPosition(null);\n        }\n        // get list of position updates as pieces to potentially animate\n        const positionUpdates = getPositionUpdates(currentWaitingForAnimationPosition ?? currentPosition, // use the saved position if it exists, otherwise use the current position\n        newPosition, chessboardColumns, boardOrientation);\n        const multiplePiecesMoved = Object.keys(positionUpdates).length > 1;\n        // manually dropped piece caused multiple pieces to move (e.g. castling)\n        if (manuallyDroppedPieceAndSquare && multiplePiecesMoved) {\n            // create a new position with just the dropped piece moved\n            const intermediatePosition = { ...currentPosition };\n            delete intermediatePosition[manuallyDroppedPieceAndSquare.sourceSquare];\n            intermediatePosition[manuallyDroppedPieceAndSquare.targetSquare] = {\n                pieceType: manuallyDroppedPieceAndSquare.piece,\n            };\n            setCurrentPosition(intermediatePosition);\n            // create position differences with only the other pieces' movements\n            const otherPiecesUpdates = { ...positionUpdates };\n            delete otherPiecesUpdates[manuallyDroppedPieceAndSquare.sourceSquare];\n            setPositionDifferences(otherPiecesUpdates);\n            // animate the other pieces' movements\n            const newTimeout = setTimeout(() => {\n                setCurrentPosition(newPosition);\n                setPositionDifferences({});\n                setManuallyDroppedPieceAndSquare(null);\n            }, animationDurationInMs);\n            animationTimeoutRef.current = newTimeout;\n            return;\n        }\n        // new position was a result of a manual drop\n        if (manuallyDroppedPieceAndSquare) {\n            // no animation needed, just set the position and reset the flag\n            setCurrentPosition(newPosition);\n            setManuallyDroppedPieceAndSquare(null);\n            return;\n        }\n        // new position was a result of an external move\n        setPositionDifferences(positionUpdates);\n        setWaitingForAnimationPosition(newPosition);\n        // start animation timeout\n        const newTimeout = setTimeout(() => {\n            setCurrentPosition(newPosition);\n            setPositionDifferences({});\n            setWaitingForAnimationPosition(null);\n        }, animationDurationInMs);\n        // update the ref to the new timeout\n        animationTimeoutRef.current = newTimeout;\n        // clear timeout on unmount\n        return () => {\n            if (animationTimeoutRef.current) {\n                clearTimeout(animationTimeoutRef.current);\n            }\n        };\n    }, [position]);\n    // if the dimensions change, we need to recreate the pieces array\n    useEffect(() => {\n        setCurrentPosition(typeof position === 'string'\n            ? fenStringToPositionObject(position, chessboardRows, chessboardColumns)\n            : position);\n    }, [chessboardRows, chessboardColumns, boardOrientation]);\n    // only redraw the board when the dimensions or board orientation change\n    const board = useMemo(() => generateBoard(chessboardRows, chessboardColumns, boardOrientation), [chessboardRows, chessboardColumns, boardOrientation]);\n    const drawArrow = useCallback((newArrowEndSquare, modifiers) => {\n        if (!allowDrawingArrows) {\n            return;\n        }\n        const arrowExistsIndex = internalArrows.findIndex((arrow) => arrow.startSquare === newArrowStartSquare &&\n            arrow.endSquare === newArrowEndSquare);\n        const arrowExistsExternally = arrows.some((arrow) => arrow.startSquare === newArrowStartSquare &&\n            arrow.endSquare === newArrowEndSquare);\n        // if the arrow already exists externally, don't add it to the internal arrows\n        if (arrowExistsExternally) {\n            setNewArrowStartSquare(null);\n            setNewArrowOverSquare(null);\n            return;\n        }\n        // new arrow with different start and end square, add to internal arrows or remove if it already exists\n        if (newArrowStartSquare && newArrowStartSquare !== newArrowEndSquare) {\n            const arrowColor = modifiers?.shiftKey\n                ? arrowOptions.secondaryColor\n                : modifiers?.ctrlKey\n                    ? arrowOptions.tertiaryColor\n                    : arrowOptions.color;\n            setInternalArrows((prevArrows) => arrowExistsIndex === -1\n                ? [\n                    ...prevArrows,\n                    {\n                        startSquare: newArrowStartSquare,\n                        endSquare: newArrowEndSquare,\n                        color: arrowColor,\n                    },\n                ]\n                : prevArrows.filter((_, index) => index !== arrowExistsIndex));\n            setNewArrowStartSquare(null);\n            setNewArrowOverSquare(null);\n        }\n    }, [\n        allowDrawingArrows,\n        arrows,\n        arrowOptions.color,\n        arrowOptions.secondaryColor,\n        arrowOptions.tertiaryColor,\n        internalArrows,\n        newArrowStartSquare,\n        newArrowOverSquare,\n    ]);\n    const clearArrows = useCallback(() => {\n        if (clearArrowsOnClick) {\n            setInternalArrows([]);\n            setNewArrowStartSquare(null);\n            setNewArrowOverSquare(null);\n        }\n    }, [clearArrowsOnClick]);\n    const setNewArrowOverSquareWithModifiers = useCallback((square, modifiers) => {\n        const color = modifiers?.shiftKey\n            ? arrowOptions.secondaryColor\n            : modifiers?.ctrlKey\n                ? arrowOptions.tertiaryColor\n                : arrowOptions.color;\n        setNewArrowOverSquare({ square, color });\n    }, [arrowOptions]);\n    const handleDragCancel = useCallback(() => {\n        setDraggingPiece(null);\n    }, []);\n    const handleDragEnd = useCallback(function handleDragEnd(event) {\n        if (!draggingPiece) {\n            return;\n        }\n        const dropSquare = event.over?.id.toString();\n        // dropped outside of droppable area (e.g. off board)\n        if (!dropSquare) {\n            onPieceDrop?.({\n                piece: draggingPiece,\n                sourceSquare: draggingPiece.position,\n                targetSquare: null,\n            });\n            // set as manually dropped piece so that no animation is shown\n            setManuallyDroppedPieceAndSquare({\n                piece: draggingPiece.pieceType,\n                sourceSquare: draggingPiece.position,\n                targetSquare: '',\n            });\n            setDraggingPiece(null);\n            return;\n        }\n        if (event.over) {\n            const isDropValid = onPieceDrop?.({\n                piece: draggingPiece,\n                sourceSquare: draggingPiece.position,\n                targetSquare: dropSquare,\n            });\n            // if the drop is valid, set the manually dropped piece and square\n            if (isDropValid) {\n                setManuallyDroppedPieceAndSquare({\n                    piece: draggingPiece.pieceType,\n                    sourceSquare: draggingPiece.position,\n                    targetSquare: dropSquare,\n                });\n            }\n            setDraggingPiece(null);\n        }\n    }, [draggingPiece]);\n    const handleDragStart = useCallback(\n    // active.id is the id of the piece being dragged\n    function handleDragStart({ active }) {\n        // the id is either the position of the piece on the board if it's on the board (e.g. \"a1\", \"b2\", etc.), or the type of the piece if it's a spare piece (e.g. \"wP\", \"bN\", etc.)\n        const isSparePiece = active.data.current?.isSparePiece;\n        onPieceDrag?.({\n            isSparePiece,\n            piece: isSparePiece\n                ? {\n                    pieceType: active.id,\n                }\n                : currentPosition[active.id],\n            square: isSparePiece ? null : active.id,\n        });\n        setDraggingPiece({\n            isSparePiece,\n            position: active.id,\n            pieceType: isSparePiece\n                ? active.id\n                : currentPosition[active.id].pieceType,\n        });\n        return;\n    }, [currentPosition]);\n    const sensors = useSensors(useSensor(PointerSensor, {\n        activationConstraint: {\n            distance: dragActivationDistance,\n        },\n    }), useSensor(KeyboardSensor), useSensor(TouchSensor), useSensor(MouseSensor));\n    // collision detection that first tries pointer-based detection and then falls back to rectangle intersection for keyboards\n    function collisionDetection(args) {\n        // first try pointer-based collision detection\n        const pointerCollisions = pointerWithin(args);\n        // if we found collisions with the pointer, return those\n        if (pointerCollisions.length > 0) {\n            return pointerCollisions;\n        }\n        // otherwise fall back to rectangle intersection\n        return rectIntersection(args);\n    }\n    return (jsxRuntimeExports.jsx(ChessboardContext.Provider, { value: {\n            // chessboard options\n            id,\n            pieces,\n            boardOrientation,\n            chessboardRows,\n            chessboardColumns,\n            boardStyle,\n            squareStyle,\n            squareStyles,\n            darkSquareStyle,\n            lightSquareStyle,\n            dropSquareStyle,\n            draggingPieceStyle,\n            draggingPieceGhostStyle,\n            darkSquareNotationStyle,\n            lightSquareNotationStyle,\n            alphaNotationStyle,\n            numericNotationStyle,\n            showNotation,\n            animationDurationInMs,\n            showAnimations,\n            allowDragging,\n            allowDragOffBoard,\n            allowDrawingArrows,\n            arrows,\n            arrowOptions,\n            canDragPiece,\n            onMouseOutSquare,\n            onMouseOverSquare,\n            onPieceClick,\n            onSquareClick,\n            onSquareRightClick,\n            squareRenderer,\n            // internal state\n            board,\n            isWrapped: true,\n            draggingPiece,\n            currentPosition,\n            positionDifferences,\n            newArrowStartSquare,\n            newArrowOverSquare,\n            setNewArrowStartSquare,\n            setNewArrowOverSquare: setNewArrowOverSquareWithModifiers,\n            internalArrows,\n            drawArrow,\n            clearArrows,\n        }, children: jsxRuntimeExports.jsx(DndContext, { collisionDetection: collisionDetection, onDragStart: handleDragStart, onDragEnd: handleDragEnd, onDragCancel: handleDragCancel, sensors: sensors, children: children }) }));\n}\n\nfunction Arrows({ boardWidth, boardHeight }) {\n    const { id, arrows, arrowOptions, boardOrientation, chessboardColumns, chessboardRows, internalArrows, newArrowStartSquare, newArrowOverSquare, } = useChessboardContext();\n    if (!boardWidth) {\n        return null;\n    }\n    const currentlyDrawingArrow = newArrowStartSquare &&\n        newArrowOverSquare &&\n        newArrowStartSquare !== newArrowOverSquare.square\n        ? {\n            startSquare: newArrowStartSquare,\n            endSquare: newArrowOverSquare.square,\n            color: newArrowOverSquare.color,\n        }\n        : null;\n    const arrowsToDraw = currentlyDrawingArrow\n        ? [...arrows, ...internalArrows, currentlyDrawingArrow]\n        : [...arrows, ...internalArrows];\n    return (jsxRuntimeExports.jsx(\"svg\", { width: boardWidth, height: boardHeight, style: {\n            position: 'absolute',\n            top: '0',\n            left: '0',\n            pointerEvents: 'none',\n            zIndex: '20', // place above pieces\n        }, children: arrowsToDraw.map((arrow, i) => {\n            const from = getRelativeCoords(boardOrientation, boardWidth, chessboardColumns, chessboardRows, arrow.startSquare);\n            const to = getRelativeCoords(boardOrientation, boardWidth, chessboardColumns, chessboardRows, arrow.endSquare);\n            // we want to shorten the arrow length so the tip of the arrow is more central to the target square instead of running over the center\n            const squareWidth = boardWidth / chessboardColumns;\n            let ARROW_LENGTH_REDUCER = squareWidth / arrowOptions.arrowLengthReducerDenominator;\n            const isArrowActive = currentlyDrawingArrow && i === arrowsToDraw.length - 1;\n            // if there are different arrows targeting the same square make their length a bit shorter\n            if (arrowsToDraw.some((restArrow) => restArrow.startSquare !== arrow.startSquare &&\n                restArrow.endSquare === arrow.endSquare) &&\n                !isArrowActive) {\n                ARROW_LENGTH_REDUCER =\n                    squareWidth / arrowOptions.sameTargetArrowLengthReducerDenominator;\n            }\n            // Calculate the difference in x and y coordinates between start and end points\n            const dx = to.x - from.x;\n            const dy = to.y - from.y;\n            // Calculate the total distance between points using Pythagorean theorem\n            // This gives us the length of the arrow if it went from center to center\n            const r = Math.hypot(dy, dx);\n            // Calculate the new end point for the arrow\n            // We subtract ARROW_LENGTH_REDUCER from the total distance to make the arrow\n            // stop before reaching the center of the target square\n            const end = {\n                // Calculate new end x coordinate by:\n                // 1. Taking the original x direction (dx)\n                // 2. Scaling it by (r - ARROW_LENGTH_REDUCER) / r to shorten it\n                // 3. Adding to the starting x coordinate\n                x: from.x + (dx * (r - ARROW_LENGTH_REDUCER)) / r,\n                // Same calculation for y coordinate\n                y: from.y + (dy * (r - ARROW_LENGTH_REDUCER)) / r,\n            };\n            return (jsxRuntimeExports.jsxs(Fragment, { children: [jsxRuntimeExports.jsx(\"marker\", { id: `${id}-arrowhead-${i}-${arrow.startSquare}-${arrow.endSquare}`, markerWidth: \"2\", markerHeight: \"2.5\", refX: \"1.25\", refY: \"1.25\", orient: \"auto\", children: jsxRuntimeExports.jsx(\"polygon\", { points: \"0.3 0, 2 1.25, 0.3 2.5\", fill: arrow.color }) }), jsxRuntimeExports.jsx(\"line\", { x1: from.x, y1: from.y, x2: end.x, y2: end.y, opacity: isArrowActive\n                            ? arrowOptions.activeOpacity\n                            : arrowOptions.opacity, stroke: arrow.color, strokeWidth: isArrowActive\n                            ? arrowOptions.activeArrowWidthMultiplier *\n                                (squareWidth / arrowOptions.arrowWidthDenominator)\n                            : squareWidth / arrowOptions.arrowWidthDenominator, markerEnd: `url(#${id}-arrowhead-${i}-${arrow.startSquare}-${arrow.endSquare})` })] }, `${id}-arrow-${arrow.startSquare}-${arrow.endSquare}${isArrowActive ? '-active' : ''}`));\n        }) }));\n}\n\nfunction Draggable({ children, isSparePiece = false, pieceType, position, }) {\n    const { allowDragging, canDragPiece } = useChessboardContext();\n    const { setNodeRef, attributes, listeners } = useDraggable({\n        id: position,\n        data: {\n            isSparePiece,\n            pieceType,\n        },\n        disabled: !allowDragging ||\n            (canDragPiece &&\n                !canDragPiece({\n                    piece: { pieceType },\n                    isSparePiece,\n                    square: position,\n                })),\n    });\n    return (jsxRuntimeExports.jsx(\"div\", { ref: setNodeRef, ...attributes, ...listeners, children: children }));\n}\n\nfunction Droppable({ children, squareId }) {\n    const { isOver, setNodeRef } = useDroppable({\n        id: squareId,\n    });\n    return jsxRuntimeExports.jsx(\"div\", { ref: setNodeRef, children: children({ isOver }) });\n}\n\nconst Piece = memo(function Piece({ clone, isSparePiece = false, position, pieceType, }) {\n    const { id, allowDragging, animationDurationInMs, boardOrientation, canDragPiece, draggingPiece, draggingPieceStyle, draggingPieceGhostStyle, pieces, positionDifferences, onPieceClick, } = useChessboardContext();\n    const [animationStyle, setAnimationStyle] = useState({});\n    let cursorStyle = clone ? 'grabbing' : 'grab';\n    if (!allowDragging ||\n        (canDragPiece &&\n            !canDragPiece({ piece: { pieceType }, isSparePiece, square: position }))) {\n        cursorStyle = 'pointer';\n    }\n    useEffect(() => {\n        if (positionDifferences[position]) {\n            const sourceSquare = position;\n            const targetSquare = positionDifferences[position];\n            const squareWidth = document\n                .querySelector(`#${id}-square-${sourceSquare}`)\n                ?.getBoundingClientRect().width;\n            if (!squareWidth) {\n                throw new Error('Square width not found');\n            }\n            setAnimationStyle({\n                transform: `translate(${(boardOrientation === 'black' ? -1 : 1) *\n                    (targetSquare.charCodeAt(0) - sourceSquare.charCodeAt(0)) *\n                    squareWidth}px, ${(boardOrientation === 'black' ? -1 : 1) *\n                    (Number(sourceSquare[1]) - Number(targetSquare[1])) *\n                    squareWidth}px)`,\n                transition: `transform ${animationDurationInMs}ms`,\n                position: 'relative', // creates a new stacking context so the piece stays above squares during animation\n                zIndex: 10,\n            });\n        }\n        else {\n            setAnimationStyle({});\n        }\n    }, [positionDifferences]);\n    const PieceSvg = pieces[pieceType];\n    return (jsxRuntimeExports.jsx(\"div\", { id: `${id}-piece-${pieceType}-${position}`, \"data-piece\": pieceType, style: {\n            ...animationStyle,\n            ...(clone\n                ? { ...defaultDraggingPieceStyle, ...draggingPieceStyle }\n                : {}),\n            ...(!clone && draggingPiece?.position === position\n                ? { ...defaultDraggingPieceGhostStyle, ...draggingPieceGhostStyle }\n                : {}),\n            width: '100%',\n            height: '100%',\n            cursor: cursorStyle,\n            touchAction: 'none', // prevent zooming and scrolling on touch devices\n        }, onClick: () => onPieceClick?.({ isSparePiece, piece: { pieceType }, square: position }), children: jsxRuntimeExports.jsx(PieceSvg, {}) }));\n});\n\nconst Square = memo(function Square({ children, squareId, isLightSquare, isOver, }) {\n    const { id, allowDrawingArrows, boardOrientation, chessboardColumns, chessboardRows, currentPosition, squareStyle, squareStyles, darkSquareStyle, lightSquareStyle, dropSquareStyle, darkSquareNotationStyle, lightSquareNotationStyle, alphaNotationStyle, numericNotationStyle, showNotation, onMouseOutSquare, onMouseOverSquare, onSquareClick, onSquareRightClick, squareRenderer, newArrowStartSquare, setNewArrowStartSquare, setNewArrowOverSquare, drawArrow, clearArrows, } = useChessboardContext();\n    const column = squareId.match(/^[a-z]+/)?.[0];\n    const row = squareId.match(/\\d+$/)?.[0];\n    return (jsxRuntimeExports.jsxs(\"div\", { id: `${id}-square-${squareId}`, style: {\n            ...defaultSquareStyle,\n            ...squareStyle,\n            ...(isLightSquare\n                ? { ...defaultLightSquareStyle, ...lightSquareStyle }\n                : { ...defaultDarkSquareStyle, ...darkSquareStyle }),\n            ...(isOver ? { ...defaultDropSquareStyle, ...dropSquareStyle } : {}),\n        }, \"data-column\": column, \"data-row\": row, \"data-square\": squareId, onClick: (e) => {\n            if (e.button === 0) {\n                onSquareClick?.({\n                    piece: currentPosition[squareId] ?? null,\n                    square: squareId,\n                });\n            }\n        }, onContextMenu: (e) => {\n            e.preventDefault();\n            onSquareRightClick?.({\n                piece: currentPosition[squareId] ?? null,\n                square: squareId,\n            });\n        }, onMouseDown: (e) => {\n            if (e.button === 0) {\n                clearArrows();\n            }\n            if (e.button === 2 && allowDrawingArrows) {\n                setNewArrowStartSquare(squareId);\n            }\n        }, onMouseUp: (e) => {\n            if (e.button === 2) {\n                if (newArrowStartSquare) {\n                    drawArrow(squareId, {\n                        shiftKey: e.shiftKey,\n                        ctrlKey: e.ctrlKey,\n                    });\n                }\n            }\n        }, onMouseOver: (e) => {\n            // right mouse button is held down and we are drawing an arrow\n            if (e.buttons === 2 && newArrowStartSquare) {\n                setNewArrowOverSquare(squareId, {\n                    shiftKey: e.shiftKey,\n                    ctrlKey: e.ctrlKey,\n                });\n            }\n            onMouseOverSquare?.({\n                piece: currentPosition[squareId] ?? null,\n                square: squareId,\n            });\n        }, onMouseLeave: () => onMouseOutSquare?.({\n            piece: currentPosition[squareId] ?? null,\n            square: squareId,\n        }), children: [showNotation ? (jsxRuntimeExports.jsxs(\"span\", { style: isLightSquare\n                    ? {\n                        ...defaultLightSquareNotationStyle,\n                        ...lightSquareNotationStyle,\n                    }\n                    : {\n                        ...defaultDarkSquareNotationStyle,\n                        ...darkSquareNotationStyle,\n                    }, children: [row ===\n                        (boardOrientation === 'white'\n                            ? '1'\n                            : chessboardRows.toString()) && (jsxRuntimeExports.jsx(\"span\", { style: { ...defaultAlphaNotationStyle, ...alphaNotationStyle }, children: column })), column ===\n                        (boardOrientation === 'white'\n                            ? 'a'\n                            : columnIndexToChessColumn(0, chessboardColumns, boardOrientation)) && (jsxRuntimeExports.jsx(\"span\", { style: {\n                            ...defaultNumericNotationStyle,\n                            ...numericNotationStyle,\n                        }, children: row }))] })) : null, squareRenderer?.({\n                piece: currentPosition[squareId] ?? null,\n                square: squareId,\n                children,\n            }) || (jsxRuntimeExports.jsx(\"div\", { style: {\n                    width: '100%',\n                    height: '100%',\n                    ...squareStyles[squareId],\n                }, children: children }))] }));\n});\n\nconst preventDragOffBoard = (boardId, draggingPiecePosition) => {\n    return ({ transform }) => {\n        const boardElement = typeof document !== 'undefined'\n            ? document.getElementById(`${boardId}-board`)\n            : null;\n        if (!boardElement) {\n            return transform;\n        }\n        // Get the a1 square to determine square size using data attributes\n        const boardRect = boardElement.getBoundingClientRect();\n        const a1Square = boardElement.querySelector('[data-column=\"a\"][data-row=\"1\"]');\n        if (!a1Square) {\n            return transform;\n        }\n        const squareWidth = a1Square.getBoundingClientRect().width;\n        const halfSquareWidth = squareWidth / 2;\n        // Extract column and row from position (supports multi-char columns/rows)\n        const match = draggingPiecePosition.match(/^([a-zA-Z]+)(\\d+)$/);\n        if (!match) {\n            return transform;\n        }\n        const [, col, row] = match;\n        // Get the starting position of the piece\n        const startSquare = boardElement.querySelector(`[data-column=\"${col}\"][data-row=\"${row}\"]`);\n        if (!startSquare) {\n            return transform;\n        }\n        const startSquareRect = startSquare.getBoundingClientRect();\n        const startX = startSquareRect.left + halfSquareWidth - boardRect.left;\n        const startY = startSquareRect.top + halfSquareWidth - boardRect.top;\n        // Clamp so the center of the piece can go exactly half a square width outside the board\n        const minX = -startX;\n        const maxX = boardRect.width - startX;\n        const minY = -startY;\n        const maxY = boardRect.height - startY;\n        const clampedX = Math.min(Math.max(transform.x, minX), maxX);\n        const clampedY = Math.min(Math.max(transform.y, minY), maxY);\n        return {\n            ...transform,\n            x: clampedX,\n            y: clampedY,\n        };\n    };\n};\n\nfunction Board() {\n    const { allowDragOffBoard, board, boardStyle, chessboardColumns, currentPosition, draggingPiece, id, } = useChessboardContext();\n    const boardRef = useRef(null);\n    const [boardWidth, setBoardWidth] = useState(boardRef.current?.clientWidth);\n    const [boardHeight, setBoardHeight] = useState(boardRef.current?.clientHeight);\n    // if the board dimensions change, update the board width and height\n    useEffect(() => {\n        if (boardRef.current) {\n            const resizeObserver = new ResizeObserver(() => {\n                setBoardWidth(boardRef.current?.clientWidth);\n                setBoardHeight(boardRef.current?.clientHeight);\n            });\n            resizeObserver.observe(boardRef.current);\n            return () => {\n                resizeObserver.disconnect();\n            };\n        }\n    }, [boardRef.current]);\n    return (jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [jsxRuntimeExports.jsxs(\"div\", { id: `${id}-board`, ref: boardRef, style: { ...defaultBoardStyle(chessboardColumns), ...boardStyle }, children: [board.map((row) => row.map((square) => {\n                        const piece = currentPosition[square.squareId];\n                        return (jsxRuntimeExports.jsx(Droppable, { squareId: square.squareId, children: ({ isOver }) => (jsxRuntimeExports.jsx(Square, { isOver: isOver, ...square, children: piece ? (jsxRuntimeExports.jsx(Draggable, { isSparePiece: false, position: square.squareId, pieceType: piece.pieceType, children: jsxRuntimeExports.jsx(Piece, { ...piece, position: square.squareId }) })) : null })) }, square.squareId));\n                    })), jsxRuntimeExports.jsx(Arrows, { boardWidth: boardWidth, boardHeight: boardHeight })] }), jsxRuntimeExports.jsx(DragOverlay, { dropAnimation: null, modifiers: [\n                    snapCenterToCursor,\n                    ...(allowDragOffBoard\n                        ? []\n                        : [preventDragOffBoard(id, draggingPiece?.position || '')]),\n                ], children: draggingPiece ? (jsxRuntimeExports.jsx(Piece, { clone: true, position: draggingPiece.position, pieceType: draggingPiece.pieceType })) : null })] }));\n}\n\nfunction Chessboard({ options }) {\n    const { isWrapped } = useChessboardContext() ?? { isWrapped: false };\n    if (isWrapped) {\n        return jsxRuntimeExports.jsx(Board, {});\n    }\n    return (jsxRuntimeExports.jsx(ChessboardProvider, { options: options, children: jsxRuntimeExports.jsx(Board, {}) }));\n}\n\nfunction SparePiece({ pieceType }) {\n    return (jsxRuntimeExports.jsx(Draggable, { isSparePiece: true, position: pieceType, pieceType: pieceType, children: jsxRuntimeExports.jsx(Piece, { isSparePiece: true, pieceType: pieceType, position: pieceType }) }));\n}\n\nexport { Chessboard, ChessboardProvider, SparePiece, chessColumnToColumnIndex, chessRowToRowIndex, columnIndexToChessColumn, defaultAlphaNotationStyle, defaultArrowOptions, defaultBoardStyle, defaultDarkSquareNotationStyle, defaultDarkSquareStyle, defaultDraggingPieceGhostStyle, defaultDraggingPieceStyle, defaultDropSquareStyle, defaultLightSquareNotationStyle, defaultLightSquareStyle, defaultNumericNotationStyle, defaultPieces, defaultSquareStyle, fenStringToPositionObject, generateBoard, getPositionUpdates, getRelativeCoords, rowIndexToChessRow, useChessboardContext };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEA,IAAI,aAAa;IAAC,SAAS,CAAC;AAAC;AAE7B,IAAI,6BAA6B,CAAC;AAElC;;;;;;;;CAQC,GAED,IAAI;AAEJ,SAAS;IACR,IAAI,uCAAuC,OAAO;IAClD,wCAAwC;IACxC,IAAI,qBAAqB,OAAO,GAAG,CAAC,+BAClC,sBAAsB,OAAO,GAAG,CAAC;IACnC,SAAS,QAAQ,IAAI,EAAE,MAAM,EAAE,QAAQ;QACrC,IAAI,MAAM;QACV,KAAK,MAAM,YAAY,CAAC,MAAM,KAAK,QAAQ;QAC3C,KAAK,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,KAAK,OAAO,GAAG;QAC/C,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,SAAS,SAAS,GAAG;QACrB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,KAAK,KAAK,MAAM,SAAS,SAAS;YAClC,OAAO;QACT;IACF;IACA,2BAA2B,QAAQ,GAAG;IACtC,2BAA2B,GAAG,GAAG;IACjC,2BAA2B,IAAI,GAAG;IAClC,OAAO;AACR;AAEA,IAAI,8BAA8B,CAAC;AAEnC;;;;;;;;CAQC,GAED,IAAI;AAEJ,SAAS;IACR,IAAI,wCAAwC,OAAO;IACnD,yCAAyC;IACzC,oEACE,AAAC;QACC,SAAS,yBAAyB,IAAI;YACpC,IAAI,QAAQ,MAAM,OAAO;YACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,2BACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;YACvC,IAAI,aAAa,OAAO,MAAM,OAAO;YACrC,OAAQ;gBACN,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;YACX;YACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;gBAEb,KAAK;oBACH,OAAO,CAAC,KAAK,WAAW,IAAI,SAAS,IAAI;gBAC3C,KAAK;oBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;gBACpD,KAAK;oBACH,IAAI,YAAY,KAAK,MAAM;oBAC3B,OAAO,KAAK,WAAW;oBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;oBAClE,OAAO;gBACT,KAAK;oBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;gBAE/C,KAAK;oBACH,YAAY,KAAK,QAAQ;oBACzB,OAAO,KAAK,KAAK;oBACjB,IAAI;wBACF,OAAO,yBAAyB,KAAK;oBACvC,EAAE,OAAO,GAAG,CAAC;YACjB;YACF,OAAO;QACT;QACA,SAAS,mBAAmB,KAAK;YAC/B,OAAO,KAAK;QACd;QACA,SAAS,uBAAuB,KAAK;YACnC,IAAI;gBACF,mBAAmB;gBACnB,IAAI,2BAA2B,CAAC;YAClC,EAAE,OAAO,GAAG;gBACV,2BAA2B;YAC7B;YACA,IAAI,0BAA0B;gBAC5B,2BAA2B;gBAC3B,IAAI,wBAAwB,yBAAyB,KAAK;gBAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;gBACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;gBAEF,OAAO,mBAAmB;YAC5B;QACF;QACA,SAAS,eAAe;QACxB,SAAS;YACP,IAAI,MAAM,eAAe;gBACvB,UAAU,QAAQ,GAAG;gBACrB,WAAW,QAAQ,IAAI;gBACvB,WAAW,QAAQ,IAAI;gBACvB,YAAY,QAAQ,KAAK;gBACzB,YAAY,QAAQ,KAAK;gBACzB,qBAAqB,QAAQ,cAAc;gBAC3C,eAAe,QAAQ,QAAQ;gBAC/B,IAAI,QAAQ;oBACV,cAAc;oBACd,YAAY;oBACZ,OAAO;oBACP,UAAU;gBACZ;gBACA,OAAO,gBAAgB,CAAC,SAAS;oBAC/B,MAAM;oBACN,KAAK;oBACL,MAAM;oBACN,OAAO;oBACP,OAAO;oBACP,gBAAgB;oBAChB,UAAU;gBACZ;YACF;YACA;QACF;QACA,SAAS;YACP;YACA,IAAI,MAAM,eAAe;gBACvB,IAAI,QAAQ;oBAAE,cAAc;oBAAM,YAAY;oBAAM,UAAU;gBAAK;gBACnE,OAAO,gBAAgB,CAAC,SAAS;oBAC/B,KAAK,OAAO,CAAC,GAAG,OAAO;wBAAE,OAAO;oBAAQ;oBACxC,MAAM,OAAO,CAAC,GAAG,OAAO;wBAAE,OAAO;oBAAS;oBAC1C,MAAM,OAAO,CAAC,GAAG,OAAO;wBAAE,OAAO;oBAAS;oBAC1C,OAAO,OAAO,CAAC,GAAG,OAAO;wBAAE,OAAO;oBAAU;oBAC5C,OAAO,OAAO,CAAC,GAAG,OAAO;wBAAE,OAAO;oBAAU;oBAC5C,gBAAgB,OAAO,CAAC,GAAG,OAAO;wBAAE,OAAO;oBAAmB;oBAC9D,UAAU,OAAO,CAAC,GAAG,OAAO;wBAAE,OAAO;oBAAa;gBACpD;YACF;YACA,IAAI,iBACF,QAAQ,KAAK,CACX;QAEN;QACA,SAAS,8BAA8B,IAAI;YACzC,IAAI,KAAK,MAAM,QACb,IAAI;gBACF,MAAM;YACR,EAAE,OAAO,GAAG;gBACV,IAAI,QAAQ,EAAE,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC;gBACjC,SAAS,AAAC,SAAS,KAAK,CAAC,EAAE,IAAK;gBAChC,SACE,CAAC,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,cACjB,mBACA,CAAC,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,OACnB,iBACA;YACV;YACF,OAAO,OAAO,SAAS,OAAO;QAChC;QACA,SAAS,6BAA6B,EAAE,EAAE,SAAS;YACjD,IAAI,CAAC,MAAM,SAAS,OAAO;YAC3B,IAAI,QAAQ,oBAAoB,GAAG,CAAC;YACpC,IAAI,KAAK,MAAM,OAAO,OAAO;YAC7B,UAAU;YACV,QAAQ,MAAM,iBAAiB;YAC/B,MAAM,iBAAiB,GAAG,KAAK;YAC/B,IAAI,qBAAqB;YACzB,qBAAqB,qBAAqB,CAAC;YAC3C,qBAAqB,CAAC,GAAG;YACzB;YACA,IAAI;gBACF,IAAI,iBAAiB;oBACnB,6BAA6B;wBAC3B,IAAI;4BACF,IAAI,WAAW;gCACb,IAAI,OAAO;oCACT,MAAM;gCACR;gCACA,OAAO,cAAc,CAAC,KAAK,SAAS,EAAE,SAAS;oCAC7C,KAAK;wCACH,MAAM;oCACR;gCACF;gCACA,IAAI,aAAa,OAAO,WAAW,QAAQ,SAAS,EAAE;oCACpD,IAAI;wCACF,QAAQ,SAAS,CAAC,MAAM,EAAE;oCAC5B,EAAE,OAAO,GAAG;wCACV,IAAI,UAAU;oCAChB;oCACA,QAAQ,SAAS,CAAC,IAAI,EAAE,EAAE;gCAC5B,OAAO;oCACL,IAAI;wCACF,KAAK,IAAI;oCACX,EAAE,OAAO,KAAK;wCACZ,UAAU;oCACZ;oCACA,GAAG,IAAI,CAAC,KAAK,SAAS;gCACxB;4BACF,OAAO;gCACL,IAAI;oCACF,MAAM;gCACR,EAAE,OAAO,KAAK;oCACZ,UAAU;gCACZ;gCACA,CAAC,OAAO,IAAI,KACV,eAAe,OAAO,KAAK,KAAK,IAChC,KAAK,KAAK,CAAC,YAAa;4BAC5B;wBACF,EAAE,OAAO,QAAQ;4BACf,IAAI,UAAU,WAAW,aAAa,OAAO,OAAO,KAAK,EACvD,OAAO;gCAAC,OAAO,KAAK;gCAAE,QAAQ,KAAK;6BAAC;wBACxC;wBACA,OAAO;4BAAC;4BAAM;yBAAK;oBACrB;gBACF;gBACA,eAAe,2BAA2B,CAAC,WAAW,GACpD;gBACF,IAAI,qBAAqB,OAAO,wBAAwB,CACtD,eAAe,2BAA2B,EAC1C;gBAEF,sBACE,mBAAmB,YAAY,IAC/B,OAAO,cAAc,CACnB,eAAe,2BAA2B,EAC1C,QACA;oBAAE,OAAO;gBAA8B;gBAE3C,IAAI,wBACA,eAAe,2BAA2B,IAC5C,cAAc,qBAAqB,CAAC,EAAE,EACtC,eAAe,qBAAqB,CAAC,EAAE;gBACzC,IAAI,eAAe,cAAc;oBAC/B,IAAI,cAAc,YAAY,KAAK,CAAC,OAClC,eAAe,aAAa,KAAK,CAAC;oBACpC,IACE,wBAAwB,qBAAqB,GAC7C,qBAAqB,YAAY,MAAM,IACvC,CAAC,WAAW,CAAC,mBAAmB,CAAC,QAAQ,CACvC,gCAIF;oBACF,MAEE,wBAAwB,aAAa,MAAM,IAC3C,CAAC,YAAY,CAAC,sBAAsB,CAAC,QAAQ,CAC3C,gCAIF;oBACF,IACE,uBAAuB,YAAY,MAAM,IACzC,0BAA0B,aAAa,MAAM,EAE7C,IACE,qBAAqB,YAAY,MAAM,GAAG,GACxC,wBAAwB,aAAa,MAAM,GAAG,GAChD,KAAK,sBACL,KAAK,yBACL,WAAW,CAAC,mBAAmB,KAC7B,YAAY,CAAC,sBAAsB,EAGrC;oBACJ,MAEE,KAAK,sBAAsB,KAAK,uBAChC,sBAAsB,wBAEtB,IACE,WAAW,CAAC,mBAAmB,KAC/B,YAAY,CAAC,sBAAsB,EACnC;wBACA,IAAI,MAAM,sBAAsB,MAAM,uBAAuB;4BAC3D,GACE,IACG,sBACD,yBACA,IAAI,yBACF,WAAW,CAAC,mBAAmB,KAC7B,YAAY,CAAC,sBAAsB,EACvC;gCACA,IAAI,SACF,OACA,WAAW,CAAC,mBAAmB,CAAC,OAAO,CACrC,YACA;gCAEJ,GAAG,WAAW,IACZ,OAAO,QAAQ,CAAC,kBAChB,CAAC,SAAS,OAAO,OAAO,CAAC,eAAe,GAAG,WAAW,CAAC;gCACzD,eAAe,OAAO,MACpB,oBAAoB,GAAG,CAAC,IAAI;gCAC9B,OAAO;4BACT;mCACK,KAAK,sBAAsB,KAAK,sBAAuB;wBAChE;wBACA;oBACF;gBACJ;YACF,SAAU;gBACP,UAAU,OACR,qBAAqB,CAAC,GAAG,oBAC1B,gBACC,MAAM,iBAAiB,GAAG;YAC/B;YACA,cAAc,CAAC,cAAc,KAAK,GAAG,WAAW,IAAI,GAAG,IAAI,GAAG,EAAE,IAC5D,8BAA8B,eAC9B;YACJ,eAAe,OAAO,MAAM,oBAAoB,GAAG,CAAC,IAAI;YACxD,OAAO;QACT;QACA,SAAS,qCAAqC,IAAI;YAChD,IAAI,QAAQ,MAAM,OAAO;YACzB,IAAI,eAAe,OAAO,MAAM;gBAC9B,IAAI,YAAY,KAAK,SAAS;gBAC9B,OAAO,6BACL,MACA,CAAC,CAAC,CAAC,aAAa,CAAC,UAAU,gBAAgB;YAE/C;YACA,IAAI,aAAa,OAAO,MAAM,OAAO,8BAA8B;YACnE,OAAQ;gBACN,KAAK;oBACH,OAAO,8BAA8B;gBACvC,KAAK;oBACH,OAAO,8BAA8B;YACzC;YACA,IAAI,aAAa,OAAO,MACtB,OAAQ,KAAK,QAAQ;gBACnB,KAAK;oBACH,OAAO,AAAC,OAAO,6BAA6B,KAAK,MAAM,EAAE,QAAS;gBACpE,KAAK;oBACH,OAAO,qCAAqC,KAAK,IAAI;gBACvD,KAAK;oBACH,YAAY,KAAK,QAAQ;oBACzB,OAAO,KAAK,KAAK;oBACjB,IAAI;wBACF,OAAO,qCAAqC,KAAK;oBACnD,EAAE,OAAO,GAAG,CAAC;YACjB;YACF,OAAO;QACT;QACA,SAAS;YACP,IAAI,aAAa,qBAAqB,CAAC;YACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;QACzD;QACA,SAAS,YAAY,MAAM;YACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;gBACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;gBAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO;YAC9C;YACA,OAAO,KAAK,MAAM,OAAO,GAAG;QAC9B;QACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;YACpD,SAAS;gBACP,8BACE,CAAC,AAAC,6BAA6B,MAC/B,QAAQ,KAAK,CACX,2OACA,YACD;YACL;YACA,sBAAsB,cAAc,GAAG;YACvC,OAAO,cAAc,CAAC,OAAO,OAAO;gBAClC,KAAK;gBACL,cAAc;YAChB;QACF;QACA,SAAS;YACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;YACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,MAC1C,QAAQ,KAAK,CACX,8IACD;YACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;YAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;QACpD;QACA,SAAS,aAAa,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK;YACzD,OAAO,MAAM,GAAG;YAChB,OAAO;gBACL,UAAU;gBACV,MAAM;gBACN,KAAK;gBACL,OAAO;gBACP,QAAQ;YACV;YACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;gBACjC,YAAY;gBACZ,KAAK;YACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;gBAAE,YAAY;gBAAO,OAAO;YAAK;YACxE,KAAK,MAAM,GAAG,CAAC;YACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;gBAC9C,cAAc;gBACd,YAAY;gBACZ,UAAU;gBACV,OAAO;YACT;YACA,OAAO,cAAc,CAAC,MAAM,cAAc;gBACxC,cAAc;gBACd,YAAY;gBACZ,UAAU;gBACV,OAAO;YACT;YACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;YAChE,OAAO;QACT;QACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;YAEJ,IACE,aAAa,OAAO,QACpB,eAAe,OAAO,QACtB,SAAS,uBACT,SAAS,uBACT,SAAS,0BACT,SAAS,uBACT,SAAS,4BACT,SAAS,wBACR,aAAa,OAAO,QACnB,SAAS,QACT,CAAC,KAAK,QAAQ,KAAK,mBACjB,KAAK,QAAQ,KAAK,mBAClB,KAAK,QAAQ,KAAK,sBAClB,KAAK,QAAQ,KAAK,uBAClB,KAAK,QAAQ,KAAK,0BAClB,KAAK,QAAQ,KAAK,4BAClB,KAAK,MAAM,KAAK,WAAW,GAC/B;gBACA,IAAI,WAAW,OAAO,QAAQ;gBAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;oBACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB,EAAE;oBAChD,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;gBACjC,OACE,QAAQ,KAAK,CACX;qBAED,kBAAkB,UAAU;YACrC,OAAO;gBACL,WAAW;gBACX,IACE,KAAK,MAAM,QACV,aAAa,OAAO,QACnB,SAAS,QACT,MAAM,OAAO,IAAI,CAAC,MAAM,MAAM,EAEhC,YACE;gBACJ,SAAS,OACJ,mBAAmB,SACpB,YAAY,QACT,mBAAmB,UACpB,KAAK,MAAM,QAAQ,KAAK,QAAQ,KAAK,qBACnC,CAAC,AAAC,mBACA,MACA,CAAC,yBAAyB,KAAK,IAAI,KAAK,SAAS,IACjD,OACD,WACC,oEAAqE,IACtE,mBAAmB,OAAO;gBACnC,QAAQ,KAAK,CACX,2IACA,kBACA;YAEJ;YACA,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;gBACtC,WAAW,yBAAyB;gBACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;oBAC/C,OAAO,UAAU;gBACnB;gBACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;gBACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,IAAK;YAC/D;YACA,WAAW;YACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;YAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;YACnE,IAAI,SAAS,QAAQ;gBACnB,WAAW,CAAC;gBACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;YAChE,OAAO,WAAW;YAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;YAER,OAAO,aAAa,MAAM,UAAU,MAAM,QAAQ,YAAY;QAChE;QACA,SAAS,kBAAkB,IAAI,EAAE,UAAU;YACzC,IACE,aAAa,OAAO,QACpB,QACA,KAAK,QAAQ,KAAK,wBAElB;gBAAA,IAAI,YAAY,OACd,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;oBACpC,IAAI,QAAQ,IAAI,CAAC,EAAE;oBACnB,eAAe,UAAU,oBAAoB,OAAO;gBACtD;qBACG,IAAI,eAAe,OACtB,KAAK,MAAM,IAAI,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;qBACtC,IACF,SAAS,QAAQ,aAAa,OAAO,OACjC,IAAI,OACL,CAAC,AAAC,IACA,AAAC,yBAAyB,IAAI,CAAC,sBAAsB,IACrD,IAAI,CAAC,aAAa,EACnB,IAAI,eAAe,OAAO,IAAI,IAAI,IAAK,GAC5C,eAAe,OAAO,KACpB,MAAM,KAAK,OAAO,IAClB,CAAC,AAAC,IAAI,EAAE,IAAI,CAAC,OAAQ,MAAM,IAAI,GAEjC,MAAO,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE,IAAI,EAC5B,eAAe,KAAK,KAAK,KACvB,oBAAoB,KAAK,KAAK,EAAE;YAAW;QACrD;QACA,SAAS,eAAe,MAAM;YAC5B,OACE,aAAa,OAAO,UACpB,SAAS,UACT,OAAO,QAAQ,KAAK;QAExB;QACA,SAAS,oBAAoB,OAAO,EAAE,UAAU;YAC9C,IACE,QAAQ,MAAM,IACd,CAAC,QAAQ,MAAM,CAAC,SAAS,IACzB,QAAQ,QAAQ,GAAG,IACnB,CAAC,AAAC,QAAQ,MAAM,CAAC,SAAS,GAAG,GAC5B,aAAa,6BAA6B,aAC3C,CAAC,qBAAqB,CAAC,WAAW,GAClC;gBACA,qBAAqB,CAAC,WAAW,GAAG;gBACpC,IAAI,aAAa;gBACjB,WACE,QAAQ,QAAQ,MAAM,IACtB,QAAQ,MAAM,KAAK,cACnB,CAAC,AAAC,aAAa,MACf,aAAa,OAAO,QAAQ,MAAM,CAAC,GAAG,GACjC,aAAa,yBAAyB,QAAQ,MAAM,CAAC,IAAI,IAC1D,aAAa,OAAO,QAAQ,MAAM,CAAC,IAAI,IACvC,CAAC,aAAa,QAAQ,MAAM,CAAC,IAAI,GACpC,aAAa,iCAAiC,aAAa,GAAI;gBAClE,IAAI,sBAAsB,qBAAqB,eAAe;gBAC9D,qBAAqB,eAAe,GAAG;oBACrC,IAAI,QAAQ,qCAAqC,QAAQ,IAAI;oBAC7D,uBAAuB,CAAC,SAAS,yBAAyB,EAAE;oBAC5D,OAAO;gBACT;gBACA,QAAQ,KAAK,CACX,2HACA,YACA;gBAEF,qBAAqB,eAAe,GAAG;YACzC;QACF;QACA,SAAS,6BAA6B,UAAU;YAC9C,IAAI,OAAO,IACT,QAAQ;YACV,SACE,CAAC,QAAQ,yBAAyB,MAAM,IAAI,CAAC,KAC7C,CAAC,OAAO,qCAAqC,QAAQ,IAAI;YAC3D,QACG,CAAC,aAAa,yBAAyB,WAAW,KACjD,CAAC,OACC,gDAAgD,aAAa,IAAI;YACvE,OAAO;QACT;QACA,IAAI,UAAU,qMAAA,CAAA,UAAK,EACjB,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC;QACnC,IAAI,sBAAsB,OAAO,GAAG,CAAC,mBACnC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,uBAAuB,OAAO,GAAG,CAAC,oBAClC,wBAAwB,OAAO,QAAQ,EACvC,2BAA2B,OAAO,GAAG,CAAC,2BACtC,uBACE,QAAQ,+DAA+D,EACzE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,SAAS,OAAO,MAAM,EACtB,2BAA2B,OAAO,GAAG,CAAC,2BACtC,cAAc,MAAM,OAAO,EAC3B,gBAAgB,GAChB,SACA,UACA,UACA,WACA,WACA,oBACA;QACF,YAAY,kBAAkB,GAAG;QACjC,IAAI,QACF,QACA,UAAU;QACZ,IAAI,sBAAsB,IAAI,CAC5B,eAAe,OAAO,UAAU,UAAU,GAC5C;QACA,IAAI,yBAAyB,OAAO,GAAG,CAAC,2BACtC;QACF,IAAI,yBAAyB,CAAC;QAC9B,IAAI,wBAAwB,CAAC,GAC3B,wBAAwB,CAAC;QAC3B,4BAA4B,QAAQ,GAAG;QACvC,4BAA4B,GAAG,GAAG,SAAU,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI;YAC9E,OAAO,WAAW,MAAM,QAAQ,UAAU,OAAO,QAAQ;QAC3D;QACA,4BAA4B,IAAI,GAAG,SAAU,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI;YAC/E,OAAO,WAAW,MAAM,QAAQ,UAAU,MAAM,QAAQ;QAC1D;IACF;IACF,OAAO;AACR;AAEA,IAAI;AAEJ,SAAS;IACR,IAAI,uBAAuB,OAAO,WAAW,OAAO;IACpD,wBAAwB;IAExB,uCAA2C;;IAE3C,OAAO;QACL,WAAW,OAAO,GAAG;IACvB;IACA,OAAO,WAAW,OAAO;AAC1B;AAEA,IAAI,oBAAoB;AAExB,wFAAwF;AACxF,MAAM,YAAY,OAAO,WAAW,eAAe,OAAO,OAAO,QAAQ,KAAK,eAAe,OAAO,OAAO,QAAQ,CAAC,aAAa,KAAK;AAEtI,SAAS,SAAS,OAAO;IACvB,MAAM,gBAAgB,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC;IACrD,OAAO,kBAAkB,qBAAqB,sEAAsE;IACpH,kBAAkB;AACpB;AAEA,SAAS,OAAO,IAAI;IAClB,OAAO,cAAc;AACvB;AAEA,SAAS,UAAU,MAAM;IACvB,IAAI,uBAAuB;IAE3B,IAAI,CAAC,QAAQ;QACX,OAAO;IACT;IAEA,IAAI,SAAS,SAAS;QACpB,OAAO;IACT;IAEA,IAAI,CAAC,OAAO,SAAS;QACnB,OAAO;IACT;IAEA,OAAO,CAAC,wBAAwB,CAAC,yBAAyB,OAAO,aAAa,KAAK,OAAO,KAAK,IAAI,uBAAuB,WAAW,KAAK,OAAO,wBAAwB;AAC3K;AAEA,SAAS,WAAW,IAAI;IACtB,MAAM,EACJ,QAAQ,EACT,GAAG,UAAU;IACd,OAAO,gBAAgB;AACzB;AAEA,SAAS,cAAc,IAAI;IACzB,IAAI,SAAS,OAAO;QAClB,OAAO;IACT;IAEA,OAAO,gBAAgB,UAAU,MAAM,WAAW;AACpD;AAEA,SAAS,aAAa,IAAI;IACxB,OAAO,gBAAgB,UAAU,MAAM,UAAU;AACnD;AAEA,SAAS,iBAAiB,MAAM;IAC9B,IAAI,CAAC,QAAQ;QACX,OAAO;IACT;IAEA,IAAI,SAAS,SAAS;QACpB,OAAO,OAAO,QAAQ;IACxB;IAEA,IAAI,CAAC,OAAO,SAAS;QACnB,OAAO;IACT;IAEA,IAAI,WAAW,SAAS;QACtB,OAAO;IACT;IAEA,IAAI,cAAc,WAAW,aAAa,SAAS;QACjD,OAAO,OAAO,aAAa;IAC7B;IAEA,OAAO;AACT;AAEA;;;CAGC,GAED,MAAM,4BAA4B,YAAY,qMAAA,CAAA,kBAAe,GAAG,qMAAA,CAAA,YAAS;AAEzE,SAAS,SAAS,OAAO;IACvB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,0BAA0B;QACxB,WAAW,OAAO,GAAG;IACvB;IACA,OAAO,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjB,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,OAAO,OAAO,GAAG,OAAO,MAAM,OAAQ;YACvF,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;QAC9B;QAEA,OAAO,WAAW,OAAO,IAAI,OAAO,KAAK,IAAI,WAAW,OAAO,IAAI;IACrE,GAAG,EAAE;AACP;AAEA,SAAS;IACP,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,UAAU;QACjC,YAAY,OAAO,GAAG,YAAY,UAAU;IAC9C,GAAG,EAAE;IACL,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACxB,IAAI,YAAY,OAAO,KAAK,MAAM;YAChC,cAAc,YAAY,OAAO;YACjC,YAAY,OAAO,GAAG;QACxB;IACF,GAAG,EAAE;IACL,OAAO;QAAC;QAAK;KAAM;AACrB;AAEA,SAAS,eAAe,KAAK,EAAE,YAAY;IACzC,IAAI,iBAAiB,KAAK,GAAG;QAC3B,eAAe;YAAC;SAAM;IACxB;IAEA,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACxB,0BAA0B;QACxB,IAAI,SAAS,OAAO,KAAK,OAAO;YAC9B,SAAS,OAAO,GAAG;QACrB;IACF,GAAG;IACH,OAAO;AACT;AAEA,SAAS,YAAY,QAAQ,EAAE,YAAY;IACzC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IACtB,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACb,MAAM,WAAW,SAAS,SAAS,OAAO;QAC1C,SAAS,OAAO,GAAG;QACnB,OAAO;IACT,GACA;WAAI;KAAa;AACnB;AAEA,SAAS,WAAW,QAAQ;IAC1B,MAAM,kBAAkB,SAAS;IACjC,MAAM,OAAO,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACpB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAA;QAC7B,IAAI,YAAY,KAAK,OAAO,EAAE;YAC5B,mBAAmB,OAAO,KAAK,IAAI,gBAAgB,SAAS,KAAK,OAAO;QAC1E;QAEA,KAAK,OAAO,GAAG;IACjB,GACA,EAAE;IACF,OAAO;QAAC;QAAM;KAAW;AAC3B;AAEA,SAAS,YAAY,KAAK;IACxB,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IACjB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO,GAAG;IAChB,GAAG;QAAC;KAAM;IACV,OAAO,IAAI,OAAO;AACpB;AAEA,IAAI,MAAM,CAAC;AACX,SAAS,YAAY,MAAM,EAAE,KAAK;IAChC,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACb,IAAI,OAAO;YACT,OAAO;QACT;QAEA,MAAM,KAAK,GAAG,CAAC,OAAO,IAAI,OAAO,IAAI,GAAG,CAAC,OAAO,GAAG;QACnD,GAAG,CAAC,OAAO,GAAG;QACd,OAAO,SAAS,MAAM;IACxB,GAAG;QAAC;QAAQ;KAAM;AACpB;AAEA,SAAS,mBAAmB,QAAQ;IAClC,OAAO,SAAU,MAAM;QACrB,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,cAAc,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,IAAI,OAAO,GAAG,OAAO,MAAM,OAAQ;YACjH,WAAW,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,KAAK;QACzC;QAEA,OAAO,YAAY,MAAM,CAAC,CAAC,aAAa;YACtC,MAAM,UAAU,OAAO,OAAO,CAAC;YAE/B,KAAK,MAAM,CAAC,KAAK,gBAAgB,IAAI,QAAS;gBAC5C,MAAM,QAAQ,WAAW,CAAC,IAAI;gBAE9B,IAAI,SAAS,MAAM;oBACjB,WAAW,CAAC,IAAI,GAAG,QAAQ,WAAW;gBACxC;YACF;YAEA,OAAO;QACT,GAAG;YAAE,GAAG,MAAM;QACd;IACF;AACF;AAEA,MAAM,MAAM,WAAW,GAAE,mBAAmB;AAC5C,MAAM,WAAW,WAAW,GAAE,mBAAmB,CAAC;AAElD,SAAS,+BAA+B,KAAK;IAC3C,OAAO,aAAa,SAAS,aAAa;AAC5C;AAEA,SAAS,gBAAgB,KAAK;IAC5B,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IAEA,MAAM,EACJ,aAAa,EACd,GAAG,UAAU,MAAM,MAAM;IAC1B,OAAO,iBAAiB,iBAAiB;AAC3C;AAEA,SAAS,aAAa,KAAK;IACzB,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IAEA,MAAM,EACJ,UAAU,EACX,GAAG,UAAU,MAAM,MAAM;IAC1B,OAAO,cAAc,iBAAiB;AACxC;AAEA;;CAEC,GAED,SAAS,oBAAoB,KAAK;IAChC,IAAI,aAAa,QAAQ;QACvB,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO,CAAC,MAAM,EAAE;YACzC,MAAM,EACJ,SAAS,CAAC,EACV,SAAS,CAAC,EACX,GAAG,MAAM,OAAO,CAAC,EAAE;YACpB,OAAO;gBACL;gBACA;YACF;QACF,OAAO,IAAI,MAAM,cAAc,IAAI,MAAM,cAAc,CAAC,MAAM,EAAE;YAC9D,MAAM,EACJ,SAAS,CAAC,EACV,SAAS,CAAC,EACX,GAAG,MAAM,cAAc,CAAC,EAAE;YAC3B,OAAO;gBACL;gBACA;YACF;QACF;IACF;IAEA,IAAI,+BAA+B,QAAQ;QACzC,OAAO;YACL,GAAG,MAAM,OAAO;YAChB,GAAG,MAAM,OAAO;QAClB;IACF;IAEA,OAAO;AACT;AAEA,MAAM,MAAM,WAAW,GAAE,OAAO,MAAM,CAAC;IACrC,WAAW;QACT,UAAS,SAAS;YAChB,IAAI,CAAC,WAAW;gBACd;YACF;YAEA,MAAM,EACJ,CAAC,EACD,CAAC,EACF,GAAG;YACJ,OAAO,iBAAiB,CAAC,IAAI,KAAK,KAAK,CAAC,KAAK,CAAC,IAAI,SAAS,CAAC,IAAI,KAAK,KAAK,CAAC,KAAK,CAAC,IAAI;QACvF;IAEF;IACA,OAAO;QACL,UAAS,SAAS;YAChB,IAAI,CAAC,WAAW;gBACd;YACF;YAEA,MAAM,EACJ,MAAM,EACN,MAAM,EACP,GAAG;YACJ,OAAO,YAAY,SAAS,cAAc,SAAS;QACrD;IAEF;IACA,WAAW;QACT,UAAS,SAAS;YAChB,IAAI,CAAC,WAAW;gBACd;YACF;YAEA,OAAO;gBAAC,IAAI,SAAS,CAAC,QAAQ,CAAC;gBAAY,IAAI,KAAK,CAAC,QAAQ,CAAC;aAAW,CAAC,IAAI,CAAC;QACjF;IAEF;IACA,YAAY;QACV,UAAS,IAAI;YACX,IAAI,EACF,QAAQ,EACR,QAAQ,EACR,MAAM,EACP,GAAG;YACJ,OAAO,WAAW,MAAM,WAAW,QAAQ;QAC7C;IAEF;AACF;AAEA,MAAM,WAAW;AACjB,SAAS,uBAAuB,OAAO;IACrC,IAAI,QAAQ,OAAO,CAAC,WAAW;QAC7B,OAAO;IACT;IAEA,OAAO,QAAQ,aAAa,CAAC;AAC/B;AAEA,MAAM,eAAe;IACnB,SAAS;AACX;AACA,SAAS,WAAW,IAAI;IACtB,IAAI,EACF,EAAE,EACF,KAAK,EACN,GAAG;IACJ,OAAO,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAChC,IAAI;QACJ,OAAO;IACT,GAAG;AACL;AAEA,SAAS,WAAW,IAAI;IACtB,IAAI,EACF,EAAE,EACF,YAAY,EACZ,eAAe,WAAW,EAC3B,GAAG;IACJ,+DAA+D;IAC/D,MAAM,iBAAiB;QACrB,UAAU;QACV,KAAK;QACL,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ,CAAC;QACT,QAAQ;QACR,SAAS;QACT,UAAU;QACV,MAAM;QACN,UAAU;QACV,YAAY;IACd;IACA,OAAO,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAChC,IAAI;QACJ,OAAO;QACP,MAAM;QACN,aAAa;QACb,eAAe;IACjB,GAAG;AACL;AAEA,SAAS;IACP,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAA;QAC3B,IAAI,SAAS,MAAM;YACjB,gBAAgB;QAClB;IACF,GAAG,EAAE;IACL,OAAO;QACL;QACA;IACF;AACF;AAEA,MAAM,oBAAoB,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE;AAErD,SAAS,cAAc,QAAQ;IAC7B,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IACpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,kBAAkB;YACrB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,cAAc,iBAAiB;QACrC,OAAO;IACT,GAAG;QAAC;QAAU;KAAiB;AACjC;AAEA,SAAS;IACP,MAAM,CAAC,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAM,IAAI;IACvC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAA;QACnC,UAAU,GAAG,CAAC;QACd,OAAO,IAAM,UAAU,MAAM,CAAC;IAChC,GAAG;QAAC;KAAU;IACd,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAA;QAC3B,IAAI,EACF,IAAI,EACJ,KAAK,EACN,GAAG;QACJ,UAAU,OAAO,CAAC,CAAA;YAChB,IAAI;YAEJ,OAAO,CAAC,iBAAiB,QAAQ,CAAC,KAAK,KAAK,OAAO,KAAK,IAAI,eAAe,IAAI,CAAC,UAAU;QAC5F;IACF,GAAG;QAAC;KAAU;IACd,OAAO;QAAC;QAAU;KAAiB;AACrC;AAEA,MAAM,kCAAkC;IACtC,WAAW;AACb;AACA,MAAM,uBAAuB;IAC3B,aAAY,IAAI;QACd,IAAI,EACF,MAAM,EACP,GAAG;QACJ,OAAO,8BAA8B,OAAO,EAAE,GAAG;IACnD;IAEA,YAAW,KAAK;QACd,IAAI,EACF,MAAM,EACN,IAAI,EACL,GAAG;QAEJ,IAAI,MAAM;YACR,OAAO,oBAAoB,OAAO,EAAE,GAAG,oCAAoC,KAAK,EAAE,GAAG;QACvF;QAEA,OAAO,oBAAoB,OAAO,EAAE,GAAG;IACzC;IAEA,WAAU,KAAK;QACb,IAAI,EACF,MAAM,EACN,IAAI,EACL,GAAG;QAEJ,IAAI,MAAM;YACR,OAAO,oBAAoB,OAAO,EAAE,GAAG,sCAAsC,KAAK,EAAE;QACtF;QAEA,OAAO,oBAAoB,OAAO,EAAE,GAAG;IACzC;IAEA,cAAa,KAAK;QAChB,IAAI,EACF,MAAM,EACP,GAAG;QACJ,OAAO,4CAA4C,OAAO,EAAE,GAAG;IACjE;AAEF;AAEA,SAAS,cAAc,IAAI;IACzB,IAAI,EACF,gBAAgB,oBAAoB,EACpC,SAAS,EACT,uBAAuB,EACvB,2BAA2B,+BAA+B,EAC3D,GAAG;IACJ,MAAM,EACJ,QAAQ,EACR,YAAY,EACb,GAAG;IACJ,MAAM,eAAe,YAAY;IACjC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IACL,cAAc,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC;YAC3B,aAAY,KAAK;gBACf,IAAI,EACF,MAAM,EACP,GAAG;gBACJ,SAAS,cAAc,WAAW,CAAC;oBACjC;gBACF;YACF;YAEA,YAAW,KAAK;gBACd,IAAI,EACF,MAAM,EACN,IAAI,EACL,GAAG;gBAEJ,IAAI,cAAc,UAAU,EAAE;oBAC5B,SAAS,cAAc,UAAU,CAAC;wBAChC;wBACA;oBACF;gBACF;YACF;YAEA,YAAW,KAAK;gBACd,IAAI,EACF,MAAM,EACN,IAAI,EACL,GAAG;gBACJ,SAAS,cAAc,UAAU,CAAC;oBAChC;oBACA;gBACF;YACF;YAEA,WAAU,KAAK;gBACb,IAAI,EACF,MAAM,EACN,IAAI,EACL,GAAG;gBACJ,SAAS,cAAc,SAAS,CAAC;oBAC/B;oBACA;gBACF;YACF;YAEA,cAAa,KAAK;gBAChB,IAAI,EACF,MAAM,EACN,IAAI,EACL,GAAG;gBACJ,SAAS,cAAc,YAAY,CAAC;oBAClC;oBACA;gBACF;YACF;QAEF,CAAC,GAAG;QAAC;QAAU;KAAc;IAE7B,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,MAAM,SAAS,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ,EAAE,MAAM,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,YAAY;QACvF,IAAI;QACJ,OAAO,yBAAyB,SAAS;IAC3C,IAAI,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,YAAY;QAClC,IAAI;QACJ,cAAc;IAChB;IACA,OAAO,YAAY,CAAA,GAAA,4MAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,aAAa;AACvD;AAEA,IAAI;AAEJ,CAAC,SAAU,MAAM;IACf,MAAM,CAAC,YAAY,GAAG;IACtB,MAAM,CAAC,WAAW,GAAG;IACrB,MAAM,CAAC,UAAU,GAAG;IACpB,MAAM,CAAC,aAAa,GAAG;IACvB,MAAM,CAAC,WAAW,GAAG;IACrB,MAAM,CAAC,oBAAoB,GAAG;IAC9B,MAAM,CAAC,uBAAuB,GAAG;IACjC,MAAM,CAAC,sBAAsB,GAAG;AAClC,CAAC,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC;AAEzB,SAAS,QAAQ;AAEjB,SAAS,UAAU,MAAM,EAAE,OAAO;IAChC,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC;YACpB;YACA,SAAS,WAAW,OAAO,UAAU,CAAC;QACxC,CAAC,GACD;QAAC;QAAQ;KAAQ;AACnB;AAEA,SAAS;IACP,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,UAAU,IAAI,MAAM,OAAO,OAAO,GAAG,OAAO,MAAM,OAAQ;QAC1F,OAAO,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;IACjC;IAEA,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM;eAAI;SAAQ,CAAC,MAAM,CAAC,CAAA,SAAU,UAAU,OAC7D;WAAI;KAAQ;AACd;AAEA,MAAM,qBAAqB,WAAW,GAAE,OAAO,MAAM,CAAC;IACpD,GAAG;IACH,GAAG;AACL;AAEA;;CAEC,GACD,SAAS,gBAAgB,EAAE,EAAE,EAAE;IAC7B,OAAO,KAAK,IAAI,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,EAAE,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,EAAE;AACpE;AAEA,SAAS,2BAA2B,KAAK,EAAE,IAAI;IAC7C,MAAM,mBAAmB,oBAAoB;IAE7C,IAAI,CAAC,kBAAkB;QACrB,OAAO;IACT;IAEA,MAAM,kBAAkB;QACtB,GAAG,CAAC,iBAAiB,CAAC,GAAG,KAAK,IAAI,IAAI,KAAK,KAAK,GAAG;QACnD,GAAG,CAAC,iBAAiB,CAAC,GAAG,KAAK,GAAG,IAAI,KAAK,MAAM,GAAG;IACrD;IACA,OAAO,gBAAgB,CAAC,GAAG,OAAO,gBAAgB,CAAC,GAAG;AACxD;AAEA;;CAEC,GACD,SAAS,kBAAkB,IAAI,EAAE,KAAK;IACpC,IAAI,EACF,MAAM,EACJ,OAAO,CAAC,EACT,EACF,GAAG;IACJ,IAAI,EACF,MAAM,EACJ,OAAO,CAAC,EACT,EACF,GAAG;IACJ,OAAO,IAAI;AACb;AACA;;CAEC,GAED,SAAS,mBAAmB,KAAK,EAAE,KAAK;IACtC,IAAI,EACF,MAAM,EACJ,OAAO,CAAC,EACT,EACF,GAAG;IACJ,IAAI,EACF,MAAM,EACJ,OAAO,CAAC,EACT,EACF,GAAG;IACJ,OAAO,IAAI;AACb;AACA;;;CAGC,GAED,SAAS,mBAAmB,KAAK;IAC/B,IAAI,EACF,IAAI,EACJ,GAAG,EACH,MAAM,EACN,KAAK,EACN,GAAG;IACJ,OAAO;QAAC;YACN,GAAG;YACH,GAAG;QACL;QAAG;YACD,GAAG,OAAO;YACV,GAAG;QACL;QAAG;YACD,GAAG;YACH,GAAG,MAAM;QACX;QAAG;YACD,GAAG,OAAO;YACV,GAAG,MAAM;QACX;KAAE;AACJ;AACA,SAAS,kBAAkB,UAAU,EAAE,QAAQ;IAC7C,IAAI,CAAC,cAAc,WAAW,MAAM,KAAK,GAAG;QAC1C,OAAO;IACT;IAEA,MAAM,CAAC,eAAe,GAAG;IACzB,OAAO,cAAc,CAAC,SAAS;AACjC;AAEA;;CAEC,GAED,SAAS,qBAAqB,KAAK,EAAE,MAAM;IACzC,MAAM,MAAM,KAAK,GAAG,CAAC,OAAO,GAAG,EAAE,MAAM,GAAG;IAC1C,MAAM,OAAO,KAAK,GAAG,CAAC,OAAO,IAAI,EAAE,MAAM,IAAI;IAC7C,MAAM,QAAQ,KAAK,GAAG,CAAC,OAAO,IAAI,GAAG,OAAO,KAAK,EAAE,MAAM,IAAI,GAAG,MAAM,KAAK;IAC3E,MAAM,SAAS,KAAK,GAAG,CAAC,OAAO,GAAG,GAAG,OAAO,MAAM,EAAE,MAAM,GAAG,GAAG,MAAM,MAAM;IAC5E,MAAM,QAAQ,QAAQ;IACtB,MAAM,SAAS,SAAS;IAExB,IAAI,OAAO,SAAS,MAAM,QAAQ;QAChC,MAAM,aAAa,OAAO,KAAK,GAAG,OAAO,MAAM;QAC/C,MAAM,YAAY,MAAM,KAAK,GAAG,MAAM,MAAM;QAC5C,MAAM,mBAAmB,QAAQ;QACjC,MAAM,oBAAoB,mBAAmB,CAAC,aAAa,YAAY,gBAAgB;QACvF,OAAO,OAAO,kBAAkB,OAAO,CAAC;IAC1C,EAAE,kFAAkF;IAGpF,OAAO;AACT;AACA;;;CAGC,GAED,MAAM,mBAAmB,CAAA;IACvB,IAAI,EACF,aAAa,EACb,cAAc,EACd,mBAAmB,EACpB,GAAG;IACJ,MAAM,aAAa,EAAE;IAErB,KAAK,MAAM,sBAAsB,oBAAqB;QACpD,MAAM,EACJ,EAAE,EACH,GAAG;QACJ,MAAM,OAAO,eAAe,GAAG,CAAC;QAEhC,IAAI,MAAM;YACR,MAAM,oBAAoB,qBAAqB,MAAM;YAErD,IAAI,oBAAoB,GAAG;gBACzB,WAAW,IAAI,CAAC;oBACd;oBACA,MAAM;wBACJ;wBACA,OAAO;oBACT;gBACF;YACF;QACF;IACF;IAEA,OAAO,WAAW,IAAI,CAAC;AACzB;AAEA;;CAEC,GAED,SAAS,kBAAkB,KAAK,EAAE,IAAI;IACpC,MAAM,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACN,GAAG;IACJ,OAAO,OAAO,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,UAAU,QAAQ,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI;AAC9E;AACA;;CAEC,GAGD,MAAM,gBAAgB,CAAA;IACpB,IAAI,EACF,mBAAmB,EACnB,cAAc,EACd,kBAAkB,EACnB,GAAG;IAEJ,IAAI,CAAC,oBAAoB;QACvB,OAAO,EAAE;IACX;IAEA,MAAM,aAAa,EAAE;IAErB,KAAK,MAAM,sBAAsB,oBAAqB;QACpD,MAAM,EACJ,EAAE,EACH,GAAG;QACJ,MAAM,OAAO,eAAe,GAAG,CAAC;QAEhC,IAAI,QAAQ,kBAAkB,oBAAoB,OAAO;YACvD;;;;OAIC,GACD,MAAM,UAAU,mBAAmB;YACnC,MAAM,YAAY,QAAQ,MAAM,CAAC,CAAC,aAAa;gBAC7C,OAAO,cAAc,gBAAgB,oBAAoB;YAC3D,GAAG;YACH,MAAM,oBAAoB,OAAO,CAAC,YAAY,CAAC,EAAE,OAAO,CAAC;YACzD,WAAW,IAAI,CAAC;gBACd;gBACA,MAAM;oBACJ;oBACA,OAAO;gBACT;YACF;QACF;IACF;IAEA,OAAO,WAAW,IAAI,CAAC;AACzB;AAEA,SAAS,YAAY,SAAS,EAAE,KAAK,EAAE,KAAK;IAC1C,OAAO;QAAE,GAAG,SAAS;QACnB,QAAQ,SAAS,QAAQ,MAAM,KAAK,GAAG,MAAM,KAAK,GAAG;QACrD,QAAQ,SAAS,QAAQ,MAAM,MAAM,GAAG,MAAM,MAAM,GAAG;IACzD;AACF;AAEA,SAAS,aAAa,KAAK,EAAE,KAAK;IAChC,OAAO,SAAS,QAAQ;QACtB,GAAG,MAAM,IAAI,GAAG,MAAM,IAAI;QAC1B,GAAG,MAAM,GAAG,GAAG,MAAM,GAAG;IAC1B,IAAI;AACN;AAEA,SAAS,uBAAuB,QAAQ;IACtC,OAAO,SAAS,iBAAiB,IAAI;QACnC,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,cAAc,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,IAAI,OAAO,GAAG,OAAO,MAAM,OAAQ;YACjH,WAAW,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,KAAK;QACzC;QAEA,OAAO,YAAY,MAAM,CAAC,CAAC,KAAK,aAAe,CAAC;gBAAE,GAAG,GAAG;gBACtD,KAAK,IAAI,GAAG,GAAG,WAAW,WAAW,CAAC;gBACtC,QAAQ,IAAI,MAAM,GAAG,WAAW,WAAW,CAAC;gBAC5C,MAAM,IAAI,IAAI,GAAG,WAAW,WAAW,CAAC;gBACxC,OAAO,IAAI,KAAK,GAAG,WAAW,WAAW,CAAC;YAC5C,CAAC,GAAG;YAAE,GAAG,IAAI;QACb;IACF;AACF;AACA,MAAM,kBAAkB,WAAW,GAAE,uBAAuB;AAE5D,SAAS,eAAe,SAAS;IAC/B,IAAI,UAAU,UAAU,CAAC,cAAc;QACrC,MAAM,iBAAiB,UAAU,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACpD,OAAO;YACL,GAAG,CAAC,cAAc,CAAC,GAAG;YACtB,GAAG,CAAC,cAAc,CAAC,GAAG;YACtB,QAAQ,CAAC,cAAc,CAAC,EAAE;YAC1B,QAAQ,CAAC,cAAc,CAAC,EAAE;QAC5B;IACF,OAAO,IAAI,UAAU,UAAU,CAAC,YAAY;QAC1C,MAAM,iBAAiB,UAAU,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACpD,OAAO;YACL,GAAG,CAAC,cAAc,CAAC,EAAE;YACrB,GAAG,CAAC,cAAc,CAAC,EAAE;YACrB,QAAQ,CAAC,cAAc,CAAC,EAAE;YAC1B,QAAQ,CAAC,cAAc,CAAC,EAAE;QAC5B;IACF;IAEA,OAAO;AACT;AAEA,SAAS,iBAAiB,IAAI,EAAE,SAAS,EAAE,eAAe;IACxD,MAAM,kBAAkB,eAAe;IAEvC,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,MAAM,EACJ,MAAM,EACN,MAAM,EACN,GAAG,UAAU,EACb,GAAG,UAAU,EACd,GAAG;IACJ,MAAM,IAAI,KAAK,IAAI,GAAG,aAAa,CAAC,IAAI,MAAM,IAAI,WAAW;IAC7D,MAAM,IAAI,KAAK,GAAG,GAAG,aAAa,CAAC,IAAI,MAAM,IAAI,WAAW,gBAAgB,KAAK,CAAC,gBAAgB,OAAO,CAAC,OAAO;IACjH,MAAM,IAAI,SAAS,KAAK,KAAK,GAAG,SAAS,KAAK,KAAK;IACnD,MAAM,IAAI,SAAS,KAAK,MAAM,GAAG,SAAS,KAAK,MAAM;IACrD,OAAO;QACL,OAAO;QACP,QAAQ;QACR,KAAK;QACL,OAAO,IAAI;QACX,QAAQ,IAAI;QACZ,MAAM;IACR;AACF;AAEA,MAAM,iBAAiB;IACrB,iBAAiB;AACnB;AACA;;CAEC,GAED,SAAS,cAAc,OAAO,EAAE,OAAO;IACrC,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU;IACZ;IAEA,IAAI,OAAO,QAAQ,qBAAqB;IAExC,IAAI,QAAQ,eAAe,EAAE;QAC3B,MAAM,EACJ,SAAS,EACT,eAAe,EAChB,GAAG,UAAU,SAAS,gBAAgB,CAAC;QAExC,IAAI,WAAW;YACb,OAAO,iBAAiB,MAAM,WAAW;QAC3C;IACF;IAEA,MAAM,EACJ,GAAG,EACH,IAAI,EACJ,KAAK,EACL,MAAM,EACN,MAAM,EACN,KAAK,EACN,GAAG;IACJ,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AACA;;;;;;;CAOC,GAED,SAAS,+BAA+B,OAAO;IAC7C,OAAO,cAAc,SAAS;QAC5B,iBAAiB;IACnB;AACF;AAEA,SAAS,oBAAoB,OAAO;IAClC,MAAM,QAAQ,QAAQ,UAAU;IAChC,MAAM,SAAS,QAAQ,WAAW;IAClC,OAAO;QACL,KAAK;QACL,MAAM;QACN,OAAO;QACP,QAAQ;QACR;QACA;IACF;AACF;AAEA,SAAS,QAAQ,IAAI,EAAE,aAAa;IAClC,IAAI,kBAAkB,KAAK,GAAG;QAC5B,gBAAgB,UAAU,MAAM,gBAAgB,CAAC;IACnD;IAEA,OAAO,cAAc,QAAQ,KAAK;AACpC;AAEA,SAAS,aAAa,OAAO,EAAE,aAAa;IAC1C,IAAI,kBAAkB,KAAK,GAAG;QAC5B,gBAAgB,UAAU,SAAS,gBAAgB,CAAC;IACtD;IAEA,MAAM,gBAAgB;IACtB,MAAM,aAAa;QAAC;QAAY;QAAa;KAAY;IACzD,OAAO,WAAW,IAAI,CAAC,CAAA;QACrB,MAAM,QAAQ,aAAa,CAAC,SAAS;QACrC,OAAO,OAAO,UAAU,WAAW,cAAc,IAAI,CAAC,SAAS;IACjE;AACF;AAEA,SAAS,uBAAuB,OAAO,EAAE,KAAK;IAC5C,MAAM,gBAAgB,EAAE;IAExB,SAAS,wBAAwB,IAAI;QACnC,IAAI,SAAS,QAAQ,cAAc,MAAM,IAAI,OAAO;YAClD,OAAO;QACT;QAEA,IAAI,CAAC,MAAM;YACT,OAAO;QACT;QAEA,IAAI,WAAW,SAAS,KAAK,gBAAgB,IAAI,QAAQ,CAAC,cAAc,QAAQ,CAAC,KAAK,gBAAgB,GAAG;YACvG,cAAc,IAAI,CAAC,KAAK,gBAAgB;YACxC,OAAO;QACT;QAEA,IAAI,CAAC,cAAc,SAAS,aAAa,OAAO;YAC9C,OAAO;QACT;QAEA,IAAI,cAAc,QAAQ,CAAC,OAAO;YAChC,OAAO;QACT;QAEA,MAAM,gBAAgB,UAAU,SAAS,gBAAgB,CAAC;QAE1D,IAAI,SAAS,SAAS;YACpB,IAAI,aAAa,MAAM,gBAAgB;gBACrC,cAAc,IAAI,CAAC;YACrB;QACF;QAEA,IAAI,QAAQ,MAAM,gBAAgB;YAChC,OAAO;QACT;QAEA,OAAO,wBAAwB,KAAK,UAAU;IAChD;IAEA,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,OAAO,wBAAwB;AACjC;AACA,SAAS,2BAA2B,IAAI;IACtC,MAAM,CAAC,wBAAwB,GAAG,uBAAuB,MAAM;IAC/D,OAAO,2BAA2B,OAAO,0BAA0B;AACrE;AAEA,SAAS,qBAAqB,OAAO;IACnC,IAAI,CAAC,aAAa,CAAC,SAAS;QAC1B,OAAO;IACT;IAEA,IAAI,SAAS,UAAU;QACrB,OAAO;IACT;IAEA,IAAI,CAAC,OAAO,UAAU;QACpB,OAAO;IACT;IAEA,IAAI,WAAW,YAAY,YAAY,iBAAiB,SAAS,gBAAgB,EAAE;QACjF,OAAO;IACT;IAEA,IAAI,cAAc,UAAU;QAC1B,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAAS,qBAAqB,OAAO;IACnC,IAAI,SAAS,UAAU;QACrB,OAAO,QAAQ,OAAO;IACxB;IAEA,OAAO,QAAQ,UAAU;AAC3B;AACA,SAAS,qBAAqB,OAAO;IACnC,IAAI,SAAS,UAAU;QACrB,OAAO,QAAQ,OAAO;IACxB;IAEA,OAAO,QAAQ,SAAS;AAC1B;AACA,SAAS,qBAAqB,OAAO;IACnC,OAAO;QACL,GAAG,qBAAqB;QACxB,GAAG,qBAAqB;IAC1B;AACF;AAEA,IAAI;AAEJ,CAAC,SAAU,SAAS;IAClB,SAAS,CAAC,SAAS,CAAC,UAAU,GAAG,EAAE,GAAG;IACtC,SAAS,CAAC,SAAS,CAAC,WAAW,GAAG,CAAC,EAAE,GAAG;AAC1C,CAAC,EAAE,aAAa,CAAC,YAAY,CAAC,CAAC;AAE/B,SAAS,2BAA2B,OAAO;IACzC,IAAI,CAAC,aAAa,CAAC,SAAS;QAC1B,OAAO;IACT;IAEA,OAAO,YAAY,SAAS,gBAAgB;AAC9C;AAEA,SAAS,kBAAkB,kBAAkB;IAC3C,MAAM,YAAY;QAChB,GAAG;QACH,GAAG;IACL;IACA,MAAM,aAAa,2BAA2B,sBAAsB;QAClE,QAAQ,OAAO,WAAW;QAC1B,OAAO,OAAO,UAAU;IAC1B,IAAI;QACF,QAAQ,mBAAmB,YAAY;QACvC,OAAO,mBAAmB,WAAW;IACvC;IACA,MAAM,YAAY;QAChB,GAAG,mBAAmB,WAAW,GAAG,WAAW,KAAK;QACpD,GAAG,mBAAmB,YAAY,GAAG,WAAW,MAAM;IACxD;IACA,MAAM,QAAQ,mBAAmB,SAAS,IAAI,UAAU,CAAC;IACzD,MAAM,SAAS,mBAAmB,UAAU,IAAI,UAAU,CAAC;IAC3D,MAAM,WAAW,mBAAmB,SAAS,IAAI,UAAU,CAAC;IAC5D,MAAM,UAAU,mBAAmB,UAAU,IAAI,UAAU,CAAC;IAC5D,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AAEA,MAAM,mBAAmB;IACvB,GAAG;IACH,GAAG;AACL;AACA,SAAS,2BAA2B,eAAe,EAAE,mBAAmB,EAAE,IAAI,EAAE,YAAY,EAAE,mBAAmB;IAC/G,IAAI,EACF,GAAG,EACH,IAAI,EACJ,KAAK,EACL,MAAM,EACP,GAAG;IAEJ,IAAI,iBAAiB,KAAK,GAAG;QAC3B,eAAe;IACjB;IAEA,IAAI,wBAAwB,KAAK,GAAG;QAClC,sBAAsB;IACxB;IAEA,MAAM,EACJ,KAAK,EACL,QAAQ,EACR,MAAM,EACN,OAAO,EACR,GAAG,kBAAkB;IACtB,MAAM,YAAY;QAChB,GAAG;QACH,GAAG;IACL;IACA,MAAM,QAAQ;QACZ,GAAG;QACH,GAAG;IACL;IACA,MAAM,YAAY;QAChB,QAAQ,oBAAoB,MAAM,GAAG,oBAAoB,CAAC;QAC1D,OAAO,oBAAoB,KAAK,GAAG,oBAAoB,CAAC;IAC1D;IAEA,IAAI,CAAC,SAAS,OAAO,oBAAoB,GAAG,GAAG,UAAU,MAAM,EAAE;QAC/D,YAAY;QACZ,UAAU,CAAC,GAAG,UAAU,QAAQ;QAChC,MAAM,CAAC,GAAG,eAAe,KAAK,GAAG,CAAC,CAAC,oBAAoB,GAAG,GAAG,UAAU,MAAM,GAAG,GAAG,IAAI,UAAU,MAAM;IACzG,OAAO,IAAI,CAAC,YAAY,UAAU,oBAAoB,MAAM,GAAG,UAAU,MAAM,EAAE;QAC/E,cAAc;QACd,UAAU,CAAC,GAAG,UAAU,OAAO;QAC/B,MAAM,CAAC,GAAG,eAAe,KAAK,GAAG,CAAC,CAAC,oBAAoB,MAAM,GAAG,UAAU,MAAM,GAAG,MAAM,IAAI,UAAU,MAAM;IAC/G;IAEA,IAAI,CAAC,WAAW,SAAS,oBAAoB,KAAK,GAAG,UAAU,KAAK,EAAE;QACpE,eAAe;QACf,UAAU,CAAC,GAAG,UAAU,OAAO;QAC/B,MAAM,CAAC,GAAG,eAAe,KAAK,GAAG,CAAC,CAAC,oBAAoB,KAAK,GAAG,UAAU,KAAK,GAAG,KAAK,IAAI,UAAU,KAAK;IAC3G,OAAO,IAAI,CAAC,UAAU,QAAQ,oBAAoB,IAAI,GAAG,UAAU,KAAK,EAAE;QACxE,cAAc;QACd,UAAU,CAAC,GAAG,UAAU,QAAQ;QAChC,MAAM,CAAC,GAAG,eAAe,KAAK,GAAG,CAAC,CAAC,oBAAoB,IAAI,GAAG,UAAU,KAAK,GAAG,IAAI,IAAI,UAAU,KAAK;IACzG;IAEA,OAAO;QACL;QACA;IACF;AACF;AAEA,SAAS,qBAAqB,OAAO;IACnC,IAAI,YAAY,SAAS,gBAAgB,EAAE;QACzC,MAAM,EACJ,UAAU,EACV,WAAW,EACZ,GAAG;QACJ,OAAO;YACL,KAAK;YACL,MAAM;YACN,OAAO;YACP,QAAQ;YACR,OAAO;YACP,QAAQ;QACV;IACF;IAEA,MAAM,EACJ,GAAG,EACH,IAAI,EACJ,KAAK,EACL,MAAM,EACP,GAAG,QAAQ,qBAAqB;IACjC,OAAO;QACL;QACA;QACA;QACA;QACA,OAAO,QAAQ,WAAW;QAC1B,QAAQ,QAAQ,YAAY;IAC9B;AACF;AAEA,SAAS,iBAAiB,mBAAmB;IAC3C,OAAO,oBAAoB,MAAM,CAAC,CAAC,KAAK;QACtC,OAAO,IAAI,KAAK,qBAAqB;IACvC,GAAG;AACL;AACA,SAAS,iBAAiB,mBAAmB;IAC3C,OAAO,oBAAoB,MAAM,CAAC,CAAC,KAAK;QACtC,OAAO,MAAM,qBAAqB;IACpC,GAAG;AACL;AACA,SAAS,iBAAiB,mBAAmB;IAC3C,OAAO,oBAAoB,MAAM,CAAC,CAAC,KAAK;QACtC,OAAO,MAAM,qBAAqB;IACpC,GAAG;AACL;AAEA,SAAS,uBAAuB,OAAO,EAAE,OAAO;IAC9C,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU;IACZ;IAEA,IAAI,CAAC,SAAS;QACZ;IACF;IAEA,MAAM,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACN,GAAG,QAAQ;IACZ,MAAM,0BAA0B,2BAA2B;IAE3D,IAAI,CAAC,yBAAyB;QAC5B;IACF;IAEA,IAAI,UAAU,KAAK,SAAS,KAAK,OAAO,OAAO,WAAW,IAAI,QAAQ,OAAO,UAAU,EAAE;QACvF,QAAQ,cAAc,CAAC;YACrB,OAAO;YACP,QAAQ;QACV;IACF;AACF;AAEA,MAAM,aAAa;IAAC;QAAC;QAAK;YAAC;YAAQ;SAAQ;QAAE;KAAiB;IAAE;QAAC;QAAK;YAAC;YAAO;SAAS;QAAE;KAAiB;CAAC;AAC3G,MAAM;IACJ,YAAY,IAAI,EAAE,OAAO,CAAE;QACzB,IAAI,CAAC,IAAI,GAAG,KAAK;QACjB,IAAI,CAAC,KAAK,GAAG,KAAK;QAClB,IAAI,CAAC,MAAM,GAAG,KAAK;QACnB,IAAI,CAAC,GAAG,GAAG,KAAK;QAChB,IAAI,CAAC,MAAM,GAAG,KAAK;QACnB,IAAI,CAAC,KAAK,GAAG,KAAK;QAClB,IAAI,CAAC,IAAI,GAAG,KAAK;QACjB,MAAM,sBAAsB,uBAAuB;QACnD,MAAM,gBAAgB,iBAAiB;QACvC,IAAI,CAAC,IAAI,GAAG;YAAE,GAAG,IAAI;QACrB;QACA,IAAI,CAAC,KAAK,GAAG,KAAK,KAAK;QACvB,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM;QAEzB,KAAK,MAAM,CAAC,MAAM,MAAM,gBAAgB,IAAI,WAAY;YACtD,KAAK,MAAM,OAAO,KAAM;gBACtB,OAAO,cAAc,CAAC,IAAI,EAAE,KAAK;oBAC/B,KAAK;wBACH,MAAM,iBAAiB,gBAAgB;wBACvC,MAAM,sBAAsB,aAAa,CAAC,KAAK,GAAG;wBAClD,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG;oBAC1B;oBACA,YAAY;gBACd;YACF;QACF;QAEA,OAAO,cAAc,CAAC,IAAI,EAAE,QAAQ;YAClC,YAAY;QACd;IACF;AAEF;AAEA,MAAM;IACJ,YAAY,MAAM,CAAE;QAClB,IAAI,CAAC,MAAM,GAAG,KAAK;QACnB,IAAI,CAAC,SAAS,GAAG,EAAE;QAEnB,IAAI,CAAC,SAAS,GAAG;YACf,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;gBACrB,IAAI;gBAEJ,OAAO,CAAC,eAAe,IAAI,CAAC,MAAM,KAAK,OAAO,KAAK,IAAI,aAAa,mBAAmB,IAAI;YAC7F;QACF;QAEA,IAAI,CAAC,MAAM,GAAG;IAChB;IAEA,IAAI,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE;QAC/B,IAAI;QAEJ,CAAC,gBAAgB,IAAI,CAAC,MAAM,KAAK,OAAO,KAAK,IAAI,cAAc,gBAAgB,CAAC,WAAW,SAAS;QACpG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;YAAC;YAAW;YAAS;SAAQ;IACnD;AAEF;AAEA,SAAS,uBAAuB,MAAM;IACpC,2FAA2F;IAC3F,6EAA6E;IAC7E,8EAA8E;IAC9E,6EAA6E;IAC7E,+DAA+D;IAC/D,MAAM,EACJ,WAAW,EACZ,GAAG,UAAU;IACd,OAAO,kBAAkB,cAAc,SAAS,iBAAiB;AACnE;AAEA,SAAS,oBAAoB,KAAK,EAAE,WAAW;IAC7C,MAAM,KAAK,KAAK,GAAG,CAAC,MAAM,CAAC;IAC3B,MAAM,KAAK,KAAK,GAAG,CAAC,MAAM,CAAC;IAE3B,IAAI,OAAO,gBAAgB,UAAU;QACnC,OAAO,KAAK,IAAI,CAAC,MAAM,IAAI,MAAM,KAAK;IACxC;IAEA,IAAI,OAAO,eAAe,OAAO,aAAa;QAC5C,OAAO,KAAK,YAAY,CAAC,IAAI,KAAK,YAAY,CAAC;IACjD;IAEA,IAAI,OAAO,aAAa;QACtB,OAAO,KAAK,YAAY,CAAC;IAC3B;IAEA,IAAI,OAAO,aAAa;QACtB,OAAO,KAAK,YAAY,CAAC;IAC3B;IAEA,OAAO;AACT;AAEA,IAAI;AAEJ,CAAC,SAAU,SAAS;IAClB,SAAS,CAAC,QAAQ,GAAG;IACrB,SAAS,CAAC,YAAY,GAAG;IACzB,SAAS,CAAC,UAAU,GAAG;IACvB,SAAS,CAAC,cAAc,GAAG;IAC3B,SAAS,CAAC,SAAS,GAAG;IACtB,SAAS,CAAC,kBAAkB,GAAG;IAC/B,SAAS,CAAC,mBAAmB,GAAG;AAClC,CAAC,EAAE,aAAa,CAAC,YAAY,CAAC,CAAC;AAE/B,SAAS,eAAe,KAAK;IAC3B,MAAM,cAAc;AACtB;AACA,SAAS,gBAAgB,KAAK;IAC5B,MAAM,eAAe;AACvB;AAEA,IAAI;AAEJ,CAAC,SAAU,YAAY;IACrB,YAAY,CAAC,QAAQ,GAAG;IACxB,YAAY,CAAC,OAAO,GAAG;IACvB,YAAY,CAAC,QAAQ,GAAG;IACxB,YAAY,CAAC,OAAO,GAAG;IACvB,YAAY,CAAC,KAAK,GAAG;IACrB,YAAY,CAAC,MAAM,GAAG;IACtB,YAAY,CAAC,QAAQ,GAAG;IACxB,YAAY,CAAC,MAAM,GAAG;AACxB,CAAC,EAAE,gBAAgB,CAAC,eAAe,CAAC,CAAC;AAErC,MAAM,uBAAuB;IAC3B,OAAO;QAAC,aAAa,KAAK;QAAE,aAAa,KAAK;KAAC;IAC/C,QAAQ;QAAC,aAAa,GAAG;KAAC;IAC1B,KAAK;QAAC,aAAa,KAAK;QAAE,aAAa,KAAK;QAAE,aAAa,GAAG;KAAC;AACjE;AACA,MAAM,kCAAkC,CAAC,OAAO;IAC9C,IAAI,EACF,kBAAkB,EACnB,GAAG;IAEJ,OAAQ,MAAM,IAAI;QAChB,KAAK,aAAa,KAAK;YACrB,OAAO;gBAAE,GAAG,kBAAkB;gBAC5B,GAAG,mBAAmB,CAAC,GAAG;YAC5B;QAEF,KAAK,aAAa,IAAI;YACpB,OAAO;gBAAE,GAAG,kBAAkB;gBAC5B,GAAG,mBAAmB,CAAC,GAAG;YAC5B;QAEF,KAAK,aAAa,IAAI;YACpB,OAAO;gBAAE,GAAG,kBAAkB;gBAC5B,GAAG,mBAAmB,CAAC,GAAG;YAC5B;QAEF,KAAK,aAAa,EAAE;YAClB,OAAO;gBAAE,GAAG,kBAAkB;gBAC5B,GAAG,mBAAmB,CAAC,GAAG;YAC5B;IACJ;IAEA,OAAO;AACT;AAEA,MAAM;IACJ,YAAY,KAAK,CAAE;QACjB,IAAI,CAAC,KAAK,GAAG,KAAK;QAClB,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,oBAAoB,GAAG,KAAK;QACjC,IAAI,CAAC,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC,eAAe,GAAG,KAAK;QAC5B,IAAI,CAAC,KAAK,GAAG;QACb,MAAM,EACJ,OAAO,EACL,MAAM,EACP,EACF,GAAG;QACJ,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,SAAS,GAAG,IAAI,UAAU,iBAAiB;QAChD,IAAI,CAAC,eAAe,GAAG,IAAI,UAAU,UAAU;QAC/C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI;QACjD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI;QAC/C,IAAI,CAAC,MAAM;IACb;IAEA,SAAS;QACP,IAAI,CAAC,WAAW;QAChB,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,MAAM,EAAE,IAAI,CAAC,YAAY;QAC5D,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,gBAAgB,EAAE,IAAI,CAAC,YAAY;QACtE,WAAW,IAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,OAAO,EAAE,IAAI,CAAC,aAAa;IAC3E;IAEA,cAAc;QACZ,MAAM,EACJ,UAAU,EACV,OAAO,EACR,GAAG,IAAI,CAAC,KAAK;QACd,MAAM,OAAO,WAAW,IAAI,CAAC,OAAO;QAEpC,IAAI,MAAM;YACR,uBAAuB;QACzB;QAEA,QAAQ;IACV;IAEA,cAAc,KAAK,EAAE;QACnB,IAAI,gBAAgB,QAAQ;YAC1B,MAAM,EACJ,MAAM,EACN,OAAO,EACP,OAAO,EACR,GAAG,IAAI,CAAC,KAAK;YACd,MAAM,EACJ,gBAAgB,oBAAoB,EACpC,mBAAmB,+BAA+B,EAClD,iBAAiB,QAAQ,EAC1B,GAAG;YACJ,MAAM,EACJ,IAAI,EACL,GAAG;YAEJ,IAAI,cAAc,GAAG,CAAC,QAAQ,CAAC,OAAO;gBACpC,IAAI,CAAC,SAAS,CAAC;gBACf;YACF;YAEA,IAAI,cAAc,MAAM,CAAC,QAAQ,CAAC,OAAO;gBACvC,IAAI,CAAC,YAAY,CAAC;gBAClB;YACF;YAEA,MAAM,EACJ,aAAa,EACd,GAAG,QAAQ,OAAO;YACnB,MAAM,qBAAqB,gBAAgB;gBACzC,GAAG,cAAc,IAAI;gBACrB,GAAG,cAAc,GAAG;YACtB,IAAI;YAEJ,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;gBAC9B,IAAI,CAAC,oBAAoB,GAAG;YAC9B;YAEA,MAAM,iBAAiB,iBAAiB,OAAO;gBAC7C;gBACA,SAAS,QAAQ,OAAO;gBACxB;YACF;YAEA,IAAI,gBAAgB;gBAClB,MAAM,mBAAmB,SAAS,gBAAgB;gBAClD,MAAM,cAAc;oBAClB,GAAG;oBACH,GAAG;gBACL;gBACA,MAAM,EACJ,mBAAmB,EACpB,GAAG,QAAQ,OAAO;gBAEnB,KAAK,MAAM,mBAAmB,oBAAqB;oBACjD,MAAM,YAAY,MAAM,IAAI;oBAC5B,MAAM,EACJ,KAAK,EACL,OAAO,EACP,MAAM,EACN,QAAQ,EACR,SAAS,EACT,SAAS,EACV,GAAG,kBAAkB;oBACtB,MAAM,oBAAoB,qBAAqB;oBAC/C,MAAM,qBAAqB;wBACzB,GAAG,KAAK,GAAG,CAAC,cAAc,aAAa,KAAK,GAAG,kBAAkB,KAAK,GAAG,kBAAkB,KAAK,GAAG,IAAI,kBAAkB,KAAK,EAAE,KAAK,GAAG,CAAC,cAAc,aAAa,KAAK,GAAG,kBAAkB,IAAI,GAAG,kBAAkB,IAAI,GAAG,kBAAkB,KAAK,GAAG,GAAG,eAAe,CAAC;wBAC3Q,GAAG,KAAK,GAAG,CAAC,cAAc,aAAa,IAAI,GAAG,kBAAkB,MAAM,GAAG,kBAAkB,MAAM,GAAG,IAAI,kBAAkB,MAAM,EAAE,KAAK,GAAG,CAAC,cAAc,aAAa,IAAI,GAAG,kBAAkB,GAAG,GAAG,kBAAkB,GAAG,GAAG,kBAAkB,MAAM,GAAG,GAAG,eAAe,CAAC;oBAC7Q;oBACA,MAAM,aAAa,cAAc,aAAa,KAAK,IAAI,CAAC,WAAW,cAAc,aAAa,IAAI,IAAI,CAAC;oBACvG,MAAM,aAAa,cAAc,aAAa,IAAI,IAAI,CAAC,YAAY,cAAc,aAAa,EAAE,IAAI,CAAC;oBAErG,IAAI,cAAc,mBAAmB,CAAC,KAAK,eAAe,CAAC,EAAE;wBAC3D,MAAM,uBAAuB,gBAAgB,UAAU,GAAG,iBAAiB,CAAC;wBAC5E,MAAM,4BAA4B,cAAc,aAAa,KAAK,IAAI,wBAAwB,UAAU,CAAC,IAAI,cAAc,aAAa,IAAI,IAAI,wBAAwB,UAAU,CAAC;wBAEnL,IAAI,6BAA6B,CAAC,iBAAiB,CAAC,EAAE;4BACpD,gFAAgF;4BAChF,qDAAqD;4BACrD,gBAAgB,QAAQ,CAAC;gCACvB,MAAM;gCACN,UAAU;4BACZ;4BACA;wBACF;wBAEA,IAAI,2BAA2B;4BAC7B,YAAY,CAAC,GAAG,gBAAgB,UAAU,GAAG;wBAC/C,OAAO;4BACL,YAAY,CAAC,GAAG,cAAc,aAAa,KAAK,GAAG,gBAAgB,UAAU,GAAG,UAAU,CAAC,GAAG,gBAAgB,UAAU,GAAG,UAAU,CAAC;wBACxI;wBAEA,IAAI,YAAY,CAAC,EAAE;4BACjB,gBAAgB,QAAQ,CAAC;gCACvB,MAAM,CAAC,YAAY,CAAC;gCACpB,UAAU;4BACZ;wBACF;wBAEA;oBACF,OAAO,IAAI,cAAc,mBAAmB,CAAC,KAAK,eAAe,CAAC,EAAE;wBAClE,MAAM,uBAAuB,gBAAgB,SAAS,GAAG,iBAAiB,CAAC;wBAC3E,MAAM,4BAA4B,cAAc,aAAa,IAAI,IAAI,wBAAwB,UAAU,CAAC,IAAI,cAAc,aAAa,EAAE,IAAI,wBAAwB,UAAU,CAAC;wBAEhL,IAAI,6BAA6B,CAAC,iBAAiB,CAAC,EAAE;4BACpD,gFAAgF;4BAChF,qDAAqD;4BACrD,gBAAgB,QAAQ,CAAC;gCACvB,KAAK;gCACL,UAAU;4BACZ;4BACA;wBACF;wBAEA,IAAI,2BAA2B;4BAC7B,YAAY,CAAC,GAAG,gBAAgB,SAAS,GAAG;wBAC9C,OAAO;4BACL,YAAY,CAAC,GAAG,cAAc,aAAa,IAAI,GAAG,gBAAgB,SAAS,GAAG,UAAU,CAAC,GAAG,gBAAgB,SAAS,GAAG,UAAU,CAAC;wBACrI;wBAEA,IAAI,YAAY,CAAC,EAAE;4BACjB,gBAAgB,QAAQ,CAAC;gCACvB,KAAK,CAAC,YAAY,CAAC;gCACnB,UAAU;4BACZ;wBACF;wBAEA;oBACF;gBACF;gBAEA,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,SAAS,gBAAgB,IAAI,CAAC,oBAAoB,GAAG;YAClF;QACF;IACF;IAEA,WAAW,KAAK,EAAE,WAAW,EAAE;QAC7B,MAAM,EACJ,MAAM,EACP,GAAG,IAAI,CAAC,KAAK;QACd,MAAM,cAAc;QACpB,OAAO;IACT;IAEA,UAAU,KAAK,EAAE;QACf,MAAM,EACJ,KAAK,EACN,GAAG,IAAI,CAAC,KAAK;QACd,MAAM,cAAc;QACpB,IAAI,CAAC,MAAM;QACX;IACF;IAEA,aAAa,KAAK,EAAE;QAClB,MAAM,EACJ,QAAQ,EACT,GAAG,IAAI,CAAC,KAAK;QACd,MAAM,cAAc;QACpB,IAAI,CAAC,MAAM;QACX;IACF;IAEA,SAAS;QACP,IAAI,CAAC,SAAS,CAAC,SAAS;QACxB,IAAI,CAAC,eAAe,CAAC,SAAS;IAChC;AAEF;AACA,eAAe,UAAU,GAAG;IAAC;QAC3B,WAAW;QACX,SAAS,CAAC,OAAO,MAAM;YACrB,IAAI,EACF,gBAAgB,oBAAoB,EACpC,YAAY,EACb,GAAG;YACJ,IAAI,EACF,MAAM,EACP,GAAG;YACJ,MAAM,EACJ,IAAI,EACL,GAAG,MAAM,WAAW;YAErB,IAAI,cAAc,KAAK,CAAC,QAAQ,CAAC,OAAO;gBACtC,MAAM,YAAY,OAAO,aAAa,CAAC,OAAO;gBAE9C,IAAI,aAAa,MAAM,MAAM,KAAK,WAAW;oBAC3C,OAAO;gBACT;gBAEA,MAAM,cAAc;gBACpB,gBAAgB,OAAO,KAAK,IAAI,aAAa;oBAC3C,OAAO,MAAM,WAAW;gBAC1B;gBACA,OAAO;YACT;YAEA,OAAO;QACT;IACF;CAAE;AAEF,SAAS,qBAAqB,UAAU;IACtC,OAAO,QAAQ,cAAc,cAAc;AAC7C;AAEA,SAAS,kBAAkB,UAAU;IACnC,OAAO,QAAQ,cAAc,WAAW;AAC1C;AAEA,MAAM;IACJ,YAAY,KAAK,EAAE,MAAM,EAAE,cAAc,CAAE;QACzC,IAAI;QAEJ,IAAI,mBAAmB,KAAK,GAAG;YAC7B,iBAAiB,uBAAuB,MAAM,KAAK,CAAC,MAAM;QAC5D;QAEA,IAAI,CAAC,KAAK,GAAG,KAAK;QAClB,IAAI,CAAC,MAAM,GAAG,KAAK;QACnB,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,QAAQ,GAAG,KAAK;QACrB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,kBAAkB,GAAG,KAAK;QAC/B,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC,iBAAiB,GAAG,KAAK;QAC9B,IAAI,CAAC,eAAe,GAAG,KAAK;QAC5B,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,GAAG;QACd,MAAM,EACJ,KAAK,EACN,GAAG;QACJ,MAAM,EACJ,MAAM,EACP,GAAG;QACJ,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,QAAQ,GAAG,iBAAiB;QACjC,IAAI,CAAC,iBAAiB,GAAG,IAAI,UAAU,IAAI,CAAC,QAAQ;QACpD,IAAI,CAAC,SAAS,GAAG,IAAI,UAAU;QAC/B,IAAI,CAAC,eAAe,GAAG,IAAI,UAAU,UAAU;QAC/C,IAAI,CAAC,kBAAkB,GAAG,CAAC,uBAAuB,oBAAoB,MAAM,KAAK,OAAO,uBAAuB;QAC/G,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI;QAC7C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI;QAC3C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI;QACzC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI;QAC/C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI;QACjD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI;QAC7D,IAAI,CAAC,MAAM;IACb;IAEA,SAAS;QACP,MAAM,EACJ,MAAM,EACN,OAAO,EACL,SAAS,EACP,oBAAoB,EACpB,0BAA0B,EAC3B,EACF,EACF,GAAG,IAAI;QACR,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,EAAE;YACpD,SAAS;QACX;QACA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS;QAElD,IAAI,OAAO,MAAM,EAAE;YACjB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY;QAC1D;QAEA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,MAAM,EAAE,IAAI,CAAC,YAAY;QAC5D,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,SAAS,EAAE;QAC9C,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,gBAAgB,EAAE,IAAI,CAAC,YAAY;QACtE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,WAAW,EAAE;QAChD,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,UAAU,OAAO,EAAE,IAAI,CAAC,aAAa;QAEhE,IAAI,sBAAsB;YACxB,IAAI,8BAA8B,QAAQ,2BAA2B;gBACnE,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;gBACvB,YAAY,IAAI,CAAC,KAAK,CAAC,UAAU;gBACjC,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO;YAC7B,IAAI;gBACF,OAAO,IAAI,CAAC,WAAW;YACzB;YAEA,IAAI,kBAAkB,uBAAuB;gBAC3C,IAAI,CAAC,SAAS,GAAG,WAAW,IAAI,CAAC,WAAW,EAAE,qBAAqB,KAAK;gBACxE,IAAI,CAAC,aAAa,CAAC;gBACnB;YACF;YAEA,IAAI,qBAAqB,uBAAuB;gBAC9C,IAAI,CAAC,aAAa,CAAC;gBACnB;YACF;QACF;QAEA,IAAI,CAAC,WAAW;IAClB;IAEA,SAAS;QACP,IAAI,CAAC,SAAS,CAAC,SAAS;QACxB,IAAI,CAAC,eAAe,CAAC,SAAS,IAAI,oEAAoE;QACtG,yFAAyF;QAEzF,WAAW,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE;QAE7C,IAAI,IAAI,CAAC,SAAS,KAAK,MAAM;YAC3B,aAAa,IAAI,CAAC,SAAS;YAC3B,IAAI,CAAC,SAAS,GAAG;QACnB;IACF;IAEA,cAAc,UAAU,EAAE,MAAM,EAAE;QAChC,MAAM,EACJ,MAAM,EACN,SAAS,EACV,GAAG,IAAI,CAAC,KAAK;QACd,UAAU,QAAQ,YAAY,IAAI,CAAC,kBAAkB,EAAE;IACzD;IAEA,cAAc;QACZ,MAAM,EACJ,kBAAkB,EACnB,GAAG,IAAI;QACR,MAAM,EACJ,OAAO,EACR,GAAG,IAAI,CAAC,KAAK;QAEd,IAAI,oBAAoB;YACtB,IAAI,CAAC,SAAS,GAAG,MAAM,uEAAuE;YAE9F,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,UAAU,KAAK,EAAE,iBAAiB;gBAC3D,SAAS;YACX,IAAI,8CAA8C;YAElD,IAAI,CAAC,mBAAmB,IAAI,gDAAgD;YAE5E,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,UAAU,eAAe,EAAE,IAAI,CAAC,mBAAmB;YAC9E,QAAQ;QACV;IACF;IAEA,WAAW,KAAK,EAAE;QAChB,IAAI;QAEJ,MAAM,EACJ,SAAS,EACT,kBAAkB,EAClB,KAAK,EACN,GAAG,IAAI;QACR,MAAM,EACJ,MAAM,EACN,SAAS,EACP,oBAAoB,EACrB,EACF,GAAG;QAEJ,IAAI,CAAC,oBAAoB;YACvB;QACF;QAEA,MAAM,cAAc,CAAC,wBAAwB,oBAAoB,MAAM,KAAK,OAAO,wBAAwB;QAC3G,MAAM,QAAQ,SAAS,oBAAoB,cAAc,wBAAwB;QAEjF,IAAI,CAAC,aAAa,sBAAsB;YACtC,IAAI,qBAAqB,uBAAuB;gBAC9C,IAAI,qBAAqB,SAAS,IAAI,QAAQ,oBAAoB,OAAO,qBAAqB,SAAS,GAAG;oBACxG,OAAO,IAAI,CAAC,YAAY;gBAC1B;gBAEA,IAAI,oBAAoB,OAAO,qBAAqB,QAAQ,GAAG;oBAC7D,OAAO,IAAI,CAAC,WAAW;gBACzB;YACF;YAEA,IAAI,kBAAkB,uBAAuB;gBAC3C,IAAI,oBAAoB,OAAO,qBAAqB,SAAS,GAAG;oBAC9D,OAAO,IAAI,CAAC,YAAY;gBAC1B;YACF;YAEA,IAAI,CAAC,aAAa,CAAC,sBAAsB;YACzC;QACF;QAEA,IAAI,MAAM,UAAU,EAAE;YACpB,MAAM,cAAc;QACtB;QAEA,OAAO;IACT;IAEA,YAAY;QACV,MAAM,EACJ,OAAO,EACP,KAAK,EACN,GAAG,IAAI,CAAC,KAAK;QACd,IAAI,CAAC,MAAM;QAEX,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,QAAQ,IAAI,CAAC,KAAK,CAAC,MAAM;QAC3B;QAEA;IACF;IAEA,eAAe;QACb,MAAM,EACJ,OAAO,EACP,QAAQ,EACT,GAAG,IAAI,CAAC,KAAK;QACd,IAAI,CAAC,MAAM;QAEX,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,QAAQ,IAAI,CAAC,KAAK,CAAC,MAAM;QAC3B;QAEA;IACF;IAEA,cAAc,KAAK,EAAE;QACnB,IAAI,MAAM,IAAI,KAAK,aAAa,GAAG,EAAE;YACnC,IAAI,CAAC,YAAY;QACnB;IACF;IAEA,sBAAsB;QACpB,IAAI;QAEJ,CAAC,wBAAwB,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,KAAK,OAAO,KAAK,IAAI,sBAAsB,eAAe;IACjH;AAEF;AAEA,MAAM,SAAS;IACb,QAAQ;QACN,MAAM;IACR;IACA,MAAM;QACJ,MAAM;IACR;IACA,KAAK;QACH,MAAM;IACR;AACF;AACA,MAAM,sBAAsB;IAC1B,YAAY,KAAK,CAAE;QACjB,MAAM,EACJ,KAAK,EACN,GAAG,OAAO,uEAAuE;QAClF,8DAA8D;QAE9D,MAAM,iBAAiB,iBAAiB,MAAM,MAAM;QACpD,KAAK,CAAC,OAAO,QAAQ;IACvB;AAEF;AACA,cAAc,UAAU,GAAG;IAAC;QAC1B,WAAW;QACX,SAAS,CAAC,MAAM;YACd,IAAI,EACF,aAAa,KAAK,EACnB,GAAG;YACJ,IAAI,EACF,YAAY,EACb,GAAG;YAEJ,IAAI,CAAC,MAAM,SAAS,IAAI,MAAM,MAAM,KAAK,GAAG;gBAC1C,OAAO;YACT;YAEA,gBAAgB,OAAO,KAAK,IAAI,aAAa;gBAC3C;YACF;YACA,OAAO;QACT;IACF;CAAE;AAEF,MAAM,WAAW;IACf,MAAM;QACJ,MAAM;IACR;IACA,KAAK;QACH,MAAM;IACR;AACF;AACA,IAAI;AAEJ,CAAC,SAAU,WAAW;IACpB,WAAW,CAAC,WAAW,CAAC,aAAa,GAAG,EAAE,GAAG;AAC/C,CAAC,EAAE,eAAe,CAAC,cAAc,CAAC,CAAC;AAEnC,MAAM,oBAAoB;IACxB,YAAY,KAAK,CAAE;QACjB,KAAK,CAAC,OAAO,UAAU,iBAAiB,MAAM,KAAK,CAAC,MAAM;IAC5D;AAEF;AACA,YAAY,UAAU,GAAG;IAAC;QACxB,WAAW;QACX,SAAS,CAAC,MAAM;YACd,IAAI,EACF,aAAa,KAAK,EACnB,GAAG;YACJ,IAAI,EACF,YAAY,EACb,GAAG;YAEJ,IAAI,MAAM,MAAM,KAAK,YAAY,UAAU,EAAE;gBAC3C,OAAO;YACT;YAEA,gBAAgB,OAAO,KAAK,IAAI,aAAa;gBAC3C;YACF;YACA,OAAO;QACT;IACF;CAAE;AAEF,MAAM,WAAW;IACf,QAAQ;QACN,MAAM;IACR;IACA,MAAM;QACJ,MAAM;IACR;IACA,KAAK;QACH,MAAM;IACR;AACF;AACA,MAAM,oBAAoB;IACxB,YAAY,KAAK,CAAE;QACjB,KAAK,CAAC,OAAO;IACf;IAEA,OAAO,QAAQ;QACb,qEAAqE;QACrE,uEAAuE;QACvE,6DAA6D;QAC7D,OAAO,gBAAgB,CAAC,SAAS,IAAI,CAAC,IAAI,EAAE,MAAM;YAChD,SAAS;YACT,SAAS;QACX;QACA,OAAO,SAAS;YACd,OAAO,mBAAmB,CAAC,SAAS,IAAI,CAAC,IAAI,EAAE;QACjD,GAAG,0EAA0E;;QAC7E,4EAA4E;QAE5E,SAAS,QAAQ;IACnB;AAEF;AACA,YAAY,UAAU,GAAG;IAAC;QACxB,WAAW;QACX,SAAS,CAAC,MAAM;YACd,IAAI,EACF,aAAa,KAAK,EACnB,GAAG;YACJ,IAAI,EACF,YAAY,EACb,GAAG;YACJ,MAAM,EACJ,OAAO,EACR,GAAG;YAEJ,IAAI,QAAQ,MAAM,GAAG,GAAG;gBACtB,OAAO;YACT;YAEA,gBAAgB,OAAO,KAAK,IAAI,aAAa;gBAC3C;YACF;YACA,OAAO;QACT;IACF;CAAE;AAEF,IAAI;AAEJ,CAAC,SAAU,mBAAmB;IAC5B,mBAAmB,CAAC,mBAAmB,CAAC,UAAU,GAAG,EAAE,GAAG;IAC1D,mBAAmB,CAAC,mBAAmB,CAAC,gBAAgB,GAAG,EAAE,GAAG;AAClE,CAAC,EAAE,uBAAuB,CAAC,sBAAsB,CAAC,CAAC;AAEnD,IAAI;AAEJ,CAAC,SAAU,cAAc;IACvB,cAAc,CAAC,cAAc,CAAC,YAAY,GAAG,EAAE,GAAG;IAClD,cAAc,CAAC,cAAc,CAAC,oBAAoB,GAAG,EAAE,GAAG;AAC5D,CAAC,EAAE,kBAAkB,CAAC,iBAAiB,CAAC,CAAC;AAEzC,SAAS,gBAAgB,IAAI;IAC3B,IAAI,EACF,YAAY,EACZ,YAAY,oBAAoB,OAAO,EACvC,SAAS,EACT,YAAY,EACZ,OAAO,EACP,WAAW,CAAC,EACZ,QAAQ,eAAe,SAAS,EAChC,kBAAkB,EAClB,mBAAmB,EACnB,uBAAuB,EACvB,KAAK,EACL,SAAS,EACV,GAAG;IACJ,MAAM,eAAe,gBAAgB;QACnC;QACA,UAAU,CAAC;IACb;IACA,MAAM,CAAC,uBAAuB,wBAAwB,GAAG;IACzD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;QACzB,GAAG;QACH,GAAG;IACL;IACA,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;QAC7B,GAAG;QACH,GAAG;IACL;IACA,MAAM,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACnB,OAAQ;YACN,KAAK,oBAAoB,OAAO;gBAC9B,OAAO,qBAAqB;oBAC1B,KAAK,mBAAmB,CAAC;oBACzB,QAAQ,mBAAmB,CAAC;oBAC5B,MAAM,mBAAmB,CAAC;oBAC1B,OAAO,mBAAmB,CAAC;gBAC7B,IAAI;YAEN,KAAK,oBAAoB,aAAa;gBACpC,OAAO;QACX;IACF,GAAG;QAAC;QAAW;QAAc;KAAmB;IAChD,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAClC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,MAAM,kBAAkB,mBAAmB,OAAO;QAElD,IAAI,CAAC,iBAAiB;YACpB;QACF;QAEA,MAAM,aAAa,YAAY,OAAO,CAAC,CAAC,GAAG,gBAAgB,OAAO,CAAC,CAAC;QACpE,MAAM,YAAY,YAAY,OAAO,CAAC,CAAC,GAAG,gBAAgB,OAAO,CAAC,CAAC;QACnE,gBAAgB,QAAQ,CAAC,YAAY;IACvC,GAAG,EAAE;IACL,MAAM,4BAA4B,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,UAAU,eAAe,SAAS,GAAG;eAAI;SAAoB,CAAC,OAAO,KAAK,qBAAqB;QAAC;QAAO;KAAoB;IAC3K,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,oBAAoB,MAAM,IAAI,CAAC,MAAM;YACpD;YACA;QACF;QAEA,KAAK,MAAM,mBAAmB,0BAA2B;YACvD,IAAI,CAAC,aAAa,OAAO,KAAK,IAAI,UAAU,gBAAgB,MAAM,OAAO;gBACvE;YACF;YAEA,MAAM,QAAQ,oBAAoB,OAAO,CAAC;YAC1C,MAAM,sBAAsB,uBAAuB,CAAC,MAAM;YAE1D,IAAI,CAAC,qBAAqB;gBACxB;YACF;YAEA,MAAM,EACJ,SAAS,EACT,KAAK,EACN,GAAG,2BAA2B,iBAAiB,qBAAqB,MAAM,cAAc;YAEzF,KAAK,MAAM,QAAQ;gBAAC;gBAAK;aAAI,CAAE;gBAC7B,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;oBACxC,KAAK,CAAC,KAAK,GAAG;oBACd,SAAS,CAAC,KAAK,GAAG;gBACpB;YACF;YAEA,IAAI,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC,GAAG,GAAG;gBAC9B;gBACA,mBAAmB,OAAO,GAAG;gBAC7B,sBAAsB,YAAY;gBAClC,YAAY,OAAO,GAAG;gBACtB,gBAAgB,OAAO,GAAG;gBAC1B;YACF;QACF;QAEA,YAAY,OAAO,GAAG;YACpB,GAAG;YACH,GAAG;QACL;QACA,gBAAgB,OAAO,GAAG;YACxB,GAAG;YACH,GAAG;QACL;QACA;IACF,GACA;QAAC;QAAc;QAAY;QAAW;QAAyB;QAAS;QACxE,KAAK,SAAS,CAAC;QACf,KAAK,SAAS,CAAC;QAAe;QAAuB;QAAqB;QAA2B;QACrG,KAAK,SAAS,CAAC;KAAW;AAC5B;AACA,MAAM,sBAAsB;IAC1B,GAAG;QACD,CAAC,UAAU,QAAQ,CAAC,EAAE;QACtB,CAAC,UAAU,OAAO,CAAC,EAAE;IACvB;IACA,GAAG;QACD,CAAC,UAAU,QAAQ,CAAC,EAAE;QACtB,CAAC,UAAU,OAAO,CAAC,EAAE;IACvB;AACF;AAEA,SAAS,gBAAgB,KAAK;IAC5B,IAAI,EACF,KAAK,EACL,QAAQ,EACT,GAAG;IACJ,MAAM,gBAAgB,YAAY;IAClC,OAAO,YAAY,CAAA;QACjB,IAAI,YAAY,CAAC,iBAAiB,CAAC,gBAAgB;YACjD,+DAA+D;YAC/D,OAAO;QACT;QAEA,MAAM,YAAY;YAChB,GAAG,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,cAAc,CAAC;YACtC,GAAG,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,cAAc,CAAC;QACxC,GAAG,0EAA0E;QAE7E,OAAO;YACL,GAAG;gBACD,CAAC,UAAU,QAAQ,CAAC,EAAE,eAAe,CAAC,CAAC,UAAU,QAAQ,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC;gBAC/E,CAAC,UAAU,OAAO,CAAC,EAAE,eAAe,CAAC,CAAC,UAAU,OAAO,CAAC,IAAI,UAAU,CAAC,KAAK;YAC9E;YACA,GAAG;gBACD,CAAC,UAAU,QAAQ,CAAC,EAAE,eAAe,CAAC,CAAC,UAAU,QAAQ,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC;gBAC/E,CAAC,UAAU,OAAO,CAAC,EAAE,eAAe,CAAC,CAAC,UAAU,OAAO,CAAC,IAAI,UAAU,CAAC,KAAK;YAC9E;QACF;IACF,GAAG;QAAC;QAAU;QAAO;KAAc;AACrC;AAEA,SAAS,cAAc,cAAc,EAAE,EAAE;IACvC,MAAM,gBAAgB,MAAM,OAAO,eAAe,GAAG,CAAC,MAAM;IAC5D,MAAM,OAAO,gBAAgB,cAAc,IAAI,CAAC,OAAO,GAAG;IAC1D,OAAO,YAAY,CAAA;QACjB,IAAI;QAEJ,IAAI,MAAM,MAAM;YACd,OAAO;QACT,EAAE,+DAA+D;QACjE,+DAA+D;QAC/D,sDAAsD;QAGtD,OAAO,CAAC,OAAO,QAAQ,OAAO,OAAO,UAAU,KAAK,OAAO,OAAO;IACpE,GAAG;QAAC;QAAM;KAAG;AACf;AAEA,SAAS,qBAAqB,OAAO,EAAE,mBAAmB;IACxD,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,QAAQ,MAAM,CAAC,CAAC,aAAa;YAChD,MAAM,EACJ,QAAQ,MAAM,EACf,GAAG;YACJ,MAAM,mBAAmB,OAAO,UAAU,CAAC,GAAG,CAAC,CAAA,YAAa,CAAC;oBAC3D,WAAW,UAAU,SAAS;oBAC9B,SAAS,oBAAoB,UAAU,OAAO,EAAE;gBAClD,CAAC;YACD,OAAO;mBAAI;mBAAgB;aAAiB;QAC9C,GAAG,EAAE,GAAG;QAAC;QAAS;KAAoB;AACxC;AAEA,IAAI;AAEJ,CAAC,SAAU,iBAAiB;IAC1B,iBAAiB,CAAC,iBAAiB,CAAC,SAAS,GAAG,EAAE,GAAG;IACrD,iBAAiB,CAAC,iBAAiB,CAAC,iBAAiB,GAAG,EAAE,GAAG;IAC7D,iBAAiB,CAAC,iBAAiB,CAAC,gBAAgB,GAAG,EAAE,GAAG;AAC9D,CAAC,EAAE,qBAAqB,CAAC,oBAAoB,CAAC,CAAC;AAE/C,IAAI;AAEJ,CAAC,SAAU,kBAAkB;IAC3B,kBAAkB,CAAC,YAAY,GAAG;AACpC,CAAC,EAAE,sBAAsB,CAAC,qBAAqB,CAAC,CAAC;AAEjD,MAAM,eAAe,WAAW,GAAE,IAAI;AACtC,SAAS,sBAAsB,UAAU,EAAE,IAAI;IAC7C,IAAI,EACF,QAAQ,EACR,YAAY,EACZ,MAAM,EACP,GAAG;IACJ,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,EACJ,SAAS,EACT,OAAO,EACP,QAAQ,EACT,GAAG;IACJ,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC7B,MAAM,WAAW;IACjB,MAAM,cAAc,eAAe;IACnC,MAAM,6BAA6B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,SAAU,GAAG;QAC1D,IAAI,QAAQ,KAAK,GAAG;YAClB,MAAM,EAAE;QACV;QAEA,IAAI,YAAY,OAAO,EAAE;YACvB;QACF;QAEA,SAAS,CAAA;YACP,IAAI,UAAU,MAAM;gBAClB,OAAO;YACT;YAEA,OAAO,MAAM,MAAM,CAAC,IAAI,MAAM,CAAC,CAAA,KAAM,CAAC,MAAM,QAAQ,CAAC;QACvD;IACF,GAAG;QAAC;KAAY;IAChB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,iBAAiB,YAAY,CAAA;QACjC,IAAI,YAAY,CAAC,UAAU;YACzB,OAAO;QACT;QAEA,IAAI,CAAC,iBAAiB,kBAAkB,gBAAgB,cAAc,OAAO,KAAK,cAAc,SAAS,MAAM;YAC7G,MAAM,MAAM,IAAI;YAEhB,KAAK,IAAI,aAAa,WAAY;gBAChC,IAAI,CAAC,WAAW;oBACd;gBACF;gBAEA,IAAI,SAAS,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,QAAQ,CAAC,UAAU,EAAE,KAAK,UAAU,IAAI,CAAC,OAAO,EAAE;oBACxF,iDAAiD;oBACjD,IAAI,GAAG,CAAC,UAAU,EAAE,EAAE,UAAU,IAAI,CAAC,OAAO;oBAC5C;gBACF;gBAEA,MAAM,OAAO,UAAU,IAAI,CAAC,OAAO;gBACnC,MAAM,OAAO,OAAO,IAAI,KAAK,QAAQ,OAAO,QAAQ;gBACpD,UAAU,IAAI,CAAC,OAAO,GAAG;gBAEzB,IAAI,MAAM;oBACR,IAAI,GAAG,CAAC,UAAU,EAAE,EAAE;gBACxB;YACF;YAEA,OAAO;QACT;QAEA,OAAO;IACT,GAAG;QAAC;QAAY;QAAO;QAAU;QAAU;KAAQ;IACnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc,OAAO,GAAG;IAC1B,GAAG;QAAC;KAAW;IACf,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ;QACF;QAEA;IACF,GACA;QAAC;QAAU;KAAS;IACpB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;YAC7B,SAAS;QACX;IACF,GACA;QAAC,KAAK,SAAS,CAAC;KAAO;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,OAAO,cAAc,YAAY,UAAU,OAAO,KAAK,MAAM;YAC3E;QACF;QAEA,UAAU,OAAO,GAAG,WAAW;YAC7B;YACA,UAAU,OAAO,GAAG;QACtB,GAAG;IACL,GACA;QAAC;QAAW;QAAU;WAA+B;KAAa;IAClE,OAAO;QACL;QACA;QACA,oBAAoB,SAAS;IAC/B;;IAEA,SAAS;QACP,OAAQ;YACN,KAAK,kBAAkB,MAAM;gBAC3B,OAAO;YAET,KAAK,kBAAkB,cAAc;gBACnC,OAAO;YAET;gBACE,OAAO,CAAC;QACZ;IACF;AACF;AAEA,SAAS,gBAAgB,KAAK,EAAE,SAAS;IACvC,OAAO,YAAY,CAAA;QACjB,IAAI,CAAC,OAAO;YACV,OAAO;QACT;QAEA,IAAI,eAAe;YACjB,OAAO;QACT;QAEA,OAAO,OAAO,cAAc,aAAa,UAAU,SAAS;IAC9D,GAAG;QAAC;QAAW;KAAM;AACvB;AAEA,SAAS,eAAe,IAAI,EAAE,OAAO;IACnC,OAAO,gBAAgB,MAAM;AAC/B;AAEA;;;CAGC,GAED,SAAS,oBAAoB,IAAI;IAC/B,IAAI,EACF,QAAQ,EACR,QAAQ,EACT,GAAG;IACJ,MAAM,kBAAkB,SAAS;IACjC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC/B,IAAI,YAAY,OAAO,WAAW,eAAe,OAAO,OAAO,gBAAgB,KAAK,aAAa;YAC/F,OAAO;QACT;QAEA,MAAM,EACJ,gBAAgB,EACjB,GAAG;QACJ,OAAO,IAAI,iBAAiB;IAC9B,GAAG;QAAC;QAAiB;KAAS;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO,IAAM,oBAAoB,OAAO,KAAK,IAAI,iBAAiB,UAAU;IAC9E,GAAG;QAAC;KAAiB;IACrB,OAAO;AACT;AAEA;;;CAGC,GAED,SAAS,kBAAkB,IAAI;IAC7B,IAAI,EACF,QAAQ,EACR,QAAQ,EACT,GAAG;IACJ,MAAM,eAAe,SAAS;IAC9B,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC7B,IAAI,YAAY,OAAO,WAAW,eAAe,OAAO,OAAO,cAAc,KAAK,aAAa;YAC7F,OAAO;QACT;QAEA,MAAM,EACJ,gBAAA,eAAc,EACf,GAAG;QACJ,OAAO,IAAI,gBAAe;IAC5B,GACA;QAAC;KAAS;IACV,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO,IAAM,kBAAkB,OAAO,KAAK,IAAI,eAAe,UAAU;IAC1E,GAAG;QAAC;KAAe;IACnB,OAAO;AACT;AAEA,SAAS,eAAe,OAAO;IAC7B,OAAO,IAAI,KAAK,cAAc,UAAU;AAC1C;AAEA,SAAS,QAAQ,OAAO,EAAE,OAAO,EAAE,YAAY;IAC7C,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU;IACZ;IAEA,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,SAAS;QACP,QAAQ,CAAA;YACN,IAAI,CAAC,SAAS;gBACZ,OAAO;YACT;YAEA,IAAI,QAAQ,WAAW,KAAK,OAAO;gBACjC,IAAI;gBAEJ,uDAAuD;gBACvD,kCAAkC;gBAClC,OAAO,CAAC,OAAO,eAAe,OAAO,cAAc,YAAY,KAAK,OAAO,OAAO;YACpF;YAEA,MAAM,UAAU,QAAQ;YAExB,IAAI,KAAK,SAAS,CAAC,iBAAiB,KAAK,SAAS,CAAC,UAAU;gBAC3D,OAAO;YACT;YAEA,OAAO;QACT;IACF;IAEA,MAAM,mBAAmB,oBAAoB;QAC3C,UAAS,OAAO;YACd,IAAI,CAAC,SAAS;gBACZ;YACF;YAEA,KAAK,MAAM,UAAU,QAAS;gBAC5B,MAAM,EACJ,IAAI,EACJ,MAAM,EACP,GAAG;gBAEJ,IAAI,SAAS,eAAe,kBAAkB,eAAe,OAAO,QAAQ,CAAC,UAAU;oBACrF;oBACA;gBACF;YACF;QACF;IAEF;IACA,MAAM,iBAAiB,kBAAkB;QACvC,UAAU;IACZ;IACA,0BAA0B;QACxB;QAEA,IAAI,SAAS;YACX,kBAAkB,OAAO,KAAK,IAAI,eAAe,OAAO,CAAC;YACzD,oBAAoB,OAAO,KAAK,IAAI,iBAAiB,OAAO,CAAC,SAAS,IAAI,EAAE;gBAC1E,WAAW;gBACX,SAAS;YACX;QACF,OAAO;YACL,kBAAkB,OAAO,KAAK,IAAI,eAAe,UAAU;YAC3D,oBAAoB,OAAO,KAAK,IAAI,iBAAiB,UAAU;QACjE;IACF,GAAG;QAAC;KAAQ;IACZ,OAAO;AACT;AAEA,SAAS,aAAa,IAAI;IACxB,MAAM,cAAc,gBAAgB;IACpC,OAAO,aAAa,MAAM;AAC5B;AAEA,MAAM,iBAAiB,EAAE;AACzB,SAAS,uBAAuB,IAAI;IAClC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,MAAM,YAAY,YAAY,CAAA;QAC5B,IAAI,CAAC,MAAM;YACT,OAAO;QACT;QAEA,IAAI,iBAAiB,kBAAkB,kBAAkB,QAAQ,aAAa,OAAO,IAAI,KAAK,UAAU,KAAK,aAAa,OAAO,CAAC,UAAU,EAAE;YAC5I,OAAO;QACT;QAEA,OAAO,uBAAuB;IAChC,GAAG;QAAC;KAAK;IACT,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa,OAAO,GAAG;IACzB,GAAG;QAAC;KAAK;IACT,OAAO;AACT;AAEA,SAAS,iBAAiB,QAAQ;IAChC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,WAAW,4CAA4C;IAEnF,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAA;QAC/B,MAAM,mBAAmB,qBAAqB,MAAM,MAAM;QAE1D,IAAI,CAAC,kBAAkB;YACrB;QACF;QAEA,qBAAqB,CAAA;YACnB,IAAI,CAAC,mBAAmB;gBACtB,OAAO;YACT;YAEA,kBAAkB,GAAG,CAAC,kBAAkB,qBAAqB;YAC7D,OAAO,IAAI,IAAI;QACjB;IACF,GAAG,EAAE;IACL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB,aAAa,OAAO;QAE7C,IAAI,aAAa,kBAAkB;YACjC,QAAQ;YACR,MAAM,UAAU,SAAS,GAAG,CAAC,CAAA;gBAC3B,MAAM,oBAAoB,qBAAqB;gBAE/C,IAAI,mBAAmB;oBACrB,kBAAkB,gBAAgB,CAAC,UAAU,cAAc;wBACzD,SAAS;oBACX;oBACA,OAAO;wBAAC;wBAAmB,qBAAqB;qBAAmB;gBACrE;gBAEA,OAAO;YACT,GAAG,MAAM,CAAC,CAAA,QAAS,SAAS;YAC5B,qBAAqB,QAAQ,MAAM,GAAG,IAAI,IAAI,WAAW;YACzD,aAAa,OAAO,GAAG;QACzB;QAEA,OAAO;YACL,QAAQ;YACR,QAAQ;QACV;;QAEA,SAAS,QAAQ,QAAQ;YACvB,SAAS,OAAO,CAAC,CAAA;gBACf,MAAM,oBAAoB,qBAAqB;gBAC/C,qBAAqB,OAAO,KAAK,IAAI,kBAAkB,mBAAmB,CAAC,UAAU;YACvF;QACF;IACF,GAAG;QAAC;QAAc;KAAS;IAC3B,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACb,IAAI,SAAS,MAAM,EAAE;YACnB,OAAO,oBAAoB,MAAM,IAAI,CAAC,kBAAkB,MAAM,IAAI,MAAM,CAAC,CAAC,KAAK,cAAgB,IAAI,KAAK,cAAc,sBAAsB,iBAAiB;QAC/J;QAEA,OAAO;IACT,GAAG;QAAC;QAAU;KAAkB;AAClC;AAEA,SAAS,sBAAsB,aAAa,EAAE,YAAY;IACxD,IAAI,iBAAiB,KAAK,GAAG;QAC3B,eAAe,EAAE;IACnB;IAEA,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,qBAAqB,OAAO,GAAG;IACjC,GACA;IACA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB,kBAAkB;QAE3C,IAAI,oBAAoB,CAAC,qBAAqB,OAAO,EAAE;YACrD,qBAAqB,OAAO,GAAG;QACjC;QAEA,IAAI,CAAC,oBAAoB,qBAAqB,OAAO,EAAE;YACrD,qBAAqB,OAAO,GAAG;QACjC;IACF,GAAG;QAAC;KAAc;IAClB,OAAO,qBAAqB,OAAO,GAAG,SAAS,eAAe,qBAAqB,OAAO,IAAI;AAChG;AAEA,SAAS,eAAe,OAAO;IAC7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW;YACd;QACF;QAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,CAAA;YAC9B,IAAI,EACF,MAAM,EACP,GAAG;YACJ,OAAO,OAAO,KAAK,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK;QACrD;QACA,OAAO;YACL,KAAK,MAAM,YAAY,YAAa;gBAClC,YAAY,OAAO,KAAK,IAAI;YAC9B;QACF;IACF,GACA,uDAAuD;IACvD,QAAQ,GAAG,CAAC,CAAA;QACV,IAAI,EACF,MAAM,EACP,GAAG;QACJ,OAAO;IACT;AACF;AAEA,SAAS,sBAAsB,SAAS,EAAE,EAAE;IAC1C,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACb,OAAO,UAAU,MAAM,CAAC,CAAC,KAAK;YAC5B,IAAI,EACF,SAAS,EACT,OAAO,EACR,GAAG;YAEJ,GAAG,CAAC,UAAU,GAAG,CAAA;gBACf,QAAQ,OAAO;YACjB;YAEA,OAAO;QACT,GAAG,CAAC;IACN,GAAG;QAAC;QAAW;KAAG;AACpB;AAEA,SAAS,cAAc,OAAO;IAC5B,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,UAAU,oBAAoB,WAAW,MAAM;QAAC;KAAQ;AAC/E;AAEA,MAAM,iBAAiB,EAAE;AACzB,SAAS,SAAS,QAAQ,EAAE,OAAO;IACjC,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU;IACZ;IAEA,MAAM,CAAC,aAAa,GAAG;IACvB,MAAM,aAAa,cAAc,eAAe,UAAU,gBAAgB;IAC1E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,SAAS;QACP,SAAS;YACP,IAAI,CAAC,SAAS,MAAM,EAAE;gBACpB,OAAO;YACT;YAEA,OAAO,SAAS,GAAG,CAAC,CAAA,UAAW,2BAA2B,WAAW,aAAa,IAAI,KAAK,QAAQ,UAAU;QAC/G;IACF;IAEA,MAAM,iBAAiB,kBAAkB;QACvC,UAAU;IACZ;IACA,0BAA0B;QACxB,kBAAkB,OAAO,KAAK,IAAI,eAAe,UAAU;QAC3D;QACA,SAAS,OAAO,CAAC,CAAA,UAAW,kBAAkB,OAAO,KAAK,IAAI,eAAe,OAAO,CAAC;IACvF,GAAG;QAAC;KAAS;IACb,OAAO;AACT;AAEA,SAAS,kBAAkB,IAAI;IAC7B,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,GAAG;QAC5B,OAAO;IACT;IAEA,MAAM,aAAa,KAAK,QAAQ,CAAC,EAAE;IACnC,OAAO,cAAc,cAAc,aAAa;AAClD;AAEA,SAAS,wBAAwB,IAAI;IACnC,IAAI,EACF,OAAO,EACR,GAAG;IACJ,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAA;QAC/B,KAAK,MAAM,EACT,MAAM,EACP,IAAI,QAAS;YACZ,IAAI,cAAc,SAAS;gBACzB,QAAQ,CAAA;oBACN,MAAM,UAAU,QAAQ;oBACxB,OAAO,OAAO;wBAAE,GAAG,IAAI;wBACrB,OAAO,QAAQ,KAAK;wBACpB,QAAQ,QAAQ,MAAM;oBACxB,IAAI;gBACN;gBACA;YACF;QACF;IACF,GAAG;QAAC;KAAQ;IACZ,MAAM,iBAAiB,kBAAkB;QACvC,UAAU;IACZ;IACA,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAA;QACnC,MAAM,OAAO,kBAAkB;QAC/B,kBAAkB,OAAO,KAAK,IAAI,eAAe,UAAU;QAE3D,IAAI,MAAM;YACR,kBAAkB,OAAO,KAAK,IAAI,eAAe,OAAO,CAAC;QAC3D;QAEA,QAAQ,OAAO,QAAQ,QAAQ;IACjC,GAAG;QAAC;QAAS;KAAe;IAC5B,MAAM,CAAC,SAAS,OAAO,GAAG,WAAW;IACrC,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC;YACpB;YACA;YACA;QACF,CAAC,GAAG;QAAC;QAAM;QAAS;KAAO;AAC7B;AAEA,MAAM,iBAAiB;IAAC;QACtB,QAAQ;QACR,SAAS,CAAC;IACZ;IAAG;QACD,QAAQ;QACR,SAAS,CAAC;IACZ;CAAE;AACF,MAAM,cAAc;IAClB,SAAS,CAAC;AACZ;AACA,MAAM,gCAAgC;IACpC,WAAW;QACT,SAAS;IACX;IACA,WAAW;QACT,SAAS;QACT,UAAU,kBAAkB,aAAa;QACzC,WAAW,mBAAmB,SAAS;IACzC;IACA,aAAa;QACX,SAAS;IACX;AACF;AAEA,MAAM,+BAA+B;IACnC,IAAI,EAAE,EAAE;QACN,IAAI;QAEJ,OAAO,MAAM,OAAO,CAAC,aAAa,KAAK,CAAC,IAAI,GAAG,KAAK,OAAO,aAAa,YAAY;IACtF;IAEA,UAAU;QACR,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM;IAC/B;IAEA,aAAa;QACX,OAAO,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,CAAA;YAC3B,IAAI,EACF,QAAQ,EACT,GAAG;YACJ,OAAO,CAAC;QACV;IACF;IAEA,WAAW,EAAE,EAAE;QACb,IAAI,uBAAuB;QAE3B,OAAO,CAAC,wBAAwB,CAAC,YAAY,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,OAAO,KAAK,IAAI,UAAU,IAAI,CAAC,OAAO,KAAK,OAAO,wBAAwB;IAC1I;AAEF;AAEA,MAAM,uBAAuB;IAC3B,gBAAgB;IAChB,QAAQ;IACR,YAAY;IACZ,gBAAgB;IAChB,YAAY;IACZ,mBAAmB;IACnB,gBAAgB,WAAW,GAAE,IAAI;IACjC,gBAAgB,WAAW,GAAE,IAAI;IACjC,qBAAqB,WAAW,GAAE,IAAI;IACtC,MAAM;IACN,aAAa;QACX,SAAS;YACP,SAAS;QACX;QACA,MAAM;QACN,QAAQ;IACV;IACA,qBAAqB,EAAE;IACvB,yBAAyB,EAAE;IAC3B,wBAAwB;IACxB,4BAA4B;IAC5B,YAAY;IACZ,oBAAoB;AACtB;AACA,MAAM,yBAAyB;IAC7B,gBAAgB;IAChB,YAAY,EAAE;IACd,QAAQ;IACR,gBAAgB;IAChB,mBAAmB;QACjB,WAAW;IACb;IACA,UAAU;IACV,gBAAgB,WAAW,GAAE,IAAI;IACjC,MAAM;IACN,4BAA4B;AAC9B;AACA,MAAM,kBAAkB,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE;AACnD,MAAM,gBAAgB,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE;AAEjD,SAAS;IACP,OAAO;QACL,WAAW;YACT,QAAQ;YACR,oBAAoB;gBAClB,GAAG;gBACH,GAAG;YACL;YACA,OAAO,IAAI;YACX,WAAW;gBACT,GAAG;gBACH,GAAG;YACL;QACF;QACA,WAAW;YACT,YAAY,IAAI;QAClB;IACF;AACF;AACA,SAAS,QAAQ,KAAK,EAAE,MAAM;IAC5B,OAAQ,OAAO,IAAI;QACjB,KAAK,OAAO,SAAS;YACnB,OAAO;gBAAE,GAAG,KAAK;gBACf,WAAW;oBAAE,GAAG,MAAM,SAAS;oBAC7B,oBAAoB,OAAO,kBAAkB;oBAC7C,QAAQ,OAAO,MAAM;gBACvB;YACF;QAEF,KAAK,OAAO,QAAQ;YAClB,IAAI,MAAM,SAAS,CAAC,MAAM,IAAI,MAAM;gBAClC,OAAO;YACT;YAEA,OAAO;gBAAE,GAAG,KAAK;gBACf,WAAW;oBAAE,GAAG,MAAM,SAAS;oBAC7B,WAAW;wBACT,GAAG,OAAO,WAAW,CAAC,CAAC,GAAG,MAAM,SAAS,CAAC,kBAAkB,CAAC,CAAC;wBAC9D,GAAG,OAAO,WAAW,CAAC,CAAC,GAAG,MAAM,SAAS,CAAC,kBAAkB,CAAC,CAAC;oBAChE;gBACF;YACF;QAEF,KAAK,OAAO,OAAO;QACnB,KAAK,OAAO,UAAU;YACpB,OAAO;gBAAE,GAAG,KAAK;gBACf,WAAW;oBAAE,GAAG,MAAM,SAAS;oBAC7B,QAAQ;oBACR,oBAAoB;wBAClB,GAAG;wBACH,GAAG;oBACL;oBACA,WAAW;wBACT,GAAG;wBACH,GAAG;oBACL;gBACF;YACF;QAEF,KAAK,OAAO,iBAAiB;YAC3B;gBACE,MAAM,EACJ,OAAO,EACR,GAAG;gBACJ,MAAM,EACJ,EAAE,EACH,GAAG;gBACJ,MAAM,aAAa,IAAI,uBAAuB,MAAM,SAAS,CAAC,UAAU;gBACxE,WAAW,GAAG,CAAC,IAAI;gBACnB,OAAO;oBAAE,GAAG,KAAK;oBACf,WAAW;wBAAE,GAAG,MAAM,SAAS;wBAC7B;oBACF;gBACF;YACF;QAEF,KAAK,OAAO,oBAAoB;YAC9B;gBACE,MAAM,EACJ,EAAE,EACF,GAAG,EACH,QAAQ,EACT,GAAG;gBACJ,MAAM,UAAU,MAAM,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC;gBAE/C,IAAI,CAAC,WAAW,QAAQ,QAAQ,GAAG,EAAE;oBACnC,OAAO;gBACT;gBAEA,MAAM,aAAa,IAAI,uBAAuB,MAAM,SAAS,CAAC,UAAU;gBACxE,WAAW,GAAG,CAAC,IAAI;oBAAE,GAAG,OAAO;oBAC7B;gBACF;gBACA,OAAO;oBAAE,GAAG,KAAK;oBACf,WAAW;wBAAE,GAAG,MAAM,SAAS;wBAC7B;oBACF;gBACF;YACF;QAEF,KAAK,OAAO,mBAAmB;YAC7B;gBACE,MAAM,EACJ,EAAE,EACF,GAAG,EACJ,GAAG;gBACJ,MAAM,UAAU,MAAM,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC;gBAE/C,IAAI,CAAC,WAAW,QAAQ,QAAQ,GAAG,EAAE;oBACnC,OAAO;gBACT;gBAEA,MAAM,aAAa,IAAI,uBAAuB,MAAM,SAAS,CAAC,UAAU;gBACxE,WAAW,MAAM,CAAC;gBAClB,OAAO;oBAAE,GAAG,KAAK;oBACf,WAAW;wBAAE,GAAG,MAAM,SAAS;wBAC7B;oBACF;gBACF;YACF;QAEF;YACE;gBACE,OAAO;YACT;IACJ;AACF;AAEA,SAAS,aAAa,IAAI;IACxB,IAAI,EACF,QAAQ,EACT,GAAG;IACJ,MAAM,EACJ,MAAM,EACN,cAAc,EACd,cAAc,EACf,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IACf,MAAM,yBAAyB,YAAY;IAC3C,MAAM,mBAAmB,YAAY,UAAU,OAAO,KAAK,IAAI,OAAO,EAAE,GAAG,+CAA+C;IAE1H,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ;QACF;QAEA,IAAI,CAAC,kBAAkB,0BAA0B,oBAAoB,MAAM;YACzE,IAAI,CAAC,gBAAgB,yBAAyB;gBAC5C;YACF;YAEA,IAAI,SAAS,aAAa,KAAK,uBAAuB,MAAM,EAAE;gBAC5D,2BAA2B;gBAC3B;YACF;YAEA,MAAM,gBAAgB,eAAe,GAAG,CAAC;YAEzC,IAAI,CAAC,eAAe;gBAClB;YACF;YAEA,MAAM,EACJ,aAAa,EACb,IAAI,EACL,GAAG;YAEJ,IAAI,CAAC,cAAc,OAAO,IAAI,CAAC,KAAK,OAAO,EAAE;gBAC3C;YACF;YAEA,sBAAsB;gBACpB,KAAK,MAAM,WAAW;oBAAC,cAAc,OAAO;oBAAE,KAAK,OAAO;iBAAC,CAAE;oBAC3D,IAAI,CAAC,SAAS;wBACZ;oBACF;oBAEA,MAAM,gBAAgB,uBAAuB;oBAE7C,IAAI,eAAe;wBACjB,cAAc,KAAK;wBACnB;oBACF;gBACF;YACF;QACF;IACF,GAAG;QAAC;QAAgB;QAAU;QAAgB;QAAkB;KAAuB;IACvF,OAAO;AACT;AAEA,SAAS,eAAe,SAAS,EAAE,IAAI;IACrC,IAAI,EACF,SAAS,EACT,GAAG,MACJ,GAAG;IACJ,OAAO,aAAa,QAAQ,UAAU,MAAM,GAAG,UAAU,MAAM,CAAC,CAAC,aAAa;QAC5E,OAAO,SAAS;YACd,WAAW;YACX,GAAG,IAAI;QACT;IACF,GAAG,aAAa;AAClB;AAEA,SAAS,0BAA0B,MAAM;IACvC,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC;YACpB,WAAW;gBAAE,GAAG,8BAA8B,SAAS;gBACrD,GAAI,UAAU,OAAO,KAAK,IAAI,OAAO,SAAS;YAChD;YACA,WAAW;gBAAE,GAAG,8BAA8B,SAAS;gBACrD,GAAI,UAAU,OAAO,KAAK,IAAI,OAAO,SAAS;YAChD;YACA,aAAa;gBAAE,GAAG,8BAA8B,WAAW;gBACzD,GAAI,UAAU,OAAO,KAAK,IAAI,OAAO,WAAW;YAClD;QACF,CAAC,GACD;QAAC,UAAU,OAAO,KAAK,IAAI,OAAO,SAAS;QAAE,UAAU,OAAO,KAAK,IAAI,OAAO,SAAS;QAAE,UAAU,OAAO,KAAK,IAAI,OAAO,WAAW;KAAC;AACxI;AAEA,SAAS,iCAAiC,IAAI;IAC5C,IAAI,EACF,UAAU,EACV,OAAO,EACP,WAAW,EACX,SAAS,IAAI,EACd,GAAG;IACJ,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,EACJ,CAAC,EACD,CAAC,EACF,GAAG,OAAO,WAAW,YAAY;QAChC,GAAG;QACH,GAAG;IACL,IAAI;IACJ,0BAA0B;QACxB,MAAM,WAAW,CAAC,KAAK,CAAC;QAExB,IAAI,YAAY,CAAC,YAAY;YAC3B,YAAY,OAAO,GAAG;YACtB;QACF;QAEA,IAAI,YAAY,OAAO,IAAI,CAAC,aAAa;YACvC,yEAAyE;YACzE,+CAA+C;YAC/C;QACF,EAAE,4DAA4D;QAG9D,MAAM,OAAO,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI,CAAC,OAAO;QAElE,IAAI,CAAC,QAAQ,KAAK,WAAW,KAAK,OAAO;YACvC,kEAAkE;YAClE,kCAAkC;YAClC;QACF;QAEA,MAAM,OAAO,QAAQ;QACrB,MAAM,YAAY,aAAa,MAAM;QAErC,IAAI,CAAC,GAAG;YACN,UAAU,CAAC,GAAG;QAChB;QAEA,IAAI,CAAC,GAAG;YACN,UAAU,CAAC,GAAG;QAChB,EAAE,qDAAqD;QAGvD,YAAY,OAAO,GAAG;QAEtB,IAAI,KAAK,GAAG,CAAC,UAAU,CAAC,IAAI,KAAK,KAAK,GAAG,CAAC,UAAU,CAAC,IAAI,GAAG;YAC1D,MAAM,0BAA0B,2BAA2B;YAE3D,IAAI,yBAAyB;gBAC3B,wBAAwB,QAAQ,CAAC;oBAC/B,KAAK,UAAU,CAAC;oBAChB,MAAM,UAAU,CAAC;gBACnB;YACF;QACF;IACF,GAAG;QAAC;QAAY;QAAG;QAAG;QAAa;KAAQ;AAC7C;AAEA,MAAM,yBAAyB,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE;IAAE,GAAG,kBAAkB;IAC/E,QAAQ;IACR,QAAQ;AACV;AACA,IAAI;AAEJ,CAAC,SAAU,MAAM;IACf,MAAM,CAAC,MAAM,CAAC,gBAAgB,GAAG,EAAE,GAAG;IACtC,MAAM,CAAC,MAAM,CAAC,eAAe,GAAG,EAAE,GAAG;IACrC,MAAM,CAAC,MAAM,CAAC,cAAc,GAAG,EAAE,GAAG;AACtC,CAAC,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC;AAEzB,MAAM,aAAa,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE,SAAS,WAAW,IAAI;IAC3D,IAAI,uBAAuB,uBAAuB,mBAAmB;IAErE,IAAI,EACF,EAAE,EACF,aAAa,EACb,aAAa,IAAI,EACjB,QAAQ,EACR,UAAU,cAAc,EACxB,qBAAqB,gBAAgB,EACrC,SAAS,EACT,SAAS,EACT,GAAG,OACJ,GAAG;IACJ,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,SAAS,WAAW;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG;IAC1B,MAAM,CAAC,sBAAsB,wBAAwB,GAAG;IACxD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,aAAa;IACzD,MAAM,gBAAgB,WAAW,OAAO,WAAW;IACnD,MAAM,EACJ,WAAW,EACT,QAAQ,QAAQ,EAChB,OAAO,cAAc,EACrB,SAAS,EACV,EACD,WAAW,EACT,YAAY,mBAAmB,EAChC,EACF,GAAG;IACJ,MAAM,OAAO,YAAY,OAAO,eAAe,GAAG,CAAC,YAAY;IAC/D,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;QACzB,SAAS;QACT,YAAY;IACd;IACA,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACrB,IAAI;QAEJ,OAAO,YAAY,OAAO;YACxB,IAAI;YACJ,8DAA8D;YAC9D,MAAM,CAAC,aAAa,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,KAAK,OAAO,aAAa;YAC9E,MAAM;QACR,IAAI;IACN,GAAG;QAAC;QAAU;KAAK;IACnB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,cAAc,eAAe,OAAO,OAAO,MAAM,CAAC;IACxD,MAAM,yBAAyB,YAAY,kBAAkB;IAC7D,MAAM,6BAA6B,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,oBAAoB,UAAU,IAAI;QAAC;KAAoB;IACxG,MAAM,yBAAyB,0BAA0B;IACzD,MAAM,EACJ,cAAc,EACd,0BAA0B,EAC1B,kBAAkB,EACnB,GAAG,sBAAsB,4BAA4B;QACpD,UAAU;QACV,cAAc;YAAC,UAAU,CAAC;YAAE,UAAU,CAAC;SAAC;QACxC,QAAQ,uBAAuB,SAAS;IAC1C;IACA,MAAM,aAAa,cAAc,gBAAgB;IACjD,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,iBAAiB,oBAAoB,kBAAkB,MAAM;QAAC;KAAe;IACzH,MAAM,oBAAoB;IAC1B,MAAM,wBAAwB,eAAe,YAAY,uBAAuB,SAAS,CAAC,OAAO;IACjG,iCAAiC;QAC/B,YAAY,YAAY,OAAO,eAAe,GAAG,CAAC,YAAY;QAC9D,QAAQ,kBAAkB,uBAAuB;QACjD,aAAa;QACb,SAAS,uBAAuB,SAAS,CAAC,OAAO;IACnD;IACA,MAAM,iBAAiB,QAAQ,YAAY,uBAAuB,SAAS,CAAC,OAAO,EAAE;IACrF,MAAM,oBAAoB,QAAQ,aAAa,WAAW,aAAa,GAAG;IAC1E,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;QAC3B,gBAAgB;QAChB,QAAQ;QACR;QACA,eAAe;QACf,YAAY;QACZ;QACA;QACA,cAAc;QACd,kBAAkB;QAClB;QACA,MAAM;QACN,qBAAqB,EAAE;QACvB,yBAAyB;IAC3B;IACA,MAAM,WAAW,oBAAoB,UAAU,CAAC,CAAC,wBAAwB,cAAc,OAAO,CAAC,IAAI,KAAK,OAAO,KAAK,IAAI,sBAAsB,EAAE;IAChJ,MAAM,cAAc,wBAAwB;QAC1C,SAAS,uBAAuB,WAAW,CAAC,OAAO;IACrD,IAAI,oDAAoD;IAExD,MAAM,eAAe,CAAC,wBAAwB,YAAY,OAAO,CAAC,OAAO,KAAK,OAAO,wBAAwB;IAC7G,MAAM,mBAAmB,gBAAgB,CAAC,oBAAoB,YAAY,IAAI,KAAK,OAAO,oBAAoB,iBAAiB;IAC/H,MAAM,kBAAkB,QAAQ,YAAY,OAAO,CAAC,OAAO,IAAI,YAAY,IAAI,GAAG,wEAAwE;IAC1J,iDAAiD;IAEjD,MAAM,gBAAgB,aAAa,kBAAkB,OAAO,iBAAiB,2CAA2C;IAExH,MAAM,aAAa,cAAc,eAAe,UAAU,gBAAgB,OAAO,gDAAgD;IAEjI,MAAM,sBAAsB,uBAAuB,gBAAgB,YAAY,OAAO,WAAW,aAAa;IAC9G,MAAM,0BAA0B,SAAS,sBAAsB,kBAAkB;IAEjF,MAAM,oBAAoB,eAAe,WAAW;QAClD,WAAW;YACT,GAAG,UAAU,CAAC,GAAG,cAAc,CAAC;YAChC,GAAG,UAAU,CAAC,GAAG,cAAc,CAAC;YAChC,QAAQ;YACR,QAAQ;QACV;QACA;QACA;QACA;QACA;QACA;QACA,MAAM,cAAc,OAAO,CAAC,IAAI;QAChC,iBAAiB,YAAY,IAAI;QACjC;QACA;QACA;IACF;IACA,MAAM,qBAAqB,wBAAwB,IAAI,uBAAuB,aAAa;IAC3F,MAAM,gBAAgB,iBAAiB,sBAAsB,2DAA2D;IAExH,MAAM,mBAAmB,sBAAsB,gBAAgB,oFAAoF;IAEnJ,MAAM,wBAAwB,sBAAsB,eAAe;QAAC;KAAe;IACnF,MAAM,0BAA0B,IAAI,mBAAmB;IACvD,MAAM,gBAAgB,mBAAmB,gBAAgB,kBAAkB,qBAAqB;IAChG,MAAM,aAAa,UAAU,gBAAgB,mBAAmB;QAC9D;QACA;QACA;QACA,qBAAqB;QACrB;IACF,KAAK;IACL,MAAM,SAAS,kBAAkB,YAAY;IAC7C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,iEAAiE;IACzG,sBAAsB;IAEtB,MAAM,mBAAmB,kBAAkB,oBAAoB,IAAI,mBAAmB;IACtF,MAAM,YAAY,YAAY,kBAAkB,CAAC,aAAa,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,KAAK,OAAO,aAAa,MAAM;IAC9H,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC/B,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAO;QAC5C,IAAI,EACF,QAAQ,MAAM,EACd,OAAO,EACR,GAAG;QAEJ,IAAI,UAAU,OAAO,IAAI,MAAM;YAC7B;QACF;QAEA,MAAM,aAAa,eAAe,GAAG,CAAC,UAAU,OAAO;QAEvD,IAAI,CAAC,YAAY;YACf;QACF;QAEA,MAAM,iBAAiB,MAAM,WAAW;QACxC,MAAM,iBAAiB,IAAI,OAAO;YAChC,QAAQ,UAAU,OAAO;YACzB;YACA,OAAO;YACP;YACA,gFAAgF;YAChF,6DAA6D;YAC7D,SAAS;YAET,SAAQ,EAAE;gBACR,MAAM,gBAAgB,eAAe,GAAG,CAAC;gBAEzC,IAAI,CAAC,eAAe;oBAClB;gBACF;gBAEA,MAAM,EACJ,WAAW,EACZ,GAAG,YAAY,OAAO;gBACvB,MAAM,QAAQ;oBACZ;gBACF;gBACA,eAAe,OAAO,KAAK,IAAI,YAAY;gBAC3C,qBAAqB;oBACnB,MAAM;oBACN;gBACF;YACF;YAEA,WAAU,EAAE,EAAE,UAAU,EAAE,kBAAkB,EAAE,MAAM;gBAClD,MAAM,gBAAgB,eAAe,GAAG,CAAC;gBAEzC,IAAI,CAAC,eAAe;oBAClB;gBACF;gBAEA,MAAM,EACJ,aAAa,EACd,GAAG,YAAY,OAAO;gBACvB,MAAM,QAAQ;oBACZ;oBACA;oBACA;oBACA;gBACF;gBACA,iBAAiB,OAAO,KAAK,IAAI,cAAc;gBAC/C,qBAAqB;oBACnB,MAAM;oBACN;gBACF;YACF;YAEA,SAAQ,kBAAkB;gBACxB,MAAM,KAAK,UAAU,OAAO;gBAE5B,IAAI,MAAM,MAAM;oBACd;gBACF;gBAEA,MAAM,gBAAgB,eAAe,GAAG,CAAC;gBAEzC,IAAI,CAAC,eAAe;oBAClB;gBACF;gBAEA,MAAM,EACJ,WAAW,EACZ,GAAG,YAAY,OAAO;gBACvB,MAAM,QAAQ;oBACZ;oBACA,QAAQ;wBACN;wBACA,MAAM,cAAc,IAAI;wBACxB,MAAM;oBACR;gBACF;gBACA,CAAA,GAAA,4MAAA,CAAA,0BAAuB,AAAD,EAAE;oBACtB,eAAe,OAAO,KAAK,IAAI,YAAY;oBAC3C,UAAU,OAAO,YAAY;oBAC7B,SAAS;wBACP,MAAM,OAAO,SAAS;wBACtB;wBACA,QAAQ;oBACV;oBACA,qBAAqB;wBACnB,MAAM;wBACN;oBACF;oBACA,gBAAgB,gBAAgB,OAAO;oBACvC,kBAAkB;gBACpB;YACF;YAEA,QAAO,WAAW;gBAChB,SAAS;oBACP,MAAM,OAAO,QAAQ;oBACrB;gBACF;YACF;YAEA,OAAO,cAAc,OAAO,OAAO;YACnC,UAAU,cAAc,OAAO,UAAU;QAC3C;QACA,gBAAgB,OAAO,GAAG;QAE1B,SAAS,cAAc,IAAI;YACzB,OAAO,eAAe;gBACpB,MAAM,EACJ,MAAM,EACN,UAAU,EACV,IAAI,EACJ,uBAAuB,EACxB,GAAG,cAAc,OAAO;gBACzB,IAAI,QAAQ;gBAEZ,IAAI,UAAU,yBAAyB;oBACrC,MAAM,EACJ,UAAU,EACX,GAAG,YAAY,OAAO;oBACvB,QAAQ;wBACN;wBACA,QAAQ;wBACR;wBACA,OAAO;wBACP;oBACF;oBAEA,IAAI,SAAS,OAAO,OAAO,IAAI,OAAO,eAAe,YAAY;wBAC/D,MAAM,eAAe,MAAM,QAAQ,OAAO,CAAC,WAAW;wBAEtD,IAAI,cAAc;4BAChB,OAAO,OAAO,UAAU;wBAC1B;oBACF;gBACF;gBAEA,UAAU,OAAO,GAAG;gBACpB,CAAA,GAAA,4MAAA,CAAA,0BAAuB,AAAD,EAAE;oBACtB,SAAS;wBACP;oBACF;oBACA,UAAU,OAAO,aAAa;oBAC9B,QAAQ;oBACR,gBAAgB;oBAChB,kBAAkB;oBAClB,gBAAgB,OAAO,GAAG;oBAC1B,MAAM,YAAY,SAAS,OAAO,OAAO,GAAG,cAAc;oBAE1D,IAAI,OAAO;wBACT,MAAM,UAAU,YAAY,OAAO,CAAC,UAAU;wBAC9C,WAAW,OAAO,KAAK,IAAI,QAAQ;wBACnC,qBAAqB;4BACnB,MAAM;4BACN;wBACF;oBACF;gBACF;YACF;QACF;IACF,GACA;QAAC;KAAe;IAChB,MAAM,oCAAoC,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,SAAS;QAC9D,OAAO,CAAC,OAAO;YACb,MAAM,cAAc,MAAM,WAAW;YACrC,MAAM,sBAAsB,eAAe,GAAG,CAAC;YAE/C,IACA,UAAU,OAAO,KAAK,QAAQ,sBAAsB;YACpD,CAAC,uBAAuB,kCAAkC;YAC1D,YAAY,MAAM,IAAI,YAAY,gBAAgB,EAAE;gBAClD;YACF;YAEA,MAAM,oBAAoB;gBACxB,QAAQ;YACV;YACA,MAAM,iBAAiB,QAAQ,OAAO,OAAO,OAAO,EAAE;YAEtD,IAAI,mBAAmB,MAAM;gBAC3B,YAAY,MAAM,GAAG;oBACnB,YAAY,OAAO,MAAM;gBAC3B;gBACA,UAAU,OAAO,GAAG;gBACpB,kBAAkB,OAAO;YAC3B;QACF;IACF,GAAG;QAAC;QAAgB;KAAkB;IACtC,MAAM,aAAa,qBAAqB,SAAS;IACjD,eAAe;IACf,0BAA0B;QACxB,IAAI,kBAAkB,WAAW,OAAO,YAAY,EAAE;YACpD,UAAU,OAAO,WAAW;QAC9B;IACF,GAAG;QAAC;QAAgB;KAAO;IAC3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,EACJ,UAAU,EACX,GAAG,YAAY,OAAO;QACvB,MAAM,EACJ,MAAM,EACN,cAAc,EACd,UAAU,EACV,IAAI,EACL,GAAG,cAAc,OAAO;QAEzB,IAAI,CAAC,UAAU,CAAC,gBAAgB;YAC9B;QACF;QAEA,MAAM,QAAQ;YACZ;YACA;YACA;YACA,OAAO;gBACL,GAAG,wBAAwB,CAAC;gBAC5B,GAAG,wBAAwB,CAAC;YAC9B;YACA;QACF;QACA,CAAA,GAAA,4MAAA,CAAA,0BAAuB,AAAD,EAAE;YACtB,cAAc,OAAO,KAAK,IAAI,WAAW;YACzC,qBAAqB;gBACnB,MAAM;gBACN;YACF;QACF;IACF,GACA;QAAC,wBAAwB,CAAC;QAAE,wBAAwB,CAAC;KAAC;IACtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,EACJ,MAAM,EACN,cAAc,EACd,UAAU,EACV,mBAAmB,EACnB,uBAAuB,EACxB,GAAG,cAAc,OAAO;QAEzB,IAAI,CAAC,UAAU,UAAU,OAAO,IAAI,QAAQ,CAAC,kBAAkB,CAAC,yBAAyB;YACvF;QACF;QAEA,MAAM,EACJ,UAAU,EACX,GAAG,YAAY,OAAO;QACvB,MAAM,gBAAgB,oBAAoB,GAAG,CAAC;QAC9C,MAAM,OAAO,iBAAiB,cAAc,IAAI,CAAC,OAAO,GAAG;YACzD,IAAI,cAAc,EAAE;YACpB,MAAM,cAAc,IAAI,CAAC,OAAO;YAChC,MAAM,cAAc,IAAI;YACxB,UAAU,cAAc,QAAQ;QAClC,IAAI;QACJ,MAAM,QAAQ;YACZ;YACA;YACA;YACA,OAAO;gBACL,GAAG,wBAAwB,CAAC;gBAC5B,GAAG,wBAAwB,CAAC;YAC9B;YACA;QACF;QACA,CAAA,GAAA,4MAAA,CAAA,0BAAuB,AAAD,EAAE;YACtB,QAAQ;YACR,cAAc,OAAO,KAAK,IAAI,WAAW;YACzC,qBAAqB;gBACnB,MAAM;gBACN;YACF;QACF;IACF,GACA;QAAC;KAAO;IACR,0BAA0B;QACxB,cAAc,OAAO,GAAG;YACtB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;QACA,YAAY,OAAO,GAAG;YACpB,SAAS;YACT,YAAY;QACd;IACF,GAAG;QAAC;QAAQ;QAAY;QAAY;QAAe;QAAgB;QAAc;QAAkB;QAAgB;QAAqB;QAAM;QAAqB;KAAwB;IAC3L,gBAAgB;QAAE,GAAG,iBAAiB;QACpC,OAAO;QACP,cAAc;QACd;QACA;QACA;IACF;IACA,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC5B,MAAM,UAAU;YACd;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;QACA,OAAO;IACT,GAAG;QAAC;QAAQ;QAAY;QAAgB;QAAgB;QAAY;QAAmB;QAAa;QAAgB;QAAqB;QAAgB;QAAM;QAA4B;QAAqB;QAAyB;QAAwB;QAAoB;KAAW;IAChS,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,MAAM,UAAU;YACd;YACA;YACA;YACA;YACA,mBAAmB;gBACjB,WAAW;YACb;YACA;YACA;YACA;YACA;QACF;QACA,OAAO;IACT,GAAG;QAAC;QAAgB;QAAY;QAAQ;QAAgB;QAAU;QAAwB;QAAgB;QAAM;KAA2B;IAC3I,OAAO,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,kBAAkB,QAAQ,EAAE;QACrD,OAAO;IACT,GAAG,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,gBAAgB,QAAQ,EAAE;QAC/C,OAAO;IACT,GAAG,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,cAAc,QAAQ,EAAE;QAC7C,OAAO;IACT,GAAG,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,uBAAuB,QAAQ,EAAE;QACtD,OAAO;IACT,GAAG,YAAY,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,cAAc;QAC/C,UAAU,CAAC,iBAAiB,OAAO,KAAK,IAAI,cAAc,YAAY,MAAM;IAC9E,KAAK,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,eAAe;QAAE,GAAG,aAAa;QACxD,yBAAyB;IAC3B;;IAEA,SAAS;QACP,MAAM,iCAAiC,CAAC,gBAAgB,OAAO,KAAK,IAAI,aAAa,iBAAiB,MAAM;QAC5G,MAAM,6BAA6B,OAAO,eAAe,WAAW,WAAW,OAAO,KAAK,QAAQ,eAAe;QAClH,MAAM,UAAU,iBAAiB,CAAC,kCAAkC,CAAC;QAErE,IAAI,OAAO,eAAe,UAAU;YAClC,OAAO;gBAAE,GAAG,UAAU;gBACpB;YACF;QACF;QAEA,OAAO;YACL;QACF;IACF;AACF;AAEA,MAAM,cAAc,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE;AAC/C,MAAM,cAAc;AACpB,MAAM,YAAY;AAClB,SAAS,aAAa,IAAI;IACxB,IAAI,EACF,EAAE,EACF,IAAI,EACJ,WAAW,KAAK,EAChB,UAAU,EACX,GAAG;IACJ,MAAM,MAAM,YAAY;IACxB,MAAM,EACJ,UAAU,EACV,cAAc,EACd,MAAM,EACN,cAAc,EACd,iBAAiB,EACjB,cAAc,EACd,IAAI,EACL,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IACf,MAAM,EACJ,OAAO,WAAW,EAClB,kBAAkB,WAAW,EAC7B,WAAW,CAAC,EACb,GAAG,cAAc,OAAO,aAAa,CAAC;IACvC,MAAM,aAAa,CAAC,UAAU,OAAO,KAAK,IAAI,OAAO,EAAE,MAAM;IAC7D,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,aAAa,yBAAyB;IACnE,MAAM,CAAC,MAAM,WAAW,GAAG;IAC3B,MAAM,CAAC,eAAe,oBAAoB,GAAG;IAC7C,MAAM,YAAY,sBAAsB,YAAY;IACpD,MAAM,UAAU,eAAe;IAC/B,0BAA0B;QACxB,eAAe,GAAG,CAAC,IAAI;YACrB;YACA;YACA;YACA;YACA,MAAM;QACR;QACA,OAAO;YACL,MAAM,OAAO,eAAe,GAAG,CAAC;YAEhC,IAAI,QAAQ,KAAK,GAAG,KAAK,KAAK;gBAC5B,eAAe,MAAM,CAAC;YACxB;QACF;IACF,GACA;QAAC;QAAgB;KAAG;IACpB,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC;YACxC;YACA;YACA,iBAAiB;YACjB,gBAAgB,cAAc,SAAS,cAAc,OAAO;YAC5D,wBAAwB;YACxB,oBAAoB,kBAAkB,SAAS;QACjD,CAAC,GAAG;QAAC;QAAU;QAAM;QAAU;QAAY;QAAiB,kBAAkB,SAAS;KAAC;IACxF,OAAO;QACL;QACA;QACA;QACA,YAAY;QACZ;QACA,WAAW,WAAW,YAAY;QAClC;QACA;QACA;QACA;QACA;IACF;AACF;AAEA,SAAS;IACP,OAAO,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;AACpB;AAEA,MAAM,cAAc;AACpB,MAAM,8BAA8B;IAClC,SAAS;AACX;AACA,SAAS,aAAa,IAAI;IACxB,IAAI,EACF,IAAI,EACJ,WAAW,KAAK,EAChB,EAAE,EACF,oBAAoB,EACrB,GAAG;IACJ,MAAM,MAAM,YAAY;IACxB,MAAM,EACJ,MAAM,EACN,QAAQ,EACR,IAAI,EACJ,0BAA0B,EAC3B,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IACf,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;QACtB;IACF;IACA,MAAM,0BAA0B,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACvC,MAAM,OAAO,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACpB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,EACJ,UAAU,sBAAsB,EAChC,qBAAqB,EACrB,SAAS,qBAAqB,EAC/B,GAAG;QAAE,GAAG,2BAA2B;QAClC,GAAG,oBAAoB;IACzB;IACA,MAAM,MAAM,eAAe,yBAAyB,OAAO,wBAAwB;IACnF,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,IAAI,CAAC,wBAAwB,OAAO,EAAE;YACpC,qFAAqF;YACrF,kDAAkD;YAClD,wBAAwB,OAAO,GAAG;YAClC;QACF;QAEA,IAAI,WAAW,OAAO,IAAI,MAAM;YAC9B,aAAa,WAAW,OAAO;QACjC;QAEA,WAAW,OAAO,GAAG,WAAW;YAC9B,2BAA2B,MAAM,OAAO,CAAC,IAAI,OAAO,IAAI,IAAI,OAAO,GAAG;gBAAC,IAAI,OAAO;aAAC;YACnF,WAAW,OAAO,GAAG;QACvB,GAAG;IACL,GACA;QAAC;KAAsB;IACvB,MAAM,iBAAiB,kBAAkB;QACvC,UAAU;QACV,UAAU,0BAA0B,CAAC;IACvC;IACA,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,YAAY;QAChD,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,IAAI,iBAAiB;YACnB,eAAe,SAAS,CAAC;YACzB,wBAAwB,OAAO,GAAG;QACpC;QAEA,IAAI,YAAY;YACd,eAAe,OAAO,CAAC;QACzB;IACF,GAAG;QAAC;KAAe;IACnB,MAAM,CAAC,SAAS,WAAW,GAAG,WAAW;IACzC,MAAM,UAAU,eAAe;IAC/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,kBAAkB,CAAC,QAAQ,OAAO,EAAE;YACvC;QACF;QAEA,eAAe,UAAU;QACzB,wBAAwB,OAAO,GAAG;QAClC,eAAe,OAAO,CAAC,QAAQ,OAAO;IACxC,GAAG;QAAC;QAAS;KAAe;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS;YACP,MAAM,OAAO,iBAAiB;YAC9B,SAAS;gBACP;gBACA;gBACA;gBACA,MAAM;gBACN;gBACA,MAAM;YACR;QACF;QACA,OAAO,IAAM,SAAS;gBACpB,MAAM,OAAO,mBAAmB;gBAChC;gBACA;YACF;IACF,GACA;QAAC;KAAG;IACJ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa,SAAS,OAAO,CAAC,QAAQ,EAAE;YAC1C,SAAS;gBACP,MAAM,OAAO,oBAAoB;gBACjC;gBACA;gBACA;YACF;YACA,SAAS,OAAO,CAAC,QAAQ,GAAG;QAC9B;IACF,GAAG;QAAC;QAAI;QAAK;QAAU;KAAS;IAChC,OAAO;QACL;QACA;QACA,QAAQ,CAAC,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE,MAAM;QAC9C,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,iBAAiB,IAAI;IAC5B,IAAI,EACF,SAAS,EACT,QAAQ,EACT,GAAG;IACJ,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,mBAAmB,YAAY;IAErC,IAAI,CAAC,YAAY,CAAC,kBAAkB,kBAAkB;QACpD,kBAAkB;IACpB;IAEA,0BAA0B;QACxB,IAAI,CAAC,SAAS;YACZ;QACF;QAEA,MAAM,MAAM,kBAAkB,OAAO,KAAK,IAAI,eAAe,GAAG;QAChE,MAAM,KAAK,kBAAkB,OAAO,KAAK,IAAI,eAAe,KAAK,CAAC,EAAE;QAEpE,IAAI,OAAO,QAAQ,MAAM,MAAM;YAC7B,kBAAkB;YAClB;QACF;QAEA,QAAQ,OAAO,CAAC,UAAU,IAAI,UAAU,IAAI,CAAC;YAC3C,kBAAkB;QACpB;IACF,GAAG;QAAC;QAAW;QAAgB;KAAQ;IACvC,OAAO,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ,EAAE,MAAM,UAAU,iBAAiB,CAAA,GAAA,qMAAA,CAAA,eAAY,AAAD,EAAE,gBAAgB;QACvG,KAAK;IACP,KAAK;AACP;AAEA,MAAM,mBAAmB;IACvB,GAAG;IACH,GAAG;IACH,QAAQ;IACR,QAAQ;AACV;AACA,SAAS,yBAAyB,IAAI;IACpC,IAAI,EACF,QAAQ,EACT,GAAG;IACJ,OAAO,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,gBAAgB,QAAQ,EAAE;QACnD,OAAO;IACT,GAAG,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,uBAAuB,QAAQ,EAAE;QACtD,OAAO;IACT,GAAG;AACL;AAEA,MAAM,aAAa;IACjB,UAAU;IACV,aAAa;AACf;AAEA,MAAM,oBAAoB,CAAA;IACxB,MAAM,sBAAsB,gBAAgB;IAC5C,OAAO,sBAAsB,yBAAyB;AACxD;AAEA,MAAM,oBAAoB,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,CAAC,MAAM;IACvD,IAAI,EACF,EAAE,EACF,cAAc,EACd,WAAW,EACX,QAAQ,EACR,SAAS,EACT,IAAI,EACJ,KAAK,EACL,SAAS,EACT,aAAa,iBAAiB,EAC/B,GAAG;IAEJ,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,MAAM,yBAAyB,cAAc,YAAY;QAAE,GAAG,SAAS;QACrE,QAAQ;QACR,QAAQ;IACV;IACA,MAAM,SAAS;QAAE,GAAG,UAAU;QAC5B,OAAO,KAAK,KAAK;QACjB,QAAQ,KAAK,MAAM;QACnB,KAAK,KAAK,GAAG;QACb,MAAM,KAAK,IAAI;QACf,WAAW,IAAI,SAAS,CAAC,QAAQ,CAAC;QAClC,iBAAiB,eAAe,iBAAiB,2BAA2B,gBAAgB,QAAQ;QACpG,YAAY,OAAO,eAAe,aAAa,WAAW,kBAAkB;QAC5E,GAAG,KAAK;IACV;IACA,OAAO,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,IAAI;QAC7B;QACA,OAAO;QACP;IACF,GAAG;AACL;AAEA,MAAM,kCAAkC,CAAA,UAAW,CAAA;QACjD,IAAI,EACF,MAAM,EACN,WAAW,EACZ,GAAG;QACJ,MAAM,iBAAiB,CAAC;QACxB,MAAM,EACJ,MAAM,EACN,SAAS,EACV,GAAG;QAEJ,IAAI,UAAU,QAAQ,OAAO,MAAM,EAAE;YACnC,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,OAAO,MAAM,EAAG;gBACxD,IAAI,UAAU,WAAW;oBACvB;gBACF;gBAEA,cAAc,CAAC,IAAI,GAAG,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC;gBACzD,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK;YACrC;QACF;QAEA,IAAI,UAAU,QAAQ,OAAO,WAAW,EAAE;YACxC,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,OAAO,WAAW,EAAG;gBAC7D,IAAI,UAAU,WAAW;oBACvB;gBACF;gBAEA,YAAY,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK;YAC1C;QACF;QAEA,IAAI,aAAa,QAAQ,UAAU,MAAM,EAAE;YACzC,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,MAAM;QAC5C;QAEA,IAAI,aAAa,QAAQ,UAAU,WAAW,EAAE;YAC9C,YAAY,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,WAAW;QACtD;QAEA,OAAO,SAAS;YACd,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,gBAAiB;gBACzD,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK;YACrC;YAEA,IAAI,aAAa,QAAQ,UAAU,MAAM,EAAE;gBACzC,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,MAAM;YAC/C;QACF;IACF;AAEA,MAAM,0BAA0B,CAAA;IAC9B,IAAI,EACF,WAAW,EACT,OAAO,EACP,KAAK,EACN,EACF,GAAG;IACJ,OAAO;QAAC;YACN,WAAW,IAAI,SAAS,CAAC,QAAQ,CAAC;QACpC;QAAG;YACD,WAAW,IAAI,SAAS,CAAC,QAAQ,CAAC;QACpC;KAAE;AACJ;AAEA,MAAM,oCAAoC;IACxC,UAAU;IACV,QAAQ;IACR,WAAW;IACX,aAAa,WAAW,GAAE,gCAAgC;QACxD,QAAQ;YACN,QAAQ;gBACN,SAAS;YACX;QACF;IACF;AACF;AACA,SAAS,iBAAiB,KAAK;IAC7B,IAAI,EACF,MAAM,EACN,cAAc,EACd,mBAAmB,EACnB,sBAAsB,EACvB,GAAG;IACJ,OAAO,SAAS,CAAC,IAAI;QACnB,IAAI,WAAW,MAAM;YACnB;QACF;QAEA,MAAM,kBAAkB,eAAe,GAAG,CAAC;QAE3C,IAAI,CAAC,iBAAiB;YACpB;QACF;QAEA,MAAM,aAAa,gBAAgB,IAAI,CAAC,OAAO;QAE/C,IAAI,CAAC,YAAY;YACf;QACF;QAEA,MAAM,iBAAiB,kBAAkB;QAEzC,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,MAAM,EACJ,SAAS,EACV,GAAG,UAAU,MAAM,gBAAgB,CAAC;QACrC,MAAM,kBAAkB,eAAe;QAEvC,IAAI,CAAC,iBAAiB;YACpB;QACF;QAEA,MAAM,YAAY,OAAO,WAAW,aAAa,SAAS,2BAA2B;QACrF,uBAAuB,YAAY,uBAAuB,SAAS,CAAC,OAAO;QAC3E,OAAO,UAAU;YACf,QAAQ;gBACN;gBACA,MAAM,gBAAgB,IAAI;gBAC1B,MAAM;gBACN,MAAM,uBAAuB,SAAS,CAAC,OAAO,CAAC;YACjD;YACA;YACA,aAAa;gBACX;gBACA,MAAM,uBAAuB,WAAW,CAAC,OAAO,CAAC;YACnD;YACA;YACA;YACA,WAAW;QACb;IACF;AACF;AAEA,SAAS,2BAA2B,OAAO;IACzC,MAAM,EACJ,QAAQ,EACR,MAAM,EACN,WAAW,EACX,SAAS,EACV,GAAG;QAAE,GAAG,iCAAiC;QACxC,GAAG,OAAO;IACZ;IACA,OAAO,CAAA;QACL,IAAI,EACF,MAAM,EACN,WAAW,EACX,SAAS,EACT,GAAG,MACJ,GAAG;QAEJ,IAAI,CAAC,UAAU;YACb,gDAAgD;YAChD;QACF;QAEA,MAAM,QAAQ;YACZ,GAAG,YAAY,IAAI,CAAC,IAAI,GAAG,OAAO,IAAI,CAAC,IAAI;YAC3C,GAAG,YAAY,IAAI,CAAC,GAAG,GAAG,OAAO,IAAI,CAAC,GAAG;QAC3C;QACA,MAAM,QAAQ;YACZ,QAAQ,UAAU,MAAM,KAAK,IAAI,OAAO,IAAI,CAAC,KAAK,GAAG,UAAU,MAAM,GAAG,YAAY,IAAI,CAAC,KAAK,GAAG;YACjG,QAAQ,UAAU,MAAM,KAAK,IAAI,OAAO,IAAI,CAAC,MAAM,GAAG,UAAU,MAAM,GAAG,YAAY,IAAI,CAAC,MAAM,GAAG;QACrG;QACA,MAAM,iBAAiB;YACrB,GAAG,UAAU,CAAC,GAAG,MAAM,CAAC;YACxB,GAAG,UAAU,CAAC,GAAG,MAAM,CAAC;YACxB,GAAG,KAAK;QACV;QACA,MAAM,qBAAqB,UAAU;YAAE,GAAG,IAAI;YAC5C;YACA;YACA,WAAW;gBACT,SAAS;gBACT,OAAO;YACT;QACF;QACA,MAAM,CAAC,cAAc,GAAG;QACxB,MAAM,eAAe,kBAAkB,CAAC,mBAAmB,MAAM,GAAG,EAAE;QAEtE,IAAI,KAAK,SAAS,CAAC,mBAAmB,KAAK,SAAS,CAAC,eAAe;YAClE,qFAAqF;YACrF;QACF;QAEA,MAAM,UAAU,eAAe,OAAO,KAAK,IAAI,YAAY;YACzD;YACA;YACA,GAAG,IAAI;QACT;QACA,MAAM,YAAY,YAAY,IAAI,CAAC,OAAO,CAAC,oBAAoB;YAC7D;YACA;YACA,MAAM;QACR;QACA,OAAO,IAAI,QAAQ,CAAA;YACjB,UAAU,QAAQ,GAAG;gBACnB,WAAW,OAAO,KAAK,IAAI;gBAC3B;YACF;QACF;IACF;AACF;AAEA,IAAI,MAAM;AACV,SAAS,OAAO,EAAE;IAChB,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACb,IAAI,MAAM,MAAM;YACd;QACF;QAEA;QACA,OAAO;IACT,GAAG;QAAC;KAAG;AACT;AAEA,MAAM,cAAc,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAA;IAC1C,IAAI,EACF,cAAc,KAAK,EACnB,QAAQ,EACR,eAAe,mBAAmB,EAClC,KAAK,EACL,UAAU,EACV,SAAS,EACT,iBAAiB,KAAK,EACtB,SAAS,EACT,SAAS,GAAG,EACb,GAAG;IACJ,MAAM,EACJ,cAAc,EACd,MAAM,EACN,cAAc,EACd,iBAAiB,EACjB,cAAc,EACd,mBAAmB,EACnB,WAAW,EACX,IAAI,EACJ,sBAAsB,EACtB,mBAAmB,EACnB,uBAAuB,EACvB,UAAU,EACX,GAAG;IACJ,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC7B,MAAM,MAAM,OAAO,UAAU,OAAO,KAAK,IAAI,OAAO,EAAE;IACtD,MAAM,oBAAoB,eAAe,WAAW;QAClD;QACA;QACA;QACA;QACA,kBAAkB,YAAY,IAAI;QAClC;QACA,iBAAiB,YAAY,IAAI;QACjC;QACA;QACA;QACA;IACF;IACA,MAAM,cAAc,gBAAgB;IACpC,MAAM,gBAAgB,iBAAiB;QACrC,QAAQ;QACR;QACA;QACA;IACF,IAAI,4FAA4F;IAChG,4EAA4E;IAE5E,MAAM,MAAM,cAAc,YAAY,MAAM,GAAG;IAC/C,OAAO,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,0BAA0B,MAAM,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,kBAAkB;QAC/F,WAAW;IACb,GAAG,UAAU,MAAM,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,mBAAmB;QACxD,KAAK;QACL,IAAI,OAAO,EAAE;QACb,KAAK;QACL,IAAI;QACJ,gBAAgB;QAChB,aAAa;QACb,WAAW;QACX,YAAY;QACZ,MAAM;QACN,OAAO;YACL;YACA,GAAG,KAAK;QACV;QACA,WAAW;IACb,GAAG,YAAY;AACjB;AAEA,MAAM,qBAAqB,CAAA;IACzB,IAAI,EACF,cAAc,EACd,gBAAgB,EAChB,SAAS,EACV,GAAG;IAEJ,IAAI,oBAAoB,gBAAgB;QACtC,MAAM,uBAAuB,oBAAoB;QAEjD,IAAI,CAAC,sBAAsB;YACzB,OAAO;QACT;QAEA,MAAM,UAAU,qBAAqB,CAAC,GAAG,iBAAiB,IAAI;QAC9D,MAAM,UAAU,qBAAqB,CAAC,GAAG,iBAAiB,GAAG;QAC7D,OAAO;YAAE,GAAG,SAAS;YACnB,GAAG,UAAU,CAAC,GAAG,UAAU,iBAAiB,KAAK,GAAG;YACpD,GAAG,UAAU,CAAC,GAAG,UAAU,iBAAiB,MAAM,GAAG;QACvD;IACF;IAEA,OAAO;AACT;AAEA,SAAS,cAAc,QAAQ,EAAE,WAAW,EAAE,gBAAgB;IAC1D,MAAM,QAAQ,MAAM,IAAI,CAAC,MAAM,WAAW,IAAM,IAAI,MAAM;IAC1D,IAAK,IAAI,MAAM,GAAG,MAAM,UAAU,MAAO;QACrC,IAAK,IAAI,SAAS,GAAG,SAAS,aAAa,SAAU;YACjD,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG;gBACjB,UAAU,GAAG,yBAAyB,QAAQ,aAAa,oBAAoB,mBAAmB,KAAK,UAAU,mBAAmB;gBACpI,eAAe,CAAC,MAAM,MAAM,IAAI,MAAM;YAC1C;QACJ;IACJ;IACA,OAAO;AACX;AACA,SAAS,mBAAmB,GAAG,EAAE,QAAQ,EAAE,gBAAgB;IACvD,OAAO,qBAAqB,UACtB,CAAC,WAAW,GAAG,EAAE,QAAQ,KACzB,CAAC,MAAM,CAAC,EAAE,QAAQ;AAC5B;AACA,SAAS,yBAAyB,MAAM,EAAE,WAAW,EAAE,gBAAgB;IACnE,OAAO,qBAAqB,UACtB,OAAO,YAAY,CAAC,KAAK,UACzB,OAAO,YAAY,CAAC,KAAK,cAAc,SAAS;AAC1D;AACA,SAAS,yBAAyB,MAAM,EAAE,WAAW,EAAE,gBAAgB;IACnE,OAAO,qBAAqB,UACtB,OAAO,UAAU,CAAC,KAAK,KACvB,cAAc,CAAC,OAAO,UAAU,CAAC,KAAK,EAAE,IAAI;AACtD;AACA,SAAS,mBAAmB,GAAG,EAAE,QAAQ,EAAE,gBAAgB;IACvD,OAAO,qBAAqB,UACtB,WAAW,OAAO,OAClB,OAAO,OAAO;AACxB;AACA,SAAS,0BAA0B,GAAG,EAAE,QAAQ,EAAE,WAAW;IACzD,MAAM,iBAAiB,CAAC;IACxB,MAAM,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;IACrC,gIAAgI;IAChI,IAAK,IAAI,MAAM,GAAG,MAAM,KAAK,MAAM,EAAE,MAAO;QACxC,IAAI,SAAS;QACb,KAAK,MAAM,QAAQ,IAAI,CAAC,IAAI,CAAE;YAC1B,qCAAqC;YACrC,IAAI,MAAM,OAAO,QAAQ;gBACrB,mEAAmE;gBACnE,MAAM,WAAW,GAAG,yBAAyB,QAAQ,aAAa,WAAW,mBAAmB,KAAK,UAAU,UAAU;gBACzH,4DAA4D;gBAC5D,cAAc,CAAC,SAAS,GAAG;oBACvB,WAAW,eAAe;gBAC9B;gBACA,kCAAkC;gBAClC;YACJ,OACK;gBACD,mEAAmE;gBACnE,UAAU,OAAO;YACrB;QACJ;IACJ;IACA,OAAO;AACX;AACA;;CAEC,GACD,SAAS,eAAe,KAAK;IACzB,4BAA4B;IAC5B,IAAI,MAAM,WAAW,OAAO,OAAO;QAC/B,OAAO,MAAM,MAAM,WAAW;IAClC;IACA,4BAA4B;IAC5B,OAAO,MAAM,MAAM,WAAW;AAClC;AACA,mDAAmD;AACnD;;;CAGC,GACD,SAAS,mBAAmB,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,gBAAgB;IAC/E,MAAM,UAAU,CAAC;IACjB,IAAK,MAAM,aAAa,YAAa;QACjC,MAAM,mBAAmB,EAAE;QAC3B,0DAA0D;QAC1D,IAAI,WAAW,CAAC,UAAU,EAAE,cAAc,WAAW,CAAC,UAAU,CAAC,SAAS,EAAE;YACxE;QACJ;QACA,IAAK,MAAM,aAAa,YAAa;YACjC,wJAAwJ;YACxJ,IAAI,WAAW,CAAC,UAAU,CAAC,SAAS,KAAK,WAAW,CAAC,UAAU,CAAC,SAAS,IACrE,cAAc,aACd,WAAW,CAAC,UAAU,CAAC,SAAS,KAAK,WAAW,CAAC,UAAU,EAAE,WAAW;gBACxE,iBAAiB,IAAI,CAAC;YAC1B;QACJ;QACA,IAAI,iBAAiB,MAAM,KAAK,GAAG;YAC/B,wDAAwD;YACxD,OAAO,CAAC,gBAAgB,CAAC,EAAE,CAAC,GAAG;QACnC,OACK;YACD,+HAA+H;YAC/H,KAAK,MAAM,mBAAmB,iBAAkB;gBAC5C,wEAAwE;gBACxE,MAAM,qBAAqB,WAAW,CAAC,gBAAgB,CAAC,SAAS,CAAC,EAAE;gBACpE,MAAM,mBAAmB,KAAK,GAAG,CAAC,yBAAyB,gBAAgB,KAAK,CAAC,YAAY,CAAC,EAAE,IAAI,IAAI,aAAa,oBACjH,yBAAyB,UAAU,KAAK,CAAC,YAAY,CAAC,EAAE,IAAI,IAAI,aAAa;gBACjF,MAAM,gBAAgB,KAAK,GAAG,CAAC,OAAO,gBAAgB,KAAK,CAAC,SAAS,CAAC,EAAE,IAAI,MACxE,OAAO,UAAU,KAAK,CAAC,SAAS,CAAC,EAAE,IAAI;gBAC3C,MAAM,mBAAmB,CAAC,yBAAyB,gBAAgB,KAAK,CAAC,YAAY,CAAC,EAAE,IAAI,IAAI,aAAa,oBACzG,OAAO,gBAAgB,KAAK,CAAC,SAAS,CAAC,EAAE,IAAI,GAAG,IAChD,MACA;gBACJ,MAAM,mBAAmB,CAAC,yBAAyB,UAAU,KAAK,CAAC,YAAY,CAAC,EAAE,IAAI,IAAI,aAAa,oBACnG,OAAO,UAAU,KAAK,CAAC,SAAS,CAAC,EAAE,IAAI,GAAG,IAC1C,MACA;gBACJ,gCAAgC;gBAChC,IAAI,uBAAuB,KAAK;oBAC5B,IAAI,gBAAgB,KAAK,CAAC,YAAY,CAAC,EAAE,KACrC,UAAU,KAAK,CAAC,YAAY,CAAC,EAAE,EAAE;wBACjC,OAAO,CAAC,gBAAgB,GAAG;wBAC3B;oBACJ;gBACJ;gBACA,2CAA2C;gBAC3C,IAAI,uBAAuB,KAAK;oBAC5B,IAAI,AAAC,qBAAqB,KAAK,kBAAkB,KAC5C,qBAAqB,KAAK,kBAAkB,GAAI;wBACjD,OAAO,CAAC,gBAAgB,GAAG;wBAC3B;oBACJ;gBACJ;gBACA,iFAAiF;gBACjF,IAAI,uBAAuB,KAAK;oBAC5B,IAAI,qBAAqB,iBACrB,qBAAqB,kBAAkB;wBACvC,OAAO,CAAC,gBAAgB,GAAG;wBAC3B;oBACJ;gBACJ;gBACA,8DAA8D;gBAC9D,IAAI,uBAAuB,KAAK;oBAC5B,IAAI,qBAAqB,KAAK,kBAAkB,GAAG;wBAC/C,OAAO,CAAC,gBAAgB,GAAG;wBAC3B;oBACJ;gBACJ;gBACA,2EAA2E;gBAC3E,IAAI,uBAAuB,KAAK;oBAC5B,IAAI,qBAAqB,KACrB,kBAAkB,KAClB,qBAAqB,eAAe;wBACpC,OAAO,CAAC,gBAAgB,GAAG;wBAC3B;oBACJ;gBACJ;gBACA,+DAA+D;gBAC/D,IAAI,uBAAuB,KAAK;oBAC5B,IAAI,oBAAoB,KAAK,iBAAiB,GAAG;wBAC7C,OAAO,CAAC,gBAAgB,GAAG;wBAC3B;oBACJ;gBACJ;YACJ;YACA,yFAAyF;YACzF,IAAI,CAAC,OAAO,MAAM,CAAC,SAAS,QAAQ,CAAC,cACjC,iBAAiB,MAAM,GAAG,GAAG;gBAC7B,KAAK,MAAM,mBAAmB,iBAAkB;oBAC5C,IAAI,CAAC,OAAO,IAAI,CAAC,SAAS,QAAQ,CAAC,kBAAkB;wBACjD,OAAO,CAAC,gBAAgB,GAAG;wBAC3B;oBACJ;gBACJ;YACJ;QACJ;IACJ;IACA,OAAO;AACX;AACA;;CAEC,GACD,SAAS,kBAAkB,gBAAgB,EAAE,UAAU,EAAE,iBAAiB,EAAE,cAAc,EAAE,MAAM;IAC9F,MAAM,cAAc,aAAa;IACjC,MAAM,IAAI,yBAAyB,OAAO,KAAK,CAAC,YAAY,CAAC,EAAE,IAAI,IAAI,mBAAmB,oBACtF,cACA,cAAc;IAClB,MAAM,IAAI,mBAAmB,OAAO,KAAK,CAAC,SAAS,CAAC,EAAE,IAAI,IAAI,gBAAgB,oBAC1E,cACA,cAAc;IAClB,OAAO;QAAE;QAAG;IAAE;AAClB;AAEA,MAAM,gBAAgB;IAClB,IAAI,CAAC,QAAW,kBAAkB,GAAG,CAAC,OAAO;YAAE,OAAO;YAA8B,SAAS;YAAO,SAAS;YAAa,OAAO;YAAQ,QAAQ;YAAQ,OAAO,OAAO;YAAU,UAAU,kBAAkB,GAAG,CAAC,QAAQ;gBAAE,GAAG;gBAAmT,OAAO;oBAC5gB,SAAS;oBACT,MAAM,OAAO,QAAQ;oBACrB,aAAa;oBACb,UAAU;oBACV,QAAQ;oBACR,aAAa;oBACb,eAAe;oBACf,gBAAgB;oBAChB,kBAAkB;oBAClB,iBAAiB;oBACjB,eAAe;gBACnB;YAAE;QAAG;IACb,IAAI,CAAC,QAAW,kBAAkB,GAAG,CAAC,OAAO;YAAE,OAAO;YAA8B,SAAS;YAAO,SAAS;YAAa,OAAO;YAAQ,QAAQ;YAAQ,OAAO,OAAO;YAAU,UAAU,kBAAkB,IAAI,CAAC,KAAK;gBAAE,OAAO;oBACpN,SAAS;oBACT,MAAM,OAAO,QAAQ;oBACrB,aAAa;oBACb,UAAU;oBACV,QAAQ;oBACR,aAAa;oBACb,eAAe;oBACf,gBAAgB;oBAChB,kBAAkB;oBAClB,iBAAiB;oBACjB,eAAe;gBACnB;gBAAG,UAAU;oBAAC,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAA2C,OAAO;4BAAE,eAAe;wBAAO;oBAAE;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAA8C,OAAO;4BAAE,eAAe;wBAAO;oBAAE;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAA6F,OAAO;4BAAE,eAAe;wBAAO;oBAAE;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;oBAAkC;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAAuC,OAAO;4BAAE,eAAe;4BAAQ,gBAAgB;wBAAQ;oBAAE;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;oBAA0C;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAAmB,OAAO;4BAAE,MAAM;4BAAQ,QAAQ;4BAAW,gBAAgB;wBAAQ;oBAAE;iBAAG;YAAC;QAAG;IACv0B,IAAI,CAAC,QAAW,kBAAkB,GAAG,CAAC,OAAO;YAAE,OAAO;YAA8B,SAAS;YAAO,SAAS;YAAa,OAAO;YAAQ,QAAQ;YAAQ,OAAO,OAAO;YAAU,UAAU,kBAAkB,IAAI,CAAC,KAAK;gBAAE,OAAO;oBACpN,SAAS;oBACT,MAAM;oBACN,aAAa;oBACb,UAAU;oBACV,QAAQ;oBACR,aAAa;oBACb,eAAe;oBACf,gBAAgB;oBAChB,kBAAkB;oBAClB,iBAAiB;oBACjB,eAAe;gBACnB;gBAAG,UAAU;oBAAC,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAAiE,OAAO;4BAAE,MAAM,OAAO,QAAQ;4BAAW,QAAQ;wBAAU;oBAAE;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAAqT,OAAO;4BAAE,MAAM,OAAO,QAAQ;4BAAW,QAAQ;wBAAU;oBAAE;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAAkE,OAAO;4BAAE,MAAM;4BAAW,QAAQ;wBAAU;oBAAE;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAAiE,WAAW;wBAA6C,OAAO;4BAAE,MAAM;4BAAW,QAAQ;wBAAU;oBAAE;iBAAG;YAAC;QAAG;IAC57B,IAAI,CAAC,QAAW,kBAAkB,GAAG,CAAC,OAAO;YAAE,OAAO;YAA8B,SAAS;YAAO,SAAS;YAAa,OAAO;YAAQ,QAAQ;YAAQ,OAAO,OAAO;YAAU,UAAU,kBAAkB,IAAI,CAAC,KAAK;gBAAE,OAAO;oBACpN,SAAS;oBACT,MAAM;oBACN,UAAU;oBACV,aAAa;oBACb,QAAQ;oBACR,aAAa;oBACb,eAAe;oBACf,gBAAgB;oBAChB,kBAAkB;oBAClB,iBAAiB;oBACjB,eAAe;gBACnB;gBAAG,UAAU;oBAAC,kBAAkB,IAAI,CAAC,KAAK;wBAAE,OAAO;4BACvC,MAAM,OAAO,QAAQ;4BACrB,QAAQ;4BACR,eAAe;wBACnB;wBAAG,UAAU;4BAAC,kBAAkB,GAAG,CAAC,QAAQ;gCAAE,GAAG;4BAAkQ;4BAAI,kBAAkB,GAAG,CAAC,QAAQ;gCAAE,GAAG;4BAAoM;4BAAI,kBAAkB,GAAG,CAAC,QAAQ;gCAAE,GAAG;4BAAuD;yBAAG;oBAAC;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAA+E,OAAO;4BAAE,MAAM;4BAAQ,QAAQ;4BAAW,gBAAgB;wBAAQ;oBAAE;iBAAG;YAAC;QAAG;IACj1B,IAAI,CAAC,QAAW,kBAAkB,GAAG,CAAC,OAAO;YAAE,OAAO;YAA8B,SAAS;YAAO,SAAS;YAAa,OAAO;YAAQ,QAAQ;YAAQ,OAAO,OAAO;YAAU,UAAU,kBAAkB,IAAI,CAAC,KAAK;gBAAE,OAAO;oBACpN,MAAM,OAAO,QAAQ;oBACrB,QAAQ;oBACR,aAAa;oBACb,gBAAgB;gBACpB;gBAAG,UAAU;oBAAC,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;oBAA6I;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;oBAAgR;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAAmC,OAAO;4BAAE,MAAM;wBAAO;oBAAE;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAAuC,OAAO;4BAAE,MAAM;wBAAO;oBAAE;oBAAI,kBAAkB,GAAG,CAAC,UAAU;wBAAE,IAAI;wBAAK,IAAI;wBAAM,GAAG;oBAAI;oBAAI,kBAAkB,GAAG,CAAC,UAAU;wBAAE,IAAI;wBAAM,IAAI;wBAAK,GAAG;oBAAI;oBAAI,kBAAkB,GAAG,CAAC,UAAU;wBAAE,IAAI;wBAAQ,IAAI;wBAAK,GAAG;oBAAI;oBAAI,kBAAkB,GAAG,CAAC,UAAU;wBAAE,IAAI;wBAAM,IAAI;wBAAK,GAAG;oBAAI;oBAAI,kBAAkB,GAAG,CAAC,UAAU;wBAAE,IAAI;wBAAM,IAAI;wBAAM,GAAG;oBAAI;iBAAG;YAAC;QAAG;IAC/gC,IAAI,CAAC,QAAW,kBAAkB,GAAG,CAAC,OAAO;YAAE,OAAO;YAA8B,SAAS;YAAO,SAAS;YAAa,OAAO;YAAQ,QAAQ;YAAQ,OAAO,OAAO;YAAU,UAAU,kBAAkB,IAAI,CAAC,KAAK;gBAAE,OAAO;oBACpN,MAAM;oBACN,aAAa;oBACb,UAAU;oBACV,QAAQ;oBACR,aAAa;oBACb,eAAe;oBACf,gBAAgB;oBAChB,kBAAkB;oBAClB,iBAAiB;oBACjB,eAAe;gBACnB;gBAAG,UAAU;oBAAC,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAAyB,OAAO;4BAAE,MAAM;4BAAQ,QAAQ;4BAAW,gBAAgB;wBAAQ;oBAAE;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAAiB,OAAO;4BAAE,MAAM;4BAAQ,QAAQ;4BAAW,gBAAgB;wBAAQ;oBAAE;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAA6H,OAAO;4BACza,MAAM,OAAO,QAAQ;4BACrB,QAAQ;4BACR,eAAe;4BACf,gBAAgB;wBACpB;oBAAE;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAA6L,OAAO;4BAAE,MAAM,OAAO,QAAQ;4BAAW,QAAQ;wBAAU;oBAAE;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAAmC,OAAO;4BAAE,MAAM;4BAAQ,QAAQ;wBAAU;oBAAE;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAA2C,OAAO;4BAAE,MAAM;4BAAQ,QAAQ;wBAAU;oBAAE;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAAmC,OAAO;4BAAE,MAAM;4BAAQ,QAAQ;wBAAU;oBAAE;iBAAG;YAAC;QAAG;IACjqB,IAAI,CAAC,QAAW,kBAAkB,GAAG,CAAC,OAAO;YAAE,OAAO;YAA8B,SAAS;YAAO,SAAS;YAAa,OAAO;YAAQ,QAAQ;YAAQ,OAAO,OAAO;YAAU,UAAU,kBAAkB,GAAG,CAAC,QAAQ;gBAAE,GAAG;gBAAmT,OAAO;oBAC5gB,SAAS;oBACT,MAAM,OAAO,QAAQ;oBACrB,aAAa;oBACb,UAAU;oBACV,QAAQ;oBACR,aAAa;oBACb,eAAe;oBACf,gBAAgB;oBAChB,kBAAkB;oBAClB,iBAAiB;oBACjB,eAAe;gBACnB;YAAE;QAAG;IACb,IAAI,CAAC,QAAW,kBAAkB,GAAG,CAAC,OAAO;YAAE,OAAO;YAA8B,SAAS;YAAO,SAAS;YAAa,OAAO;YAAQ,QAAQ;YAAQ,OAAO,OAAO;YAAU,UAAU,kBAAkB,IAAI,CAAC,KAAK;gBAAE,OAAO;oBACpN,SAAS;oBACT,MAAM,OAAO,QAAQ;oBACrB,aAAa;oBACb,UAAU;oBACV,QAAQ;oBACR,aAAa;oBACb,eAAe;oBACf,gBAAgB;oBAChB,kBAAkB;oBAClB,iBAAiB;oBACjB,eAAe;gBACnB;gBAAG,UAAU;oBAAC,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAA2C,OAAO;4BAAE,eAAe;wBAAO;oBAAE;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAAwD,OAAO;4BAAE,eAAe;wBAAO;oBAAE;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAA8C,OAAO;4BAAE,eAAe;wBAAO;oBAAE;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAAwD,OAAO;4BAAE,eAAe;4BAAQ,gBAAgB;wBAAQ;oBAAE;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAAoD,OAAO;4BAAE,eAAe;wBAAO;oBAAE;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAAwG,OAAO;4BAAE,eAAe;wBAAO;oBAAE;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAAiC,OAAO;4BAC33B,MAAM;4BACN,QAAQ;4BACR,aAAa;4BACb,gBAAgB;wBACpB;oBAAE;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAAuB,OAAO;4BACnE,MAAM;4BACN,QAAQ;4BACR,aAAa;4BACb,gBAAgB;wBACpB;oBAAE;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAAuB,OAAO;4BACnE,MAAM;4BACN,QAAQ;4BACR,aAAa;4BACb,gBAAgB;wBACpB;oBAAE;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAAuB,OAAO;4BACnE,MAAM;4BACN,QAAQ;4BACR,aAAa;4BACb,gBAAgB;wBACpB;oBAAE;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAAmB,OAAO;4BAC/D,MAAM;4BACN,QAAQ;4BACR,aAAa;4BACb,gBAAgB;wBACpB;oBAAE;iBAAG;YAAC;QAAG;IACzB,IAAI,CAAC,QAAW,kBAAkB,GAAG,CAAC,OAAO;YAAE,OAAO;YAA8B,SAAS;YAAO,SAAS;YAAa,OAAO;YAAQ,QAAQ;YAAQ,OAAO,OAAO;YAAU,UAAU,kBAAkB,IAAI,CAAC,KAAK;gBAAE,OAAO;oBACpN,SAAS;oBACT,MAAM;oBACN,aAAa;oBACb,UAAU;oBACV,QAAQ;oBACR,aAAa;oBACb,eAAe;oBACf,gBAAgB;oBAChB,kBAAkB;oBAClB,iBAAiB;oBACjB,eAAe;gBACnB;gBAAG,UAAU;oBAAC,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAAiE,OAAO;4BAAE,MAAM,OAAO,QAAQ;4BAAW,QAAQ;wBAAU;oBAAE;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAAqT,OAAO;4BAAE,MAAM,OAAO,QAAQ;4BAAW,QAAQ;wBAAU;oBAAE;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAAkE,OAAO;4BAAE,MAAM;4BAAW,QAAQ;wBAAU;oBAAE;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAAiE,WAAW;wBAA6C,OAAO;4BAAE,MAAM;4BAAW,QAAQ;wBAAU;oBAAE;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAAuO,OAAO;4BAAE,MAAM;4BAAW,QAAQ;wBAAO;oBAAE;iBAAG;YAAC;QAAG;IACrvC,IAAI,CAAC,QAAW,kBAAkB,GAAG,CAAC,OAAO;YAAE,OAAO;YAA8B,SAAS;YAAO,SAAS;YAAa,OAAO;YAAQ,QAAQ;YAAQ,OAAO,OAAO;YAAU,UAAU,kBAAkB,IAAI,CAAC,KAAK;gBAAE,OAAO;oBACpN,SAAS;oBACT,MAAM;oBACN,UAAU;oBACV,aAAa;oBACb,QAAQ;oBACR,aAAa;oBACb,eAAe;oBACf,gBAAgB;oBAChB,kBAAkB;oBAClB,iBAAiB;oBACjB,eAAe;gBACnB;gBAAG,UAAU;oBAAC,kBAAkB,IAAI,CAAC,KAAK;wBAAE,OAAO;4BACvC,MAAM,OAAO,QAAQ;4BACrB,QAAQ;4BACR,eAAe;wBACnB;wBAAG,UAAU;4BAAC,kBAAkB,GAAG,CAAC,QAAQ;gCAAE,GAAG;4BAAkQ;4BAAI,kBAAkB,GAAG,CAAC,QAAQ;gCAAE,GAAG;4BAAoM;4BAAI,kBAAkB,GAAG,CAAC,QAAQ;gCAAE,GAAG;4BAAuD;yBAAG;oBAAC;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAA+E,OAAO;4BAAE,MAAM;4BAAQ,QAAQ;4BAAW,gBAAgB;wBAAQ;oBAAE;iBAAG;YAAC;QAAG;IACj1B,IAAI,CAAC,QAAW,kBAAkB,GAAG,CAAC,OAAO;YAAE,OAAO;YAA8B,SAAS;YAAO,SAAS;YAAa,OAAO;YAAQ,QAAQ;YAAQ,OAAO,OAAO;YAAU,UAAU,kBAAkB,IAAI,CAAC,KAAK;gBAAE,OAAO;oBACpN,MAAM,OAAO,QAAQ;oBACrB,QAAQ;oBACR,aAAa;oBACb,eAAe;oBACf,gBAAgB;gBACpB;gBAAG,UAAU;oBAAC,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAA8I,OAAO;4BAAE,eAAe;4BAAQ,MAAM,OAAO,QAAQ;wBAAU;oBAAE;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;oBAA2N;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;oBAAkC;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;oBAA8B;oBAAI,kBAAkB,GAAG,CAAC,UAAU;wBAAE,IAAI;wBAAK,IAAI;wBAAM,GAAG;oBAAI;oBAAI,kBAAkB,GAAG,CAAC,UAAU;wBAAE,IAAI;wBAAM,IAAI;wBAAK,GAAG;oBAAI;oBAAI,kBAAkB,GAAG,CAAC,UAAU;wBAAE,IAAI;wBAAQ,IAAI;wBAAK,GAAG;oBAAI;oBAAI,kBAAkB,GAAG,CAAC,UAAU;wBAAE,IAAI;wBAAM,IAAI;wBAAK,GAAG;oBAAI;oBAAI,kBAAkB,GAAG,CAAC,UAAU;wBAAE,IAAI;wBAAM,IAAI;wBAAM,GAAG;oBAAI;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAAmC,OAAO;4BAAE,MAAM;4BAAQ,QAAQ;4BAAW,eAAe;wBAAO;oBAAE;oBAAI,kBAAkB,IAAI,CAAC,KAAK;wBAAE,OAAO;4BAAE,MAAM;4BAAQ,QAAQ;wBAAU;wBAAG,UAAU;4BAAC,kBAAkB,GAAG,CAAC,QAAQ;gCAAE,GAAG;4BAA8B;4BAAI,kBAAkB,GAAG,CAAC,QAAQ;gCAAE,GAAG;4BAA0B;4BAAI,kBAAkB,GAAG,CAAC,QAAQ;gCAAE,GAAG;4BAAsC;4BAAI,kBAAkB,GAAG,CAAC,QAAQ;gCAAE,GAAG;4BAAsC;yBAAG;oBAAC;iBAAG;YAAC;QAAG;IACv+C,IAAI,CAAC,QAAW,kBAAkB,GAAG,CAAC,OAAO;YAAE,OAAO;YAA8B,SAAS;YAAO,SAAS;YAAa,OAAO;YAAQ,QAAQ;YAAQ,OAAO,OAAO;YAAU,UAAU,kBAAkB,IAAI,CAAC,KAAK;gBAAE,OAAO;oBACpN,MAAM;oBACN,aAAa;oBACb,UAAU;oBACV,QAAQ;oBACR,aAAa;oBACb,eAAe;oBACf,gBAAgB;oBAChB,kBAAkB;oBAClB,iBAAiB;oBACjB,eAAe;gBACnB;gBAAG,UAAU;oBAAC,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAAyB,OAAO;4BAAE,MAAM;4BAAQ,QAAQ;4BAAW,gBAAgB;wBAAQ;wBAAG,IAAI;oBAAW;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAA6H,OAAO;4BAC7T,MAAM,OAAO,QAAQ;4BACrB,aAAa;4BACb,eAAe;4BACf,gBAAgB;wBACpB;oBAAE;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAA6L,OAAO;4BAAE,MAAM,OAAO,QAAQ;4BAAW,QAAQ;wBAAU;oBAAE;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAAiB,OAAO;4BAAE,MAAM;4BAAQ,QAAQ;4BAAW,gBAAgB;wBAAQ;oBAAE;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAAuJ,OAAO;4BAAE,MAAM;4BAAQ,QAAQ;wBAAU;oBAAE;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,GAAG;wBAA2G,OAAO;4BAAE,MAAM;4BAAQ,QAAQ;wBAAU;oBAAE;iBAAG;YAAC;QAAG;AACh2B;AAEA,SAAS,kBAAkB,iBAAiB;IACxC,OAAO;QACH,SAAS;QACT,qBAAqB,CAAC,OAAO,EAAE,kBAAkB,MAAM,CAAC;QACxD,UAAU;QACV,OAAO;QACP,QAAQ;QACR,UAAU;IACd;AACJ;AACA,MAAM,qBAAqB;IACvB,aAAa;IACb,SAAS;IACT,gBAAgB;IAChB,YAAY;IACZ,UAAU;AACd;AACA,MAAM,yBAAyB;IAC3B,iBAAiB;AACrB;AACA,MAAM,0BAA0B;IAC5B,iBAAiB;AACrB;AACA,MAAM,yBAAyB;IAC3B,WAAW;AACf;AACA,MAAM,iCAAiC;IACnC,OAAO;AACX;AACA,MAAM,kCAAkC;IACpC,OAAO;AACX;AACA,MAAM,4BAA4B;IAC9B,UAAU;IACV,UAAU;IACV,QAAQ;IACR,OAAO;IACP,YAAY;AAChB;AACA,MAAM,8BAA8B;IAChC,UAAU;IACV,UAAU;IACV,KAAK;IACL,MAAM;IACN,YAAY;AAChB;AACA,MAAM,4BAA4B;IAC9B,WAAW;AACf;AACA,MAAM,iCAAiC;IACnC,SAAS;AACb;AACA,MAAM,sBAAsB;IACxB,OAAO;IACP,gBAAgB;IAChB,eAAe;IACf,+BAA+B;IAC/B,yCAAyC;IACzC,uBAAuB;IACvB,4BAA4B;IAC5B,SAAS;IACT,eAAe;AACnB;AAEA,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE;AACxC,MAAM,uBAAuB,IAAM,CAAA,GAAA,qMAAA,CAAA,MAAG,AAAD,EAAE;AACvC,SAAS,mBAAmB,EAAE,QAAQ,EAAE,OAAO,EAAG;IAC9C,MAAM,EACN,KAAK;IACL,KAAK,YAAY,EACjB,sBAAsB;IACtB,SAAS,aAAa,EAAE,WAAW,6CAA6C,EAChF,mCAAmC;IACnC,mBAAmB,OAAO,EAAE,iBAAiB,CAAC,EAAE,oBAAoB,CAAC,EACrE,2BAA2B;IAC3B,aAAa,kBAAkB,kBAAkB,EAAE,cAAc,kBAAkB,EAAE,eAAe,CAAC,CAAC,EAAE,kBAAkB,sBAAsB,EAAE,mBAAmB,uBAAuB,EAAE,kBAAkB,sBAAsB,EAAE,qBAAqB,yBAAyB,EAAE,0BAA0B,8BAA8B,EAChV,WAAW;IACX,0BAA0B,8BAA8B,EAAE,2BAA2B,+BAA+B,EAAE,qBAAqB,yBAAyB,EAAE,uBAAuB,2BAA2B,EAAE,eAAe,IAAI,EAC7O,YAAY;IACZ,wBAAwB,GAAG,EAAE,iBAAiB,IAAI,EAClD,gBAAgB;IAChB,gBAAgB,IAAI,EAAE,oBAAoB,IAAI,EAAE,yBAAyB,CAAC,EAC1E,SAAS;IACT,qBAAqB,IAAI,EAAE,SAAS,EAAE,EAAE,eAAe,mBAAmB,EAAE,qBAAqB,IAAI,EACrG,WAAW;IACX,YAAY,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,aAAa,EAAE,kBAAkB,EAAE,cAAc,EAAG,GAAG,WAAW,CAAC;IAC9J,oCAAoC;IACpC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,mDAAmD;IACnD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,aAAa,WACrE,0BAA0B,UAAU,gBAAgB,qBACpD;IACN,gEAAgE;IAChE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAChE,uCAAuC;IACvC,MAAM,CAAC,+BAA+B,iCAAiC,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnF,SAAS;IACT,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACvD,oIAAoI;IACpI,MAAM,CAAC,6BAA6B,+BAA+B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/E,iEAAiE;IACjE,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACnC,gEAAgE;IAChE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,cAAc,OAAO,aAAa,WAClC,0BAA0B,UAAU,gBAAgB,qBACpD;QACN,yCAAyC;QACzC,IAAI,CAAC,gBAAgB;YACjB,mBAAmB;YACnB;QACJ;QACA,0IAA0I;QAC1I,MAAM,qCAAqC;QAC3C,+HAA+H;QAC/H,IAAI,oCAAoC;YACpC,mBAAmB;YACnB,+BAA+B;QACnC;QACA,gEAAgE;QAChE,MAAM,kBAAkB,mBAAmB,sCAAsC,iBACjF,aAAa,mBAAmB;QAChC,MAAM,sBAAsB,OAAO,IAAI,CAAC,iBAAiB,MAAM,GAAG;QAClE,wEAAwE;QACxE,IAAI,iCAAiC,qBAAqB;YACtD,0DAA0D;YAC1D,MAAM,uBAAuB;gBAAE,GAAG,eAAe;YAAC;YAClD,OAAO,oBAAoB,CAAC,8BAA8B,YAAY,CAAC;YACvE,oBAAoB,CAAC,8BAA8B,YAAY,CAAC,GAAG;gBAC/D,WAAW,8BAA8B,KAAK;YAClD;YACA,mBAAmB;YACnB,oEAAoE;YACpE,MAAM,qBAAqB;gBAAE,GAAG,eAAe;YAAC;YAChD,OAAO,kBAAkB,CAAC,8BAA8B,YAAY,CAAC;YACrE,uBAAuB;YACvB,sCAAsC;YACtC,MAAM,aAAa,WAAW;gBAC1B,mBAAmB;gBACnB,uBAAuB,CAAC;gBACxB,iCAAiC;YACrC,GAAG;YACH,oBAAoB,OAAO,GAAG;YAC9B;QACJ;QACA,6CAA6C;QAC7C,IAAI,+BAA+B;YAC/B,gEAAgE;YAChE,mBAAmB;YACnB,iCAAiC;YACjC;QACJ;QACA,gDAAgD;QAChD,uBAAuB;QACvB,+BAA+B;QAC/B,0BAA0B;QAC1B,MAAM,aAAa,WAAW;YAC1B,mBAAmB;YACnB,uBAAuB,CAAC;YACxB,+BAA+B;QACnC,GAAG;QACH,oCAAoC;QACpC,oBAAoB,OAAO,GAAG;QAC9B,2BAA2B;QAC3B,OAAO;YACH,IAAI,oBAAoB,OAAO,EAAE;gBAC7B,aAAa,oBAAoB,OAAO;YAC5C;QACJ;IACJ,GAAG;QAAC;KAAS;IACb,iEAAiE;IACjE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,mBAAmB,OAAO,aAAa,WACjC,0BAA0B,UAAU,gBAAgB,qBACpD;IACV,GAAG;QAAC;QAAgB;QAAmB;KAAiB;IACxD,wEAAwE;IACxE,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,cAAc,gBAAgB,mBAAmB,mBAAmB;QAAC;QAAgB;QAAmB;KAAiB;IACrJ,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,mBAAmB;QAC9C,IAAI,CAAC,oBAAoB;YACrB;QACJ;QACA,MAAM,mBAAmB,eAAe,SAAS,CAAC,CAAC,QAAU,MAAM,WAAW,KAAK,uBAC/E,MAAM,SAAS,KAAK;QACxB,MAAM,wBAAwB,OAAO,IAAI,CAAC,CAAC,QAAU,MAAM,WAAW,KAAK,uBACvE,MAAM,SAAS,KAAK;QACxB,8EAA8E;QAC9E,IAAI,uBAAuB;YACvB,uBAAuB;YACvB,sBAAsB;YACtB;QACJ;QACA,uGAAuG;QACvG,IAAI,uBAAuB,wBAAwB,mBAAmB;YAClE,MAAM,aAAa,WAAW,WACxB,aAAa,cAAc,GAC3B,WAAW,UACP,aAAa,aAAa,GAC1B,aAAa,KAAK;YAC5B,kBAAkB,CAAC,aAAe,qBAAqB,CAAC,IAClD;uBACK;oBACH;wBACI,aAAa;wBACb,WAAW;wBACX,OAAO;oBACX;iBACH,GACC,WAAW,MAAM,CAAC,CAAC,GAAG,QAAU,UAAU;YAChD,uBAAuB;YACvB,sBAAsB;QAC1B;IACJ,GAAG;QACC;QACA;QACA,aAAa,KAAK;QAClB,aAAa,cAAc;QAC3B,aAAa,aAAa;QAC1B;QACA;QACA;KACH;IACD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,IAAI,oBAAoB;YACpB,kBAAkB,EAAE;YACpB,uBAAuB;YACvB,sBAAsB;QAC1B;IACJ,GAAG;QAAC;KAAmB;IACvB,MAAM,qCAAqC,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAQ;QAC5D,MAAM,QAAQ,WAAW,WACnB,aAAa,cAAc,GAC3B,WAAW,UACP,aAAa,aAAa,GAC1B,aAAa,KAAK;QAC5B,sBAAsB;YAAE;YAAQ;QAAM;IAC1C,GAAG;QAAC;KAAa;IACjB,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,iBAAiB;IACrB,GAAG,EAAE;IACL,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,SAAS,cAAc,KAAK;QAC1D,IAAI,CAAC,eAAe;YAChB;QACJ;QACA,MAAM,aAAa,MAAM,IAAI,EAAE,GAAG;QAClC,qDAAqD;QACrD,IAAI,CAAC,YAAY;YACb,cAAc;gBACV,OAAO;gBACP,cAAc,cAAc,QAAQ;gBACpC,cAAc;YAClB;YACA,8DAA8D;YAC9D,iCAAiC;gBAC7B,OAAO,cAAc,SAAS;gBAC9B,cAAc,cAAc,QAAQ;gBACpC,cAAc;YAClB;YACA,iBAAiB;YACjB;QACJ;QACA,IAAI,MAAM,IAAI,EAAE;YACZ,MAAM,cAAc,cAAc;gBAC9B,OAAO;gBACP,cAAc,cAAc,QAAQ;gBACpC,cAAc;YAClB;YACA,kEAAkE;YAClE,IAAI,aAAa;gBACb,iCAAiC;oBAC7B,OAAO,cAAc,SAAS;oBAC9B,cAAc,cAAc,QAAQ;oBACpC,cAAc;gBAClB;YACJ;YACA,iBAAiB;QACrB;IACJ,GAAG;QAAC;KAAc;IAClB,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAClC,iDAAiD;IACjD,SAAS,gBAAgB,EAAE,MAAM,EAAE;QAC/B,+KAA+K;QAC/K,MAAM,eAAe,OAAO,IAAI,CAAC,OAAO,EAAE;QAC1C,cAAc;YACV;YACA,OAAO,eACD;gBACE,WAAW,OAAO,EAAE;YACxB,IACE,eAAe,CAAC,OAAO,EAAE,CAAC;YAChC,QAAQ,eAAe,OAAO,OAAO,EAAE;QAC3C;QACA,iBAAiB;YACb;YACA,UAAU,OAAO,EAAE;YACnB,WAAW,eACL,OAAO,EAAE,GACT,eAAe,CAAC,OAAO,EAAE,CAAC,CAAC,SAAS;QAC9C;QACA;IACJ,GAAG;QAAC;KAAgB;IACpB,MAAM,UAAU,WAAW,UAAU,eAAe;QAChD,sBAAsB;YAClB,UAAU;QACd;IACJ,IAAI,UAAU,iBAAiB,UAAU,cAAc,UAAU;IACjE,2HAA2H;IAC3H,SAAS,mBAAmB,IAAI;QAC5B,8CAA8C;QAC9C,MAAM,oBAAoB,cAAc;QACxC,wDAAwD;QACxD,IAAI,kBAAkB,MAAM,GAAG,GAAG;YAC9B,OAAO;QACX;QACA,gDAAgD;QAChD,OAAO,iBAAiB;IAC5B;IACA,OAAQ,kBAAkB,GAAG,CAAC,kBAAkB,QAAQ,EAAE;QAAE,OAAO;YAC3D,qBAAqB;YACrB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,iBAAiB;YACjB;YACA,WAAW;YACX;YACA;YACA;YACA;YACA;YACA;YACA,uBAAuB;YACvB;YACA;YACA;QACJ;QAAG,UAAU,kBAAkB,GAAG,CAAC,YAAY;YAAE,oBAAoB;YAAoB,aAAa;YAAiB,WAAW;YAAe,cAAc;YAAkB,SAAS;YAAS,UAAU;QAAS;IAAG;AACjO;AAEA,SAAS,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE;IACvC,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,YAAY,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,cAAc,EAAE,cAAc,EAAE,mBAAmB,EAAE,kBAAkB,EAAG,GAAG;IACpJ,IAAI,CAAC,YAAY;QACb,OAAO;IACX;IACA,MAAM,wBAAwB,uBAC1B,sBACA,wBAAwB,mBAAmB,MAAM,GAC/C;QACE,aAAa;QACb,WAAW,mBAAmB,MAAM;QACpC,OAAO,mBAAmB,KAAK;IACnC,IACE;IACN,MAAM,eAAe,wBACf;WAAI;WAAW;QAAgB;KAAsB,GACrD;WAAI;WAAW;KAAe;IACpC,OAAQ,kBAAkB,GAAG,CAAC,OAAO;QAAE,OAAO;QAAY,QAAQ;QAAa,OAAO;YAC9E,UAAU;YACV,KAAK;YACL,MAAM;YACN,eAAe;YACf,QAAQ;QACZ;QAAG,UAAU,aAAa,GAAG,CAAC,CAAC,OAAO;YAClC,MAAM,OAAO,kBAAkB,kBAAkB,YAAY,mBAAmB,gBAAgB,MAAM,WAAW;YACjH,MAAM,KAAK,kBAAkB,kBAAkB,YAAY,mBAAmB,gBAAgB,MAAM,SAAS;YAC7G,sIAAsI;YACtI,MAAM,cAAc,aAAa;YACjC,IAAI,uBAAuB,cAAc,aAAa,6BAA6B;YACnF,MAAM,gBAAgB,yBAAyB,MAAM,aAAa,MAAM,GAAG;YAC3E,0FAA0F;YAC1F,IAAI,aAAa,IAAI,CAAC,CAAC,YAAc,UAAU,WAAW,KAAK,MAAM,WAAW,IAC5E,UAAU,SAAS,KAAK,MAAM,SAAS,KACvC,CAAC,eAAe;gBAChB,uBACI,cAAc,aAAa,uCAAuC;YAC1E;YACA,+EAA+E;YAC/E,MAAM,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC;YACxB,MAAM,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC;YACxB,wEAAwE;YACxE,yEAAyE;YACzE,MAAM,IAAI,KAAK,KAAK,CAAC,IAAI;YACzB,4CAA4C;YAC5C,6EAA6E;YAC7E,uDAAuD;YACvD,MAAM,MAAM;gBACR,qCAAqC;gBACrC,0CAA0C;gBAC1C,gEAAgE;gBAChE,yCAAyC;gBACzC,GAAG,KAAK,CAAC,GAAG,AAAC,KAAK,CAAC,IAAI,oBAAoB,IAAK;gBAChD,oCAAoC;gBACpC,GAAG,KAAK,CAAC,GAAG,AAAC,KAAK,CAAC,IAAI,oBAAoB,IAAK;YACpD;YACA,OAAQ,kBAAkB,IAAI,CAAC,qMAAA,CAAA,WAAQ,EAAE;gBAAE,UAAU;oBAAC,kBAAkB,GAAG,CAAC,UAAU;wBAAE,IAAI,GAAG,GAAG,WAAW,EAAE,EAAE,CAAC,EAAE,MAAM,WAAW,CAAC,CAAC,EAAE,MAAM,SAAS,EAAE;wBAAE,aAAa;wBAAK,cAAc;wBAAO,MAAM;wBAAQ,MAAM;wBAAQ,QAAQ;wBAAQ,UAAU,kBAAkB,GAAG,CAAC,WAAW;4BAAE,QAAQ;4BAA0B,MAAM,MAAM,KAAK;wBAAC;oBAAG;oBAAI,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,IAAI,KAAK,CAAC;wBAAE,IAAI,KAAK,CAAC;wBAAE,IAAI,IAAI,CAAC;wBAAE,IAAI,IAAI,CAAC;wBAAE,SAAS,gBAC5Z,aAAa,aAAa,GAC1B,aAAa,OAAO;wBAAE,QAAQ,MAAM,KAAK;wBAAE,aAAa,gBACxD,aAAa,0BAA0B,GACrC,CAAC,cAAc,aAAa,qBAAqB,IACnD,cAAc,aAAa,qBAAqB;wBAAE,WAAW,CAAC,KAAK,EAAE,GAAG,WAAW,EAAE,EAAE,CAAC,EAAE,MAAM,WAAW,CAAC,CAAC,EAAE,MAAM,SAAS,CAAC,CAAC,CAAC;oBAAC;iBAAG;YAAC,GAAG,GAAG,GAAG,OAAO,EAAE,MAAM,WAAW,CAAC,CAAC,EAAE,MAAM,SAAS,GAAG,gBAAgB,YAAY,IAAI;QACrP;IAAG;AACX;AAEA,SAAS,UAAU,EAAE,QAAQ,EAAE,eAAe,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAG;IACvE,MAAM,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG;IACxC,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,aAAa;QACvD,IAAI;QACJ,MAAM;YACF;YACA;QACJ;QACA,UAAU,CAAC,iBACN,gBACG,CAAC,aAAa;YACV,OAAO;gBAAE;YAAU;YACnB;YACA,QAAQ;QACZ;IACZ;IACA,OAAQ,kBAAkB,GAAG,CAAC,OAAO;QAAE,KAAK;QAAY,GAAG,UAAU;QAAE,GAAG,SAAS;QAAE,UAAU;IAAS;AAC5G;AAEA,SAAS,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE;IACrC,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,aAAa;QACxC,IAAI;IACR;IACA,OAAO,kBAAkB,GAAG,CAAC,OAAO;QAAE,KAAK;QAAY,UAAU,SAAS;YAAE;QAAO;IAAG;AAC1F;AAEA,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE,SAAS,MAAM,EAAE,KAAK,EAAE,eAAe,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAG;IACnF,MAAM,EAAE,EAAE,EAAE,aAAa,EAAE,qBAAqB,EAAE,gBAAgB,EAAE,YAAY,EAAE,aAAa,EAAE,kBAAkB,EAAE,uBAAuB,EAAE,MAAM,EAAE,mBAAmB,EAAE,YAAY,EAAG,GAAG;IAC7L,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IACtD,IAAI,cAAc,QAAQ,aAAa;IACvC,IAAI,CAAC,iBACA,gBACG,CAAC,aAAa;QAAE,OAAO;YAAE;QAAU;QAAG;QAAc,QAAQ;IAAS,IAAK;QAC9E,cAAc;IAClB;IACA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,mBAAmB,CAAC,SAAS,EAAE;YAC/B,MAAM,eAAe;YACrB,MAAM,eAAe,mBAAmB,CAAC,SAAS;YAClD,MAAM,cAAc,SACf,aAAa,CAAC,CAAC,CAAC,EAAE,GAAG,QAAQ,EAAE,cAAc,GAC5C,wBAAwB;YAC9B,IAAI,CAAC,aAAa;gBACd,MAAM,IAAI,MAAM;YACpB;YACA,kBAAkB;gBACd,WAAW,CAAC,UAAU,EAAE,CAAC,qBAAqB,UAAU,CAAC,IAAI,CAAC,IAC1D,CAAC,aAAa,UAAU,CAAC,KAAK,aAAa,UAAU,CAAC,EAAE,IACxD,YAAY,IAAI,EAAE,CAAC,qBAAqB,UAAU,CAAC,IAAI,CAAC,IACxD,CAAC,OAAO,YAAY,CAAC,EAAE,IAAI,OAAO,YAAY,CAAC,EAAE,CAAC,IAClD,YAAY,GAAG,CAAC;gBACpB,YAAY,CAAC,UAAU,EAAE,sBAAsB,EAAE,CAAC;gBAClD,UAAU;gBACV,QAAQ;YACZ;QACJ,OACK;YACD,kBAAkB,CAAC;QACvB;IACJ,GAAG;QAAC;KAAoB;IACxB,MAAM,WAAW,MAAM,CAAC,UAAU;IAClC,OAAQ,kBAAkB,GAAG,CAAC,OAAO;QAAE,IAAI,GAAG,GAAG,OAAO,EAAE,UAAU,CAAC,EAAE,UAAU;QAAE,cAAc;QAAW,OAAO;YAC3G,GAAG,cAAc;YACjB,GAAI,QACE;gBAAE,GAAG,yBAAyB;gBAAE,GAAG,kBAAkB;YAAC,IACtD,CAAC,CAAC;YACR,GAAI,CAAC,SAAS,eAAe,aAAa,WACpC;gBAAE,GAAG,8BAA8B;gBAAE,GAAG,uBAAuB;YAAC,IAChE,CAAC,CAAC;YACR,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,aAAa;QACjB;QAAG,SAAS,IAAM,eAAe;gBAAE;gBAAc,OAAO;oBAAE;gBAAU;gBAAG,QAAQ;YAAS;QAAI,UAAU,kBAAkB,GAAG,CAAC,UAAU,CAAC;IAAG;AAClJ;AAEA,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,aAAa,EAAE,MAAM,EAAG;IAC9E,MAAM,EAAE,EAAE,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,cAAc,EAAE,eAAe,EAAE,WAAW,EAAE,YAAY,EAAE,eAAe,EAAE,gBAAgB,EAAE,eAAe,EAAE,uBAAuB,EAAE,wBAAwB,EAAE,kBAAkB,EAAE,oBAAoB,EAAE,YAAY,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,aAAa,EAAE,kBAAkB,EAAE,cAAc,EAAE,mBAAmB,EAAE,sBAAsB,EAAE,qBAAqB,EAAE,SAAS,EAAE,WAAW,EAAG,GAAG;IACxd,MAAM,SAAS,SAAS,KAAK,CAAC,YAAY,CAAC,EAAE;IAC7C,MAAM,MAAM,SAAS,KAAK,CAAC,SAAS,CAAC,EAAE;IACvC,OAAQ,kBAAkB,IAAI,CAAC,OAAO;QAAE,IAAI,GAAG,GAAG,QAAQ,EAAE,UAAU;QAAE,OAAO;YACvE,GAAG,kBAAkB;YACrB,GAAG,WAAW;YACd,GAAI,gBACE;gBAAE,GAAG,uBAAuB;gBAAE,GAAG,gBAAgB;YAAC,IAClD;gBAAE,GAAG,sBAAsB;gBAAE,GAAG,eAAe;YAAC,CAAC;YACvD,GAAI,SAAS;gBAAE,GAAG,sBAAsB;gBAAE,GAAG,eAAe;YAAC,IAAI,CAAC,CAAC;QACvE;QAAG,eAAe;QAAQ,YAAY;QAAK,eAAe;QAAU,SAAS,CAAC;YAC1E,IAAI,EAAE,MAAM,KAAK,GAAG;gBAChB,gBAAgB;oBACZ,OAAO,eAAe,CAAC,SAAS,IAAI;oBACpC,QAAQ;gBACZ;YACJ;QACJ;QAAG,eAAe,CAAC;YACf,EAAE,cAAc;YAChB,qBAAqB;gBACjB,OAAO,eAAe,CAAC,SAAS,IAAI;gBACpC,QAAQ;YACZ;QACJ;QAAG,aAAa,CAAC;YACb,IAAI,EAAE,MAAM,KAAK,GAAG;gBAChB;YACJ;YACA,IAAI,EAAE,MAAM,KAAK,KAAK,oBAAoB;gBACtC,uBAAuB;YAC3B;QACJ;QAAG,WAAW,CAAC;YACX,IAAI,EAAE,MAAM,KAAK,GAAG;gBAChB,IAAI,qBAAqB;oBACrB,UAAU,UAAU;wBAChB,UAAU,EAAE,QAAQ;wBACpB,SAAS,EAAE,OAAO;oBACtB;gBACJ;YACJ;QACJ;QAAG,aAAa,CAAC;YACb,8DAA8D;YAC9D,IAAI,EAAE,OAAO,KAAK,KAAK,qBAAqB;gBACxC,sBAAsB,UAAU;oBAC5B,UAAU,EAAE,QAAQ;oBACpB,SAAS,EAAE,OAAO;gBACtB;YACJ;YACA,oBAAoB;gBAChB,OAAO,eAAe,CAAC,SAAS,IAAI;gBACpC,QAAQ;YACZ;QACJ;QAAG,cAAc,IAAM,mBAAmB;gBACtC,OAAO,eAAe,CAAC,SAAS,IAAI;gBACpC,QAAQ;YACZ;QAAI,UAAU;YAAC,eAAgB,kBAAkB,IAAI,CAAC,QAAQ;gBAAE,OAAO,gBACzD;oBACE,GAAG,+BAA+B;oBAClC,GAAG,wBAAwB;gBAC/B,IACE;oBACE,GAAG,8BAA8B;oBACjC,GAAG,uBAAuB;gBAC9B;gBAAG,UAAU;oBAAC,QACV,CAAC,qBAAqB,UAChB,MACA,eAAe,QAAQ,EAAE,KAAM,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,OAAO;4BAAE,GAAG,yBAAyB;4BAAE,GAAG,kBAAkB;wBAAC;wBAAG,UAAU;oBAAO;oBAAK,WAC3J,CAAC,qBAAqB,UAChB,MACA,yBAAyB,GAAG,mBAAmB,iBAAiB,KAAM,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,OAAO;4BAC/G,GAAG,2BAA2B;4BAC9B,GAAG,oBAAoB;wBAC3B;wBAAG,UAAU;oBAAI;iBAAI;YAAC,KAAM;YAAM,iBAAiB;gBAC3D,OAAO,eAAe,CAAC,SAAS,IAAI;gBACpC,QAAQ;gBACR;YACJ,MAAO,kBAAkB,GAAG,CAAC,OAAO;gBAAE,OAAO;oBACrC,OAAO;oBACP,QAAQ;oBACR,GAAG,YAAY,CAAC,SAAS;gBAC7B;gBAAG,UAAU;YAAS;SAAI;IAAC;AAC3C;AAEA,MAAM,sBAAsB,CAAC,SAAS;IAClC,OAAO,CAAC,EAAE,SAAS,EAAE;QACjB,MAAM,eAAe,OAAO,aAAa,cACnC,SAAS,cAAc,CAAC,GAAG,QAAQ,MAAM,CAAC,IAC1C;QACN,IAAI,CAAC,cAAc;YACf,OAAO;QACX;QACA,mEAAmE;QACnE,MAAM,YAAY,aAAa,qBAAqB;QACpD,MAAM,WAAW,aAAa,aAAa,CAAC;QAC5C,IAAI,CAAC,UAAU;YACX,OAAO;QACX;QACA,MAAM,cAAc,SAAS,qBAAqB,GAAG,KAAK;QAC1D,MAAM,kBAAkB,cAAc;QACtC,0EAA0E;QAC1E,MAAM,QAAQ,sBAAsB,KAAK,CAAC;QAC1C,IAAI,CAAC,OAAO;YACR,OAAO;QACX;QACA,MAAM,GAAG,KAAK,IAAI,GAAG;QACrB,yCAAyC;QACzC,MAAM,cAAc,aAAa,aAAa,CAAC,CAAC,cAAc,EAAE,IAAI,aAAa,EAAE,IAAI,EAAE,CAAC;QAC1F,IAAI,CAAC,aAAa;YACd,OAAO;QACX;QACA,MAAM,kBAAkB,YAAY,qBAAqB;QACzD,MAAM,SAAS,gBAAgB,IAAI,GAAG,kBAAkB,UAAU,IAAI;QACtE,MAAM,SAAS,gBAAgB,GAAG,GAAG,kBAAkB,UAAU,GAAG;QACpE,wFAAwF;QACxF,MAAM,OAAO,CAAC;QACd,MAAM,OAAO,UAAU,KAAK,GAAG;QAC/B,MAAM,OAAO,CAAC;QACd,MAAM,OAAO,UAAU,MAAM,GAAG;QAChC,MAAM,WAAW,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,UAAU,CAAC,EAAE,OAAO;QACvD,MAAM,WAAW,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,UAAU,CAAC,EAAE,OAAO;QACvD,OAAO;YACH,GAAG,SAAS;YACZ,GAAG;YACH,GAAG;QACP;IACJ;AACJ;AAEA,SAAS;IACL,MAAM,EAAE,iBAAiB,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,eAAe,EAAE,aAAa,EAAE,EAAE,EAAG,GAAG;IACzG,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,OAAO,EAAE;IAC/D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,OAAO,EAAE;IACjE,oEAAoE;IACpE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,SAAS,OAAO,EAAE;YAClB,MAAM,iBAAiB,IAAI,eAAe;gBACtC,cAAc,SAAS,OAAO,EAAE;gBAChC,eAAe,SAAS,OAAO,EAAE;YACrC;YACA,eAAe,OAAO,CAAC,SAAS,OAAO;YACvC,OAAO;gBACH,eAAe,UAAU;YAC7B;QACJ;IACJ,GAAG;QAAC,SAAS,OAAO;KAAC;IACrB,OAAQ,kBAAkB,IAAI,CAAC,kBAAkB,QAAQ,EAAE;QAAE,UAAU;YAAC,kBAAkB,IAAI,CAAC,OAAO;gBAAE,IAAI,GAAG,GAAG,MAAM,CAAC;gBAAE,KAAK;gBAAU,OAAO;oBAAE,GAAG,kBAAkB,kBAAkB;oBAAE,GAAG,UAAU;gBAAC;gBAAG,UAAU;oBAAC,MAAM,GAAG,CAAC,CAAC,MAAQ,IAAI,GAAG,CAAC,CAAC;4BAChO,MAAM,QAAQ,eAAe,CAAC,OAAO,QAAQ,CAAC;4BAC9C,OAAQ,kBAAkB,GAAG,CAAC,WAAW;gCAAE,UAAU,OAAO,QAAQ;gCAAE,UAAU,CAAC,EAAE,MAAM,EAAE,GAAM,kBAAkB,GAAG,CAAC,QAAQ;wCAAE,QAAQ;wCAAQ,GAAG,MAAM;wCAAE,UAAU,QAAS,kBAAkB,GAAG,CAAC,WAAW;4CAAE,cAAc;4CAAO,UAAU,OAAO,QAAQ;4CAAE,WAAW,MAAM,SAAS;4CAAE,UAAU,kBAAkB,GAAG,CAAC,OAAO;gDAAE,GAAG,KAAK;gDAAE,UAAU,OAAO,QAAQ;4CAAC;wCAAG,KAAM;oCAAK;4BAAI,GAAG,OAAO,QAAQ;wBACnZ;oBAAK,kBAAkB,GAAG,CAAC,QAAQ;wBAAE,YAAY;wBAAY,aAAa;oBAAY;iBAAG;YAAC;YAAI,kBAAkB,GAAG,CAAC,aAAa;gBAAE,eAAe;gBAAM,WAAW;oBACnK;uBACI,oBACE,EAAE,GACF;wBAAC,oBAAoB,IAAI,eAAe,YAAY;qBAAI;iBACjE;gBAAE,UAAU,gBAAiB,kBAAkB,GAAG,CAAC,OAAO;oBAAE,OAAO;oBAAM,UAAU,cAAc,QAAQ;oBAAE,WAAW,cAAc,SAAS;gBAAC,KAAM;YAAK;SAAG;IAAC;AAC9K;AAEA,SAAS,WAAW,EAAE,OAAO,EAAE;IAC3B,MAAM,EAAE,SAAS,EAAE,GAAG,0BAA0B;QAAE,WAAW;IAAM;IACnE,IAAI,WAAW;QACX,OAAO,kBAAkB,GAAG,CAAC,OAAO,CAAC;IACzC;IACA,OAAQ,kBAAkB,GAAG,CAAC,oBAAoB;QAAE,SAAS;QAAS,UAAU,kBAAkB,GAAG,CAAC,OAAO,CAAC;IAAG;AACrH;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE;IAC7B,OAAQ,kBAAkB,GAAG,CAAC,WAAW;QAAE,cAAc;QAAM,UAAU;QAAW,WAAW;QAAW,UAAU,kBAAkB,GAAG,CAAC,OAAO;YAAE,cAAc;YAAM,WAAW;YAAW,UAAU;QAAU;IAAG;AACxN", "ignoreList": [0], "debugId": null}}]}