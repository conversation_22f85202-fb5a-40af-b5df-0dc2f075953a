{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/components/auth/LoginButton.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { LogIn, LogOut, User } from 'lucide-react'\n\nexport default function LoginButton() {\n  const { user, signInWithGoogle, signOut, loading } = useAuth()\n  const [isLoading, setIsLoading] = useState(false)\n\n  const handleSignIn = async () => {\n    setIsLoading(true)\n    try {\n      await signInWithGoogle()\n    } catch (error) {\n      console.error('Sign in error:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleSignOut = async () => {\n    setIsLoading(true)\n    try {\n      await signOut()\n    } catch (error) {\n      console.error('Sign out error:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"animate-pulse bg-gray-200 rounded-md h-10 w-24\"></div>\n    )\n  }\n\n  if (user) {\n    return null // Account button will be handled by AccountButton component\n  }\n\n  return (\n    <button\n      onClick={handleSignIn}\n      disabled={isLoading}\n      className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n    >\n      {isLoading ? (\n        <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n      ) : (\n        <>\n          <LogIn className=\"w-4 h-4 mr-2\" />\n          Sign in with Google\n        </>\n      )}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,eAAe;QACnB,aAAa;QACb,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;QAClC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB;QACpB,aAAa;QACb,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;QACnC,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;;;;;IAEnB;IAEA,IAAI,MAAM;QACR,OAAO,KAAK,4DAA4D;;IAC1E;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,UAAU;QACV,WAAU;kBAET,0BACC,8OAAC;YAAI,WAAU;;;;;iCAEf;;8BACE,8OAAC,wMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;gBAAiB;;;;;;;;AAM5C", "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/components/auth/AccountButton.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useRouter } from 'next/navigation'\nimport { User, Settings, LogOut, ChevronDown } from 'lucide-react'\n\nexport default function AccountButton() {\n  const { user, signOut, loading } = useAuth()\n  const [isDropdownOpen, setIsDropdownOpen] = useState(false)\n  const [isLoading, setIsLoading] = useState(false)\n  const dropdownRef = useRef<HTMLDivElement>(null)\n  const router = useRouter()\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    function handleClickOutside(event: MouseEvent) {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsDropdownOpen(false)\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside)\n    }\n  }, [])\n\n  const handleSignOut = async () => {\n    setIsLoading(true)\n    try {\n      await signOut()\n      setIsDropdownOpen(false)\n      router.push('/')\n    } catch (error) {\n      console.error('Sign out error:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleProfileClick = () => {\n    setIsDropdownOpen(false)\n    router.push('/profile')\n  }\n\n  const getInitials = (name: string) => {\n    return name\n      .split(' ')\n      .map(word => word.charAt(0))\n      .join('')\n      .toUpperCase()\n      .slice(0, 2)\n  }\n\n  if (loading || !user) {\n    return null\n  }\n\n  return (\n    <div className=\"relative\" ref={dropdownRef}>\n      <button\n        onClick={() => setIsDropdownOpen(!isDropdownOpen)}\n        className=\"flex items-center space-x-2 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors\"\n      >\n        {user.avatar_url ? (\n          <img\n            src={user.avatar_url}\n            alt={user.display_name}\n            className=\"w-8 h-8 rounded-full object-cover\"\n          />\n        ) : (\n          <div className=\"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium\">\n            {getInitials(user.display_name)}\n          </div>\n        )}\n        <span className=\"hidden sm:block\">{user.display_name}</span>\n        <ChevronDown className={`w-4 h-4 transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`} />\n      </button>\n\n      {isDropdownOpen && (\n        <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50\">\n          <div className=\"py-1\">\n            <div className=\"px-4 py-2 text-sm text-gray-500 border-b\">\n              <div className=\"font-medium text-gray-900\">{user.display_name}</div>\n              <div className=\"text-xs\">{user.email}</div>\n            </div>\n            \n            <button\n              onClick={handleProfileClick}\n              className=\"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\"\n            >\n              <User className=\"w-4 h-4 mr-3\" />\n              Profile\n            </button>\n            \n            <button\n              onClick={handleProfileClick}\n              className=\"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\"\n            >\n              <Settings className=\"w-4 h-4 mr-3\" />\n              Account Settings\n            </button>\n            \n            <div className=\"border-t\">\n              <button\n                onClick={handleSignOut}\n                disabled={isLoading}\n                className=\"flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors disabled:opacity-50\"\n              >\n                {isLoading ? (\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-3\"></div>\n                ) : (\n                  <LogOut className=\"w-4 h-4 mr-3\" />\n                )}\n                {isLoading ? 'Signing out...' : 'Sign Out'}\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACzC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,mBAAmB,KAAiB;YAC3C,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E,kBAAkB;YACpB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,aAAa;QACb,IAAI;YACF,MAAM;YACN,kBAAkB;YAClB,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;QACnC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,qBAAqB;QACzB,kBAAkB;QAClB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,IACxB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,IAAI,WAAW,CAAC,MAAM;QACpB,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;QAAW,KAAK;;0BAC7B,8OAAC;gBACC,SAAS,IAAM,kBAAkB,CAAC;gBAClC,WAAU;;oBAET,KAAK,UAAU,iBACd,8OAAC;wBACC,KAAK,KAAK,UAAU;wBACpB,KAAK,KAAK,YAAY;wBACtB,WAAU;;;;;6CAGZ,8OAAC;wBAAI,WAAU;kCACZ,YAAY,KAAK,YAAY;;;;;;kCAGlC,8OAAC;wBAAK,WAAU;kCAAmB,KAAK,YAAY;;;;;;kCACpD,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAW,CAAC,6BAA6B,EAAE,iBAAiB,eAAe,IAAI;;;;;;;;;;;;YAG7F,gCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAA6B,KAAK,YAAY;;;;;;8CAC7D,8OAAC;oCAAI,WAAU;8CAAW,KAAK,KAAK;;;;;;;;;;;;sCAGtC,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAInC,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAIvC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;;oCAET,0BACC,8OAAC;wCAAI,WAAU;;;;;6DAEf,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAEnB,YAAY,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhD", "debugId": null}}, {"offset": {"line": 337, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/components/layout/Navigation.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { useAuth } from '@/contexts/AuthContext'\nimport LoginButton from '@/components/auth/LoginButton'\nimport AccountButton from '@/components/auth/AccountButton'\nimport { Crown, Home, User, Gamepad2 } from 'lucide-react'\n\nexport default function Navigation() {\n  const { user } = useAuth()\n\n  return (\n    <nav className=\"bg-white shadow-lg\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <Crown className=\"h-8 w-8 text-blue-600\" />\n              <span className=\"text-xl font-bold text-gray-900\">ChessHub</span>\n            </Link>\n          </div>\n\n          <div className=\"flex items-center space-x-8\">\n            <Link\n              href=\"/\"\n              className=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium\"\n            >\n              <Home className=\"w-4 h-4\" />\n              <span>Home</span>\n            </Link>\n\n            <Link\n              href=\"/demo\"\n              className=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium\"\n            >\n              <Gamepad2 className=\"w-4 h-4\" />\n              <span>Demo</span>\n            </Link>\n\n            {user && (\n              <>\n                <Link\n                  href=\"/play\"\n                  className=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  <Gamepad2 className=\"w-4 h-4\" />\n                  <span>Play</span>\n                </Link>\n                <Link\n                  href=\"/profile\"\n                  className=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  <User className=\"w-4 h-4\" />\n                  <span>Profile</span>\n                </Link>\n              </>\n            )}\n\n            <LoginButton />\n            <AccountButton />\n          </div>\n        </div>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEvB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;;;;;;kCAItD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,mMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;kDAAK;;;;;;;;;;;;0CAGR,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;kDAAK;;;;;;;;;;;;4BAGP,sBACC;;kDACE,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAK;;;;;;;;;;;;;;0CAKZ,8OAAC,yIAAA,CAAA,UAAW;;;;;0CACZ,8OAAC,2IAAA,CAAA,UAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM1B", "debugId": null}}, {"offset": {"line": 539, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/app/debug/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { supabase } from '@/lib/supabase'\nimport Navigation from '@/components/layout/Navigation'\n\nexport default function DebugPage() {\n  const { user, supabaseUser } = useAuth()\n  const [diagnostics, setDiagnostics] = useState<any>({})\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    runDiagnostics()\n  }, [user])\n\n  const runDiagnostics = async () => {\n    const results: any = {\n      timestamp: new Date().toISOString(),\n      supabaseConfigured: !!supabase,\n      userFromContext: !!user,\n      supabaseUserFromContext: !!supabaseUser,\n      userDetails: user,\n      supabaseUserDetails: supabaseUser\n    }\n\n    if (supabase) {\n      try {\n        // Test connection\n        const { data: session } = await supabase.auth.getSession()\n        results.sessionExists = !!session.session\n        results.sessionUser = session.session?.user\n\n        // Check if users table exists and user is in it\n        if (user) {\n          try {\n            const { data: userData, error: userError } = await supabase\n              .from('users')\n              .select('*')\n              .eq('id', user.id)\n              .single()\n            \n            results.userInDatabase = !userError && !!userData\n            results.userDatabaseData = userData\n            results.userDatabaseError = userError\n          } catch (error) {\n            results.userInDatabase = false\n            results.userDatabaseError = error\n          }\n\n          // Check if games table exists\n          try {\n            const { data: gamesTest, error: gamesError } = await supabase\n              .from('games')\n              .select('count', { count: 'exact', head: true })\n            \n            results.gamesTableExists = !gamesError\n            results.gamesTableError = gamesError\n          } catch (error) {\n            results.gamesTableExists = false\n            results.gamesTableError = error\n          }\n\n          // Test game creation (dry run)\n          if (results.gamesTableExists && results.userInDatabase) {\n            try {\n              const { data: gameData, error: gameError } = await supabase\n                .from('games')\n                .insert({\n                  white_player_id: user.id,\n                  status: 'waiting'\n                })\n                .select()\n                .single()\n              \n              results.gameCreationTest = !gameError\n              results.gameCreationError = gameError\n              \n              // If successful, delete the test game\n              if (gameData && !gameError) {\n                await supabase.from('games').delete().eq('id', gameData.id)\n                results.testGameDeleted = true\n              }\n            } catch (error) {\n              results.gameCreationTest = false\n              results.gameCreationError = error\n            }\n          }\n        }\n      } catch (error) {\n        results.supabaseConnectionError = error\n      }\n    }\n\n    setDiagnostics(results)\n    setLoading(false)\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Navigation />\n        <div className=\"max-w-4xl mx-auto py-8 px-4\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"></div>\n            <p className=\"mt-2 text-gray-600\">Running diagnostics...</p>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navigation />\n      \n      <div className=\"max-w-4xl mx-auto py-8 px-4\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-extrabold text-gray-900 mb-2\">Debug Information</h1>\n          <p className=\"text-lg text-gray-600\">Diagnostic information for troubleshooting</p>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-md p-6\">\n          <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">System Status</h2>\n          \n          <div className=\"space-y-4\">\n            <div className=\"grid grid-cols-2 gap-4\">\n              <div className={`p-3 rounded ${diagnostics.supabaseConfigured ? 'bg-green-100' : 'bg-red-100'}`}>\n                <span className=\"font-medium\">Supabase Configured:</span> {diagnostics.supabaseConfigured ? '✅' : '❌'}\n              </div>\n              <div className={`p-3 rounded ${diagnostics.userFromContext ? 'bg-green-100' : 'bg-red-100'}`}>\n                <span className=\"font-medium\">User Loaded:</span> {diagnostics.userFromContext ? '✅' : '❌'}\n              </div>\n              <div className={`p-3 rounded ${diagnostics.sessionExists ? 'bg-green-100' : 'bg-red-100'}`}>\n                <span className=\"font-medium\">Session Active:</span> {diagnostics.sessionExists ? '✅' : '❌'}\n              </div>\n              <div className={`p-3 rounded ${diagnostics.userInDatabase ? 'bg-green-100' : 'bg-red-100'}`}>\n                <span className=\"font-medium\">User in Database:</span> {diagnostics.userInDatabase ? '✅' : '❌'}\n              </div>\n              <div className={`p-3 rounded ${diagnostics.gamesTableExists ? 'bg-green-100' : 'bg-red-100'}`}>\n                <span className=\"font-medium\">Games Table Exists:</span> {diagnostics.gamesTableExists ? '✅' : '❌'}\n              </div>\n              <div className={`p-3 rounded ${diagnostics.gameCreationTest ? 'bg-green-100' : 'bg-red-100'}`}>\n                <span className=\"font-medium\">Game Creation Test:</span> {diagnostics.gameCreationTest ? '✅' : '❌'}\n              </div>\n            </div>\n          </div>\n\n          <div className=\"mt-8\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Detailed Information</h3>\n            <pre className=\"bg-gray-100 p-4 rounded text-sm overflow-auto max-h-96\">\n              {JSON.stringify(diagnostics, null, 2)}\n            </pre>\n          </div>\n\n          <div className=\"mt-6\">\n            <button\n              onClick={runDiagnostics}\n              className=\"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\"\n            >\n              Refresh Diagnostics\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO,CAAC;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAK;IAET,MAAM,iBAAiB;QACrB,MAAM,UAAe;YACnB,WAAW,IAAI,OAAO,WAAW;YACjC,oBAAoB,CAAC,CAAC,sHAAA,CAAA,WAAQ;YAC9B,iBAAiB,CAAC,CAAC;YACnB,yBAAyB,CAAC,CAAC;YAC3B,aAAa;YACb,qBAAqB;QACvB;QAEA,IAAI,sHAAA,CAAA,WAAQ,EAAE;YACZ,IAAI;gBACF,kBAAkB;gBAClB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;gBACxD,QAAQ,aAAa,GAAG,CAAC,CAAC,QAAQ,OAAO;gBACzC,QAAQ,WAAW,GAAG,QAAQ,OAAO,EAAE;gBAEvC,gDAAgD;gBAChD,IAAI,MAAM;oBACR,IAAI;wBACF,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACxD,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;wBAET,QAAQ,cAAc,GAAG,CAAC,aAAa,CAAC,CAAC;wBACzC,QAAQ,gBAAgB,GAAG;wBAC3B,QAAQ,iBAAiB,GAAG;oBAC9B,EAAE,OAAO,OAAO;wBACd,QAAQ,cAAc,GAAG;wBACzB,QAAQ,iBAAiB,GAAG;oBAC9B;oBAEA,8BAA8B;oBAC9B,IAAI;wBACF,MAAM,EAAE,MAAM,SAAS,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC1D,IAAI,CAAC,SACL,MAAM,CAAC,SAAS;4BAAE,OAAO;4BAAS,MAAM;wBAAK;wBAEhD,QAAQ,gBAAgB,GAAG,CAAC;wBAC5B,QAAQ,eAAe,GAAG;oBAC5B,EAAE,OAAO,OAAO;wBACd,QAAQ,gBAAgB,GAAG;wBAC3B,QAAQ,eAAe,GAAG;oBAC5B;oBAEA,+BAA+B;oBAC/B,IAAI,QAAQ,gBAAgB,IAAI,QAAQ,cAAc,EAAE;wBACtD,IAAI;4BACF,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACxD,IAAI,CAAC,SACL,MAAM,CAAC;gCACN,iBAAiB,KAAK,EAAE;gCACxB,QAAQ;4BACV,GACC,MAAM,GACN,MAAM;4BAET,QAAQ,gBAAgB,GAAG,CAAC;4BAC5B,QAAQ,iBAAiB,GAAG;4BAE5B,sCAAsC;4BACtC,IAAI,YAAY,CAAC,WAAW;gCAC1B,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,SAAS,MAAM,GAAG,EAAE,CAAC,MAAM,SAAS,EAAE;gCAC1D,QAAQ,eAAe,GAAG;4BAC5B;wBACF,EAAE,OAAO,OAAO;4BACd,QAAQ,gBAAgB,GAAG;4BAC3B,QAAQ,iBAAiB,GAAG;wBAC9B;oBACF;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,uBAAuB,GAAG;YACpC;QACF;QAEA,eAAe;QACf,WAAW;IACb;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0IAAA,CAAA,UAAU;;;;;8BACX,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;;;;;;;;;;;;IAK5C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,0IAAA,CAAA,UAAU;;;;;0BAEX,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA6C;;;;;;0CAC3D,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAGvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAEzD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAW,CAAC,YAAY,EAAE,YAAY,kBAAkB,GAAG,iBAAiB,cAAc;;8DAC7F,8OAAC;oDAAK,WAAU;8DAAc;;;;;;gDAA2B;gDAAE,YAAY,kBAAkB,GAAG,MAAM;;;;;;;sDAEpG,8OAAC;4CAAI,WAAW,CAAC,YAAY,EAAE,YAAY,eAAe,GAAG,iBAAiB,cAAc;;8DAC1F,8OAAC;oDAAK,WAAU;8DAAc;;;;;;gDAAmB;gDAAE,YAAY,eAAe,GAAG,MAAM;;;;;;;sDAEzF,8OAAC;4CAAI,WAAW,CAAC,YAAY,EAAE,YAAY,aAAa,GAAG,iBAAiB,cAAc;;8DACxF,8OAAC;oDAAK,WAAU;8DAAc;;;;;;gDAAsB;gDAAE,YAAY,aAAa,GAAG,MAAM;;;;;;;sDAE1F,8OAAC;4CAAI,WAAW,CAAC,YAAY,EAAE,YAAY,cAAc,GAAG,iBAAiB,cAAc;;8DACzF,8OAAC;oDAAK,WAAU;8DAAc;;;;;;gDAAwB;gDAAE,YAAY,cAAc,GAAG,MAAM;;;;;;;sDAE7F,8OAAC;4CAAI,WAAW,CAAC,YAAY,EAAE,YAAY,gBAAgB,GAAG,iBAAiB,cAAc;;8DAC3F,8OAAC;oDAAK,WAAU;8DAAc;;;;;;gDAA0B;gDAAE,YAAY,gBAAgB,GAAG,MAAM;;;;;;;sDAEjG,8OAAC;4CAAI,WAAW,CAAC,YAAY,EAAE,YAAY,gBAAgB,GAAG,iBAAiB,cAAc;;8DAC3F,8OAAC;oDAAK,WAAU;8DAAc;;;;;;gDAA0B;gDAAE,YAAY,gBAAgB,GAAG,MAAM;;;;;;;;;;;;;;;;;;0CAKrG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCAAI,WAAU;kDACZ,KAAK,SAAS,CAAC,aAAa,MAAM;;;;;;;;;;;;0CAIvC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}]}