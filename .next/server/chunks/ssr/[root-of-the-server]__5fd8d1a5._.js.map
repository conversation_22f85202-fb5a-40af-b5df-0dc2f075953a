{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/components/auth/LoginButton.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { LogIn, LogOut, User } from 'lucide-react'\n\nexport default function LoginButton() {\n  const { user, signInWithGoogle, signOut, loading } = useAuth()\n  const [isLoading, setIsLoading] = useState(false)\n\n  const handleSignIn = async () => {\n    setIsLoading(true)\n    try {\n      await signInWithGoogle()\n    } catch (error) {\n      console.error('Sign in error:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleSignOut = async () => {\n    setIsLoading(true)\n    try {\n      await signOut()\n    } catch (error) {\n      console.error('Sign out error:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"animate-pulse bg-gray-200 rounded-md h-10 w-24\"></div>\n    )\n  }\n\n  if (user) {\n    return (\n      <div className=\"flex items-center space-x-4\">\n        <div className=\"flex items-center space-x-2\">\n          {user.avatar_url ? (\n            <img\n              src={user.avatar_url}\n              alt={user.display_name}\n              className=\"w-8 h-8 rounded-full\"\n            />\n          ) : (\n            <div className=\"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center\">\n              <User className=\"w-4 h-4 text-gray-600\" />\n            </div>\n          )}\n          <span className=\"text-sm font-medium text-gray-700\">\n            {user.display_name}\n          </span>\n        </div>\n        <button\n          onClick={handleSignOut}\n          disabled={isLoading}\n          className=\"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n        >\n          {isLoading ? (\n            <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n          ) : (\n            <>\n              <LogOut className=\"w-4 h-4 mr-1\" />\n              Sign Out\n            </>\n          )}\n        </button>\n      </div>\n    )\n  }\n\n  return (\n    <button\n      onClick={handleSignIn}\n      disabled={isLoading}\n      className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n    >\n      {isLoading ? (\n        <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n      ) : (\n        <>\n          <LogIn className=\"w-4 h-4 mr-2\" />\n          Sign in with Google\n        </>\n      )}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,eAAe;QACnB,aAAa;QACb,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;QAClC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB;QACpB,aAAa;QACb,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;QACnC,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;;;;;IAEnB;IAEA,IAAI,MAAM;QACR,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;wBACZ,KAAK,UAAU,iBACd,8OAAC;4BACC,KAAK,KAAK,UAAU;4BACpB,KAAK,KAAK,YAAY;4BACtB,WAAU;;;;;iDAGZ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAGpB,8OAAC;4BAAK,WAAU;sCACb,KAAK,YAAY;;;;;;;;;;;;8BAGtB,8OAAC;oBACC,SAAS;oBACT,UAAU;oBACV,WAAU;8BAET,0BACC,8OAAC;wBAAI,WAAU;;;;;6CAEf;;0CACE,8OAAC,0MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;IAO/C;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,UAAU;QACV,WAAU;kBAET,0BACC,8OAAC;YAAI,WAAU;;;;;iCAEf;;8BACE,8OAAC,wMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;gBAAiB;;;;;;;;AAM5C", "debugId": null}}, {"offset": {"line": 188, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/components/layout/Navigation.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { useAuth } from '@/contexts/AuthContext'\nimport LoginButton from '@/components/auth/LoginButton'\nimport { Crown, Home, User, Gamepad2 } from 'lucide-react'\n\nexport default function Navigation() {\n  const { user } = useAuth()\n\n  return (\n    <nav className=\"bg-white shadow-lg\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <Crown className=\"h-8 w-8 text-blue-600\" />\n              <span className=\"text-xl font-bold text-gray-900\">ChessHub</span>\n            </Link>\n          </div>\n\n          <div className=\"flex items-center space-x-8\">\n            <Link\n              href=\"/\"\n              className=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium\"\n            >\n              <Home className=\"w-4 h-4\" />\n              <span>Home</span>\n            </Link>\n\n            <Link\n              href=\"/demo\"\n              className=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium\"\n            >\n              <Gamepad2 className=\"w-4 h-4\" />\n              <span>Demo</span>\n            </Link>\n\n            {user && (\n              <>\n                <Link\n                  href=\"/play\"\n                  className=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  <Gamepad2 className=\"w-4 h-4\" />\n                  <span>Play</span>\n                </Link>\n                <Link\n                  href=\"/profile\"\n                  className=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  <User className=\"w-4 h-4\" />\n                  <span>Profile</span>\n                </Link>\n              </>\n            )}\n\n            <LoginButton />\n          </div>\n        </div>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEvB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;;;;;;kCAItD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,mMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;kDAAK;;;;;;;;;;;;0CAGR,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;kDAAK;;;;;;;;;;;;4BAGP,sBACC;;kDACE,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAK;;;;;;;;;;;;;;0CAKZ,8OAAC,yIAAA,CAAA,UAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMxB", "debugId": null}}, {"offset": {"line": 383, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/hooks/useChessGame.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useCallback, useEffect } from 'react'\nimport { Chess, Square } from 'chess.js'\n\nexport interface ChessGameState {\n  game: Chess\n  gameId?: string\n  playerColor: 'white' | 'black'\n  isPlayerTurn: boolean\n  gameStatus: 'waiting' | 'active' | 'completed' | 'abandoned'\n  winner?: 'white' | 'black' | 'draw'\n  moveHistory: string[]\n}\n\nexport function useChessGame(initialFen?: string) {\n  const [gameState, setGameState] = useState<ChessGameState>(() => ({\n    game: new Chess(initialFen),\n    playerColor: 'white',\n    isPlayerTurn: true,\n    gameStatus: 'active', // Start as active for demo mode\n    moveHistory: []\n  }))\n\n  const [selectedSquare, setSelectedSquare] = useState<Square | null>(null)\n  const [possibleMoves, setPossibleMoves] = useState<Square[]>([])\n\n  // Get possible moves for a square\n  const getPossibleMoves = useCallback((square: Square): Square[] => {\n    const moves = gameState.game.moves({ square, verbose: true })\n    return moves.map(move => move.to as Square)\n  }, [gameState.game])\n\n  // Make a move\n  const makeMove = useCallback((from: Square, to: Square, promotion?: string) => {\n    // For demo mode, always allow moves (skip turn check)\n    // In multiplayer mode, this would check gameState.isPlayerTurn\n\n    try {\n      // Create a copy of the game to test the move\n      const gameCopy = new Chess(gameState.game.fen())\n      const move = gameCopy.move({\n        from,\n        to,\n        promotion: promotion || 'q' // Default to queen promotion\n      })\n\n      if (move) {\n        // Update state with the new game state\n        setGameState(prev => ({\n          ...prev,\n          game: gameCopy, // Use the updated game copy\n          moveHistory: [...prev.moveHistory, move.san],\n          isPlayerTurn: true // Keep as true for demo mode to allow continuous play\n        }))\n\n        return { success: true, move }\n      } else {\n        return { success: false, error: 'Invalid move' }\n      }\n    } catch (error) {\n      return { success: false, error: 'Invalid move' }\n    }\n  }, [gameState.game])\n\n  // Handle square click\n  const onSquareClick = useCallback((square: Square) => {\n    // If no square is selected, select this square if it has a piece\n    if (!selectedSquare) {\n      const piece = gameState.game.get(square)\n      // For demo mode, allow selecting any piece (both colors)\n      if (piece) {\n        setSelectedSquare(square)\n        setPossibleMoves(getPossibleMoves(square))\n      }\n      return\n    }\n\n    // If the same square is clicked, deselect\n    if (selectedSquare === square) {\n      setSelectedSquare(null)\n      setPossibleMoves([])\n      return\n    }\n\n    // Try to make a move\n    const moveResult = makeMove(selectedSquare, square)\n\n    // Clear selection regardless of move success\n    setSelectedSquare(null)\n    setPossibleMoves([])\n\n    return moveResult\n  }, [selectedSquare, gameState.game, getPossibleMoves, makeMove])\n\n  // Handle piece drop (drag and drop)\n  const onPieceDrop = useCallback((sourceSquare: Square, targetSquare: Square) => {\n    const result = makeMove(sourceSquare, targetSquare)\n    return result?.success || false\n  }, [makeMove])\n\n  // Reset game\n  const resetGame = useCallback(() => {\n    setGameState(prev => ({\n      ...prev,\n      game: new Chess(),\n      moveHistory: [],\n      gameStatus: 'active', // Set to active for demo mode\n      winner: undefined,\n      isPlayerTurn: true\n    }))\n    setSelectedSquare(null)\n    setPossibleMoves([])\n  }, [])\n\n  // Update game from external state (for multiplayer)\n  const updateGameState = useCallback((newState: Partial<ChessGameState>) => {\n    setGameState(prev => ({\n      ...prev,\n      ...newState,\n      game: newState.game ? new Chess(newState.game.fen()) : prev.game\n    }))\n  }, [])\n\n  // Check game status\n  useEffect(() => {\n    const game = gameState.game\n    let status: ChessGameState['gameStatus'] = 'active'\n    let winner: ChessGameState['winner'] = undefined\n\n    if (game.isGameOver()) {\n      status = 'completed'\n      if (game.isCheckmate()) {\n        winner = game.turn() === 'w' ? 'black' : 'white'\n      } else if (game.isDraw()) {\n        winner = 'draw'\n      }\n    }\n\n    if (status !== gameState.gameStatus || winner !== gameState.winner) {\n      setGameState(prev => ({\n        ...prev,\n        gameStatus: status,\n        winner\n      }))\n    }\n  }, [gameState.game, gameState.gameStatus, gameState.winner])\n\n  return {\n    gameState,\n    selectedSquare,\n    possibleMoves,\n    onSquareClick,\n    onPieceDrop,\n    makeMove,\n    resetGame,\n    updateGameState,\n    isCheck: gameState.game.isCheck(),\n    isCheckmate: gameState.game.isCheckmate(),\n    isDraw: gameState.game.isDraw(),\n    isGameOver: gameState.game.isGameOver(),\n    currentFen: gameState.game.fen(),\n    pgn: gameState.game.pgn()\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;AAeO,SAAS,aAAa,UAAmB;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,IAAM,CAAC;YAChE,MAAM,IAAI,mJAAA,CAAA,QAAK,CAAC;YAChB,aAAa;YACb,cAAc;YACd,YAAY;YACZ,aAAa,EAAE;QACjB,CAAC;IAED,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE/D,kCAAkC;IAClC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,MAAM,QAAQ,UAAU,IAAI,CAAC,KAAK,CAAC;YAAE;YAAQ,SAAS;QAAK;QAC3D,OAAO,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,EAAE;IAClC,GAAG;QAAC,UAAU,IAAI;KAAC;IAEnB,cAAc;IACd,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,MAAc,IAAY;QACtD,sDAAsD;QACtD,+DAA+D;QAE/D,IAAI;YACF,6CAA6C;YAC7C,MAAM,WAAW,IAAI,mJAAA,CAAA,QAAK,CAAC,UAAU,IAAI,CAAC,GAAG;YAC7C,MAAM,OAAO,SAAS,IAAI,CAAC;gBACzB;gBACA;gBACA,WAAW,aAAa,IAAI,6BAA6B;YAC3D;YAEA,IAAI,MAAM;gBACR,uCAAuC;gBACvC,aAAa,CAAA,OAAQ,CAAC;wBACpB,GAAG,IAAI;wBACP,MAAM;wBACN,aAAa;+BAAI,KAAK,WAAW;4BAAE,KAAK,GAAG;yBAAC;wBAC5C,cAAc,KAAK,sDAAsD;oBAC3E,CAAC;gBAED,OAAO;oBAAE,SAAS;oBAAM;gBAAK;YAC/B,OAAO;gBACL,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAe;YACjD;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAe;QACjD;IACF,GAAG;QAAC,UAAU,IAAI;KAAC;IAEnB,sBAAsB;IACtB,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,iEAAiE;QACjE,IAAI,CAAC,gBAAgB;YACnB,MAAM,QAAQ,UAAU,IAAI,CAAC,GAAG,CAAC;YACjC,yDAAyD;YACzD,IAAI,OAAO;gBACT,kBAAkB;gBAClB,iBAAiB,iBAAiB;YACpC;YACA;QACF;QAEA,0CAA0C;QAC1C,IAAI,mBAAmB,QAAQ;YAC7B,kBAAkB;YAClB,iBAAiB,EAAE;YACnB;QACF;QAEA,qBAAqB;QACrB,MAAM,aAAa,SAAS,gBAAgB;QAE5C,6CAA6C;QAC7C,kBAAkB;QAClB,iBAAiB,EAAE;QAEnB,OAAO;IACT,GAAG;QAAC;QAAgB,UAAU,IAAI;QAAE;QAAkB;KAAS;IAE/D,oCAAoC;IACpC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,cAAsB;QACrD,MAAM,SAAS,SAAS,cAAc;QACtC,OAAO,QAAQ,WAAW;IAC5B,GAAG;QAAC;KAAS;IAEb,aAAa;IACb,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,aAAa,CAAA,OAAQ,CAAC;gBACpB,GAAG,IAAI;gBACP,MAAM,IAAI,mJAAA,CAAA,QAAK;gBACf,aAAa,EAAE;gBACf,YAAY;gBACZ,QAAQ;gBACR,cAAc;YAChB,CAAC;QACD,kBAAkB;QAClB,iBAAiB,EAAE;IACrB,GAAG,EAAE;IAEL,oDAAoD;IACpD,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,aAAa,CAAA,OAAQ,CAAC;gBACpB,GAAG,IAAI;gBACP,GAAG,QAAQ;gBACX,MAAM,SAAS,IAAI,GAAG,IAAI,mJAAA,CAAA,QAAK,CAAC,SAAS,IAAI,CAAC,GAAG,MAAM,KAAK,IAAI;YAClE,CAAC;IACH,GAAG,EAAE;IAEL,oBAAoB;IACpB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,OAAO,UAAU,IAAI;QAC3B,IAAI,SAAuC;QAC3C,IAAI,SAAmC;QAEvC,IAAI,KAAK,UAAU,IAAI;YACrB,SAAS;YACT,IAAI,KAAK,WAAW,IAAI;gBACtB,SAAS,KAAK,IAAI,OAAO,MAAM,UAAU;YAC3C,OAAO,IAAI,KAAK,MAAM,IAAI;gBACxB,SAAS;YACX;QACF;QAEA,IAAI,WAAW,UAAU,UAAU,IAAI,WAAW,UAAU,MAAM,EAAE;YAClE,aAAa,CAAA,OAAQ,CAAC;oBACpB,GAAG,IAAI;oBACP,YAAY;oBACZ;gBACF,CAAC;QACH;IACF,GAAG;QAAC,UAAU,IAAI;QAAE,UAAU,UAAU;QAAE,UAAU,MAAM;KAAC;IAE3D,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,SAAS,UAAU,IAAI,CAAC,OAAO;QAC/B,aAAa,UAAU,IAAI,CAAC,WAAW;QACvC,QAAQ,UAAU,IAAI,CAAC,MAAM;QAC7B,YAAY,UAAU,IAAI,CAAC,UAAU;QACrC,YAAY,UAAU,IAAI,CAAC,GAAG;QAC9B,KAAK,UAAU,IAAI,CAAC,GAAG;IACzB;AACF", "debugId": null}}, {"offset": {"line": 559, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/components/chess/ChessBoard.tsx"], "sourcesContent": ["'use client'\n\nimport { Chessboard } from 'react-chessboard'\nimport { Square } from 'chess.js'\nimport { useChessGame } from '@/hooks/useChessGame'\nimport { Crown, RotateCcw, Flag } from 'lucide-react'\nimport { useMemo } from 'react'\n\ninterface ChessBoardProps {\n  gameId?: string\n  playerColor?: 'white' | 'black'\n  isSpectator?: boolean\n  onMove?: (move: any) => void\n  initialFen?: string\n}\n\nexport default function ChessBoard({\n  playerColor = 'white',\n  isSpectator = false,\n  onMove,\n  initialFen\n}: ChessBoardProps) {\n  const {\n    gameState,\n    selectedSquare,\n    possibleMoves,\n    onSquareClick,\n    onPieceDrop,\n    resetGame,\n    isCheck,\n    isCheckmate,\n    isDraw,\n    isGameOver,\n    currentFen\n  } = useChessGame(initialFen)\n\n  // Custom square styles for highlighting\n  const customSquareStyles = useMemo(() => {\n    const styles: { [square: string]: React.CSSProperties } = {}\n\n    // Highlight selected square\n    if (selectedSquare) {\n      styles[selectedSquare] = {\n        backgroundColor: 'rgba(255, 255, 0, 0.4)'\n      }\n    }\n\n    // Highlight possible moves\n    possibleMoves.forEach(square => {\n      styles[square] = {\n        background: 'radial-gradient(circle, rgba(0,0,0,.1) 25%, transparent 25%)',\n        borderRadius: '50%'\n      }\n    })\n\n    // Highlight check\n    if (isCheck) {\n      const kingSquare = gameState.game.board().flat().find(\n        piece => piece && piece.type === 'k' && piece.color === gameState.game.turn()\n      )\n      if (kingSquare) {\n        // Find king position - this is a simplified approach\n        for (let rank = 0; rank < 8; rank++) {\n          for (let file = 0; file < 8; file++) {\n            const square = String.fromCharCode(97 + file) + (8 - rank) as Square\n            const piece = gameState.game.get(square)\n            if (piece && piece.type === 'k' && piece.color === gameState.game.turn()) {\n              styles[square] = {\n                backgroundColor: 'rgba(255, 0, 0, 0.4)'\n              }\n            }\n          }\n        }\n      }\n    }\n\n    return styles\n  }, [selectedSquare, possibleMoves, isCheck, gameState.game])\n\n  const handlePieceDrop = ({ sourceSquare, targetSquare }: any) => {\n    if (isSpectator) return false\n\n    const result = onPieceDrop(sourceSquare as Square, targetSquare as Square)\n\n    if (result && onMove) {\n      // Get the last move from the game\n      const history = gameState.game.history({ verbose: true })\n      const lastMove = history[history.length - 1]\n      onMove(lastMove)\n    }\n\n    return result\n  }\n\n  const handleSquareClick = ({ square }: any) => {\n    if (!isSpectator) {\n      onSquareClick(square as Square)\n    }\n  }\n\n  const getGameStatusMessage = () => {\n    if (isCheckmate) {\n      const winner = gameState.game.turn() === 'w' ? 'Black' : 'White'\n      return `Checkmate! ${winner} wins!`\n    }\n    if (isDraw) {\n      return 'Game ended in a draw'\n    }\n    if (isCheck) {\n      return 'Check!'\n    }\n    if (gameState.gameStatus === 'waiting') {\n      return 'Waiting for opponent...'\n    }\n    return `${gameState.game.turn() === 'w' ? 'White' : 'Black'} to move`\n  }\n\n  return (\n    <div className=\"flex flex-col items-center space-y-4\">\n      {/* Game Status */}\n      <div className=\"bg-white rounded-lg shadow-md p-4 w-full max-w-md text-center\">\n        <div className=\"flex items-center justify-center space-x-2 mb-2\">\n          <Crown className=\"h-5 w-5 text-yellow-500\" />\n          <span className=\"font-semibold text-gray-800\">\n            {getGameStatusMessage()}\n          </span>\n        </div>\n        \n        {gameState.gameStatus === 'active' && (\n          <div className=\"text-sm text-gray-600\">\n            Turn: {gameState.game.turn() === 'w' ? 'White' : 'Black'}\n          </div>\n        )}\n      </div>\n\n      {/* Chess Board */}\n      <div className=\"relative\">\n        <Chessboard\n          options={{\n            position: currentFen,\n            onPieceDrop: handlePieceDrop,\n            onSquareClick: !isSpectator ? handleSquareClick : undefined,\n            boardOrientation: playerColor,\n            squareStyles: customSquareStyles,\n            allowDrawingArrows: true,\n            boardStyle: {\n              borderRadius: '8px',\n              boxShadow: '0 10px 25px rgba(0, 0, 0, 0.2)',\n              width: '400px',\n              height: '400px'\n            }\n          }}\n        />\n        \n        {/* Game Over Overlay */}\n        {isGameOver && (\n          <div className=\"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-lg\">\n            <div className=\"bg-white p-6 rounded-lg text-center\">\n              <h3 className=\"text-xl font-bold mb-2\">Game Over</h3>\n              <p className=\"text-gray-600 mb-4\">{getGameStatusMessage()}</p>\n              {!isSpectator && (\n                <button\n                  onClick={resetGame}\n                  className=\"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\"\n                >\n                  <RotateCcw className=\"h-4 w-4 mr-2\" />\n                  New Game\n                </button>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Game Controls */}\n      {!isSpectator && gameState.gameStatus === 'active' && (\n        <div className=\"flex space-x-2\">\n          <button\n            onClick={resetGame}\n            className=\"inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50\"\n          >\n            <RotateCcw className=\"h-4 w-4 mr-1\" />\n            Reset\n          </button>\n          \n          <button\n            className=\"inline-flex items-center px-3 py-2 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50\"\n          >\n            <Flag className=\"h-4 w-4 mr-1\" />\n            Resign\n          </button>\n        </div>\n      )}\n\n      {/* Move History */}\n      <div className=\"bg-white rounded-lg shadow-md p-4 w-full max-w-md\">\n        <h3 className=\"font-semibold text-gray-800 mb-2\">Move History</h3>\n        <div className=\"max-h-32 overflow-y-auto text-sm\">\n          {gameState.moveHistory.length > 0 ? (\n            <div className=\"grid grid-cols-2 gap-2\">\n              {gameState.moveHistory.map((move, index) => (\n                <div key={index} className=\"text-gray-600\">\n                  {Math.floor(index / 2) + 1}.{index % 2 === 0 ? '' : '..'} {move}\n                </div>\n              ))}\n            </div>\n          ) : (\n            <p className=\"text-gray-500\">No moves yet</p>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AAAA;AAAA;AACA;AANA;;;;;;AAgBe,SAAS,WAAW,EACjC,cAAc,OAAO,EACrB,cAAc,KAAK,EACnB,MAAM,EACN,UAAU,EACM;IAChB,MAAM,EACJ,SAAS,EACT,cAAc,EACd,aAAa,EACb,aAAa,EACb,WAAW,EACX,SAAS,EACT,OAAO,EACP,WAAW,EACX,MAAM,EACN,UAAU,EACV,UAAU,EACX,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD,EAAE;IAEjB,wCAAwC;IACxC,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACjC,MAAM,SAAoD,CAAC;QAE3D,4BAA4B;QAC5B,IAAI,gBAAgB;YAClB,MAAM,CAAC,eAAe,GAAG;gBACvB,iBAAiB;YACnB;QACF;QAEA,2BAA2B;QAC3B,cAAc,OAAO,CAAC,CAAA;YACpB,MAAM,CAAC,OAAO,GAAG;gBACf,YAAY;gBACZ,cAAc;YAChB;QACF;QAEA,kBAAkB;QAClB,IAAI,SAAS;YACX,MAAM,aAAa,UAAU,IAAI,CAAC,KAAK,GAAG,IAAI,GAAG,IAAI,CACnD,CAAA,QAAS,SAAS,MAAM,IAAI,KAAK,OAAO,MAAM,KAAK,KAAK,UAAU,IAAI,CAAC,IAAI;YAE7E,IAAI,YAAY;gBACd,qDAAqD;gBACrD,IAAK,IAAI,OAAO,GAAG,OAAO,GAAG,OAAQ;oBACnC,IAAK,IAAI,OAAO,GAAG,OAAO,GAAG,OAAQ;wBACnC,MAAM,SAAS,OAAO,YAAY,CAAC,KAAK,QAAQ,CAAC,IAAI,IAAI;wBACzD,MAAM,QAAQ,UAAU,IAAI,CAAC,GAAG,CAAC;wBACjC,IAAI,SAAS,MAAM,IAAI,KAAK,OAAO,MAAM,KAAK,KAAK,UAAU,IAAI,CAAC,IAAI,IAAI;4BACxE,MAAM,CAAC,OAAO,GAAG;gCACf,iBAAiB;4BACnB;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAO;IACT,GAAG;QAAC;QAAgB;QAAe;QAAS,UAAU,IAAI;KAAC;IAE3D,MAAM,kBAAkB,CAAC,EAAE,YAAY,EAAE,YAAY,EAAO;QAC1D,IAAI,aAAa,OAAO;QAExB,MAAM,SAAS,YAAY,cAAwB;QAEnD,IAAI,UAAU,QAAQ;YACpB,kCAAkC;YAClC,MAAM,UAAU,UAAU,IAAI,CAAC,OAAO,CAAC;gBAAE,SAAS;YAAK;YACvD,MAAM,WAAW,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE;YAC5C,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAM,oBAAoB,CAAC,EAAE,MAAM,EAAO;QACxC,IAAI,CAAC,aAAa;YAChB,cAAc;QAChB;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI,aAAa;YACf,MAAM,SAAS,UAAU,IAAI,CAAC,IAAI,OAAO,MAAM,UAAU;YACzD,OAAO,CAAC,WAAW,EAAE,OAAO,MAAM,CAAC;QACrC;QACA,IAAI,QAAQ;YACV,OAAO;QACT;QACA,IAAI,SAAS;YACX,OAAO;QACT;QACA,IAAI,UAAU,UAAU,KAAK,WAAW;YACtC,OAAO;QACT;QACA,OAAO,GAAG,UAAU,IAAI,CAAC,IAAI,OAAO,MAAM,UAAU,QAAQ,QAAQ,CAAC;IACvE;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAK,WAAU;0CACb;;;;;;;;;;;;oBAIJ,UAAU,UAAU,KAAK,0BACxB,8OAAC;wBAAI,WAAU;;4BAAwB;4BAC9B,UAAU,IAAI,CAAC,IAAI,OAAO,MAAM,UAAU;;;;;;;;;;;;;0BAMvD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,2JAAA,CAAA,aAAU;wBACT,SAAS;4BACP,UAAU;4BACV,aAAa;4BACb,eAAe,CAAC,cAAc,oBAAoB;4BAClD,kBAAkB;4BAClB,cAAc;4BACd,oBAAoB;4BACpB,YAAY;gCACV,cAAc;gCACd,WAAW;gCACX,OAAO;gCACP,QAAQ;4BACV;wBACF;;;;;;oBAID,4BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAyB;;;;;;8CACvC,8OAAC;oCAAE,WAAU;8CAAsB;;;;;;gCAClC,CAAC,6BACA,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;YAUjD,CAAC,eAAe,UAAU,UAAU,KAAK,0BACxC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAIxC,8OAAC;wBACC,WAAU;;0CAEV,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAOvC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAI,WAAU;kCACZ,UAAU,WAAW,CAAC,MAAM,GAAG,kBAC9B,8OAAC;4BAAI,WAAU;sCACZ,UAAU,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,sBAChC,8OAAC;oCAAgB,WAAU;;wCACxB,KAAK,KAAK,CAAC,QAAQ,KAAK;wCAAE;wCAAE,QAAQ,MAAM,IAAI,KAAK;wCAAK;wCAAE;;mCADnD;;;;;;;;;iDAMd,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;;;;;;AAMzC", "debugId": null}}, {"offset": {"line": 887, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/app/game/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useParams } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { supabase } from '@/lib/supabase'\nimport Navigation from '@/components/layout/Navigation'\nimport ChessBoard from '@/components/chess/ChessBoard'\nimport { ArrowLeft, Users, MessageCircle } from 'lucide-react'\nimport Link from 'next/link'\n\ninterface GameData {\n  id: string\n  white_player_id: string\n  black_player_id?: string\n  game_state: string\n  moves: string[]\n  status: 'waiting' | 'active' | 'completed' | 'abandoned'\n  winner?: 'white' | 'black' | 'draw'\n  white_player: {\n    display_name: string\n    avatar_url?: string\n  }\n  black_player?: {\n    display_name: string\n    avatar_url?: string\n  }\n}\n\nexport default function GamePage() {\n  const params = useParams()\n  const gameId = params.id as string\n  const { user } = useAuth()\n  \n  const [game, setGame] = useState<GameData | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n\n  useEffect(() => {\n    if (gameId && user) {\n      fetchGame()\n      subscribeToGameUpdates()\n    }\n  }, [gameId, user])\n\n  const fetchGame = async () => {\n    try {\n      const { data, error } = await supabase\n        .from('games')\n        .select(`\n          *,\n          white_player:users!games_white_player_id_fkey(display_name, avatar_url),\n          black_player:users!games_black_player_id_fkey(display_name, avatar_url)\n        `)\n        .eq('id', gameId)\n        .single()\n\n      if (error) throw error\n      \n      // Check if user is authorized to view this game\n      if (data.white_player_id !== user?.id && data.black_player_id !== user?.id) {\n        setError('You are not authorized to view this game')\n        return\n      }\n\n      setGame(data)\n    } catch (error) {\n      console.error('Error fetching game:', error)\n      setError('Game not found')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const subscribeToGameUpdates = () => {\n    const subscription = supabase\n      .channel(`game-${gameId}`)\n      .on(\n        'postgres_changes',\n        {\n          event: '*',\n          schema: 'public',\n          table: 'games',\n          filter: `id=eq.${gameId}`\n        },\n        (payload) => {\n          console.log('Game update:', payload)\n          fetchGame() // Refetch game data when it changes\n        }\n      )\n      .subscribe()\n\n    return () => {\n      subscription.unsubscribe()\n    }\n  }\n\n  const handleMove = async (move: any) => {\n    if (!game || !user) return\n\n    try {\n      // Update game state in database\n      const newMoves = [...game.moves, move.san]\n      const { error } = await supabase\n        .from('games')\n        .update({\n          game_state: move.after, // FEN after the move\n          moves: newMoves\n        })\n        .eq('id', gameId)\n\n      if (error) throw error\n\n      // Also record the move in game_moves table\n      await supabase\n        .from('game_moves')\n        .insert({\n          game_id: gameId,\n          player_id: user.id,\n          move: move.san,\n          fen_after: move.after,\n          move_number: newMoves.length\n        })\n\n    } catch (error) {\n      console.error('Error updating game:', error)\n    }\n  }\n\n  const getPlayerColor = (): 'white' | 'black' => {\n    if (!game || !user) return 'white'\n    return game.white_player_id === user.id ? 'white' : 'black'\n  }\n\n  const isPlayerTurn = (): boolean => {\n    if (!game || !user) return false\n    \n    // Parse FEN to get current turn\n    const fenParts = game.game_state.split(' ')\n    const currentTurn = fenParts[1] // 'w' for white, 'b' for black\n    \n    const playerColor = getPlayerColor()\n    return (currentTurn === 'w' && playerColor === 'white') || \n           (currentTurn === 'b' && playerColor === 'black')\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Navigation />\n        <div className=\"max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n            <p className=\"mt-4 text-gray-600\">Loading game...</p>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  if (error || !game) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Navigation />\n        <div className=\"max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">\n              {error || 'Game not found'}\n            </h1>\n            <Link\n              href=\"/play\"\n              className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\"\n            >\n              <ArrowLeft className=\"h-4 w-4 mr-2\" />\n              Back to Games\n            </Link>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navigation />\n      \n      <div className=\"max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <Link\n                href=\"/play\"\n                className=\"inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-2\"\n              >\n                <ArrowLeft className=\"h-4 w-4 mr-1\" />\n                Back to Games\n              </Link>\n              <h1 className=\"text-3xl font-extrabold text-gray-900\">\n                Chess Game\n              </h1>\n            </div>\n            \n            <div className=\"text-right\">\n              <div className=\"text-sm text-gray-500\">Game Status</div>\n              <div className={`text-lg font-semibold capitalize ${\n                game.status === 'active' ? 'text-green-600' :\n                game.status === 'waiting' ? 'text-yellow-600' :\n                'text-gray-600'\n              }`}>\n                {game.status}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-8\">\n          {/* Players Info */}\n          <div className=\"lg:col-span-1 space-y-4\">\n            {/* White Player */}\n            <div className=\"bg-white rounded-lg shadow-md p-4\">\n              <div className=\"flex items-center space-x-3\">\n                {game.white_player.avatar_url ? (\n                  <img\n                    src={game.white_player.avatar_url}\n                    alt={game.white_player.display_name}\n                    className=\"w-10 h-10 rounded-full\"\n                  />\n                ) : (\n                  <div className=\"w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center\">\n                    <Users className=\"w-5 h-5 text-gray-600\" />\n                  </div>\n                )}\n                <div>\n                  <div className=\"font-medium text-gray-900\">\n                    {game.white_player.display_name}\n                  </div>\n                  <div className=\"text-sm text-gray-500\">White</div>\n                </div>\n              </div>\n            </div>\n\n            {/* Black Player */}\n            <div className=\"bg-white rounded-lg shadow-md p-4\">\n              <div className=\"flex items-center space-x-3\">\n                {game.black_player?.avatar_url ? (\n                  <img\n                    src={game.black_player.avatar_url}\n                    alt={game.black_player.display_name}\n                    className=\"w-10 h-10 rounded-full\"\n                  />\n                ) : (\n                  <div className=\"w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center\">\n                    <Users className=\"w-5 h-5 text-gray-600\" />\n                  </div>\n                )}\n                <div>\n                  <div className=\"font-medium text-gray-900\">\n                    {game.black_player?.display_name || 'Waiting for player...'}\n                  </div>\n                  <div className=\"text-sm text-gray-500\">Black</div>\n                </div>\n              </div>\n            </div>\n\n            {/* Chat placeholder */}\n            <div className=\"bg-white rounded-lg shadow-md p-4\">\n              <div className=\"flex items-center space-x-2 mb-3\">\n                <MessageCircle className=\"h-5 w-5 text-gray-400\" />\n                <span className=\"font-medium text-gray-900\">Chat</span>\n              </div>\n              <div className=\"text-sm text-gray-500 text-center py-4\">\n                Chat feature coming soon!\n              </div>\n            </div>\n          </div>\n\n          {/* Chess Board */}\n          <div className=\"lg:col-span-3 flex justify-center\">\n            <ChessBoard\n              gameId={gameId}\n              playerColor={getPlayerColor()}\n              isSpectator={game.status !== 'active' || !game.black_player_id}\n              onMove={handleMove}\n              initialFen={game.game_state}\n            />\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AATA;;;;;;;;;;AA6Be,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,OAAO,EAAE;IACxB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEvB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,MAAM;YAClB;YACA;QACF;IACF,GAAG;QAAC;QAAQ;KAAK;IAEjB,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,CAAC;;;;QAIT,CAAC,EACA,EAAE,CAAC,MAAM,QACT,MAAM;YAET,IAAI,OAAO,MAAM;YAEjB,gDAAgD;YAChD,IAAI,KAAK,eAAe,KAAK,MAAM,MAAM,KAAK,eAAe,KAAK,MAAM,IAAI;gBAC1E,SAAS;gBACT;YACF;YAEA,QAAQ;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,yBAAyB;QAC7B,MAAM,eAAe,sHAAA,CAAA,WAAQ,CAC1B,OAAO,CAAC,CAAC,KAAK,EAAE,QAAQ,EACxB,EAAE,CACD,oBACA;YACE,OAAO;YACP,QAAQ;YACR,OAAO;YACP,QAAQ,CAAC,MAAM,EAAE,QAAQ;QAC3B,GACA,CAAC;YACC,QAAQ,GAAG,CAAC,gBAAgB;YAC5B,YAAY,oCAAoC;;QAClD,GAED,SAAS;QAEZ,OAAO;YACL,aAAa,WAAW;QAC1B;IACF;IAEA,MAAM,aAAa,OAAO;QACxB,IAAI,CAAC,QAAQ,CAAC,MAAM;QAEpB,IAAI;YACF,gCAAgC;YAChC,MAAM,WAAW;mBAAI,KAAK,KAAK;gBAAE,KAAK,GAAG;aAAC;YAC1C,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,SACL,MAAM,CAAC;gBACN,YAAY,KAAK,KAAK;gBACtB,OAAO;YACT,GACC,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO,MAAM;YAEjB,2CAA2C;YAC3C,MAAM,sHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,cACL,MAAM,CAAC;gBACN,SAAS;gBACT,WAAW,KAAK,EAAE;gBAClB,MAAM,KAAK,GAAG;gBACd,WAAW,KAAK,KAAK;gBACrB,aAAa,SAAS,MAAM;YAC9B;QAEJ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,QAAQ,CAAC,MAAM,OAAO;QAC3B,OAAO,KAAK,eAAe,KAAK,KAAK,EAAE,GAAG,UAAU;IACtD;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,QAAQ,CAAC,MAAM,OAAO;QAE3B,gCAAgC;QAChC,MAAM,WAAW,KAAK,UAAU,CAAC,KAAK,CAAC;QACvC,MAAM,cAAc,QAAQ,CAAC,EAAE,CAAC,+BAA+B;;QAE/D,MAAM,cAAc;QACpB,OAAO,AAAC,gBAAgB,OAAO,gBAAgB,WACvC,gBAAgB,OAAO,gBAAgB;IACjD;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0IAAA,CAAA,UAAU;;;;;8BACX,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;;;;;;;;;;;;IAK5C;IAEA,IAAI,SAAS,CAAC,MAAM;QAClB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0IAAA,CAAA,UAAU;;;;;8BACX,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,SAAS;;;;;;0CAEZ,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;IAOlD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,0IAAA,CAAA,UAAU;;;;;0BAEX,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGxC,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;;;;;;;8CAKxD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,8OAAC;4CAAI,WAAW,CAAC,iCAAiC,EAChD,KAAK,MAAM,KAAK,WAAW,mBAC3B,KAAK,MAAM,KAAK,YAAY,oBAC5B,iBACA;sDACC,KAAK,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAMpB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;gDACZ,KAAK,YAAY,CAAC,UAAU,iBAC3B,8OAAC;oDACC,KAAK,KAAK,YAAY,CAAC,UAAU;oDACjC,KAAK,KAAK,YAAY,CAAC,YAAY;oDACnC,WAAU;;;;;yEAGZ,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;8DAGrB,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEACZ,KAAK,YAAY,CAAC,YAAY;;;;;;sEAEjC,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;kDAM7C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;gDACZ,KAAK,YAAY,EAAE,2BAClB,8OAAC;oDACC,KAAK,KAAK,YAAY,CAAC,UAAU;oDACjC,KAAK,KAAK,YAAY,CAAC,YAAY;oDACnC,WAAU;;;;;yEAGZ,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;8DAGrB,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEACZ,KAAK,YAAY,EAAE,gBAAgB;;;;;;sEAEtC,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;kDAM7C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,wNAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;kEACzB,8OAAC;wDAAK,WAAU;kEAA4B;;;;;;;;;;;;0DAE9C,8OAAC;gDAAI,WAAU;0DAAyC;;;;;;;;;;;;;;;;;;0CAO5D,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,yIAAA,CAAA,UAAU;oCACT,QAAQ;oCACR,aAAa;oCACb,aAAa,KAAK,MAAM,KAAK,YAAY,CAAC,KAAK,eAAe;oCAC9D,QAAQ;oCACR,YAAY,KAAK,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzC", "debugId": null}}]}