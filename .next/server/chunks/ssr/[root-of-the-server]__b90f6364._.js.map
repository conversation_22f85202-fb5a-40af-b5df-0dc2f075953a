{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/components/auth/LoginButton.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { LogIn, LogOut, User } from 'lucide-react'\n\nexport default function LoginButton() {\n  const { user, signInWithGoogle, signOut, loading } = useAuth()\n  const [isLoading, setIsLoading] = useState(false)\n\n  const handleSignIn = async () => {\n    setIsLoading(true)\n    try {\n      await signInWithGoogle()\n    } catch (error) {\n      console.error('Sign in error:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleSignOut = async () => {\n    setIsLoading(true)\n    try {\n      await signOut()\n    } catch (error) {\n      console.error('Sign out error:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"animate-pulse bg-gray-200 rounded-md h-10 w-24\"></div>\n    )\n  }\n\n  if (user) {\n    return null // Account button will be handled by AccountButton component\n  }\n\n  return (\n    <button\n      onClick={handleSignIn}\n      disabled={isLoading}\n      className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n    >\n      {isLoading ? (\n        <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n      ) : (\n        <>\n          <LogIn className=\"w-4 h-4 mr-2\" />\n          Sign in with Google\n        </>\n      )}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,eAAe;QACnB,aAAa;QACb,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;QAClC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB;QACpB,aAAa;QACb,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;QACnC,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;;;;;IAEnB;IAEA,IAAI,MAAM;QACR,OAAO,KAAK,4DAA4D;;IAC1E;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,UAAU;QACV,WAAU;kBAET,0BACC,8OAAC;YAAI,WAAU;;;;;iCAEf;;8BACE,8OAAC,wMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;gBAAiB;;;;;;;;AAM5C", "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/components/auth/AccountButton.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useRouter } from 'next/navigation'\nimport { User, Settings, LogOut, ChevronDown } from 'lucide-react'\n\nexport default function AccountButton() {\n  const { user, signOut, loading } = useAuth()\n  const [isDropdownOpen, setIsDropdownOpen] = useState(false)\n  const [isLoading, setIsLoading] = useState(false)\n  const dropdownRef = useRef<HTMLDivElement>(null)\n  const router = useRouter()\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    function handleClickOutside(event: MouseEvent) {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsDropdownOpen(false)\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside)\n    }\n  }, [])\n\n  const handleSignOut = async () => {\n    setIsLoading(true)\n    try {\n      await signOut()\n      setIsDropdownOpen(false)\n      router.push('/')\n    } catch (error) {\n      console.error('Sign out error:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleProfileClick = () => {\n    setIsDropdownOpen(false)\n    router.push('/profile')\n  }\n\n  const getInitials = (name: string) => {\n    return name\n      .split(' ')\n      .map(word => word.charAt(0))\n      .join('')\n      .toUpperCase()\n      .slice(0, 2)\n  }\n\n  if (loading || !user) {\n    return null\n  }\n\n  return (\n    <div className=\"relative\" ref={dropdownRef}>\n      <button\n        onClick={() => setIsDropdownOpen(!isDropdownOpen)}\n        className=\"flex items-center space-x-2 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors\"\n      >\n        {user.avatar_url ? (\n          <img\n            src={user.avatar_url}\n            alt={user.display_name}\n            className=\"w-8 h-8 rounded-full object-cover\"\n          />\n        ) : (\n          <div className=\"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium\">\n            {getInitials(user.display_name)}\n          </div>\n        )}\n        <span className=\"hidden sm:block\">{user.display_name}</span>\n        <ChevronDown className={`w-4 h-4 transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`} />\n      </button>\n\n      {isDropdownOpen && (\n        <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50\">\n          <div className=\"py-1\">\n            <div className=\"px-4 py-2 text-sm text-gray-500 border-b\">\n              <div className=\"font-medium text-gray-900\">{user.display_name}</div>\n              <div className=\"text-xs\">{user.email}</div>\n            </div>\n            \n            <button\n              onClick={handleProfileClick}\n              className=\"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\"\n            >\n              <User className=\"w-4 h-4 mr-3\" />\n              Profile\n            </button>\n            \n            <button\n              onClick={handleProfileClick}\n              className=\"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\"\n            >\n              <Settings className=\"w-4 h-4 mr-3\" />\n              Account Settings\n            </button>\n            \n            <div className=\"border-t\">\n              <button\n                onClick={handleSignOut}\n                disabled={isLoading}\n                className=\"flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors disabled:opacity-50\"\n              >\n                {isLoading ? (\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-3\"></div>\n                ) : (\n                  <LogOut className=\"w-4 h-4 mr-3\" />\n                )}\n                {isLoading ? 'Signing out...' : 'Sign Out'}\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACzC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,mBAAmB,KAAiB;YAC3C,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E,kBAAkB;YACpB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,aAAa;QACb,IAAI;YACF,MAAM;YACN,kBAAkB;YAClB,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;QACnC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,qBAAqB;QACzB,kBAAkB;QAClB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,IACxB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,IAAI,WAAW,CAAC,MAAM;QACpB,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;QAAW,KAAK;;0BAC7B,8OAAC;gBACC,SAAS,IAAM,kBAAkB,CAAC;gBAClC,WAAU;;oBAET,KAAK,UAAU,iBACd,8OAAC;wBACC,KAAK,KAAK,UAAU;wBACpB,KAAK,KAAK,YAAY;wBACtB,WAAU;;;;;6CAGZ,8OAAC;wBAAI,WAAU;kCACZ,YAAY,KAAK,YAAY;;;;;;kCAGlC,8OAAC;wBAAK,WAAU;kCAAmB,KAAK,YAAY;;;;;;kCACpD,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAW,CAAC,6BAA6B,EAAE,iBAAiB,eAAe,IAAI;;;;;;;;;;;;YAG7F,gCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAA6B,KAAK,YAAY;;;;;;8CAC7D,8OAAC;oCAAI,WAAU;8CAAW,KAAK,KAAK;;;;;;;;;;;;sCAGtC,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAInC,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAIvC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;;oCAET,0BACC,8OAAC;wCAAI,WAAU;;;;;6DAEf,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAEnB,YAAY,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhD", "debugId": null}}, {"offset": {"line": 337, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/components/layout/Navigation.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { useAuth } from '@/contexts/AuthContext'\nimport LoginButton from '@/components/auth/LoginButton'\nimport AccountButton from '@/components/auth/AccountButton'\nimport { Crown, Home, User, Gamepad2 } from 'lucide-react'\n\nexport default function Navigation() {\n  const { user } = useAuth()\n\n  return (\n    <nav className=\"bg-white shadow-lg\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <Crown className=\"h-8 w-8 text-blue-600\" />\n              <span className=\"text-xl font-bold text-gray-900\">ChessHub</span>\n            </Link>\n          </div>\n\n          <div className=\"flex items-center space-x-8\">\n            <Link\n              href=\"/\"\n              className=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium\"\n            >\n              <Home className=\"w-4 h-4\" />\n              <span>Home</span>\n            </Link>\n\n            <Link\n              href=\"/demo\"\n              className=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium\"\n            >\n              <Gamepad2 className=\"w-4 h-4\" />\n              <span>Demo</span>\n            </Link>\n\n            {user && (\n              <>\n                <Link\n                  href=\"/play\"\n                  className=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  <Gamepad2 className=\"w-4 h-4\" />\n                  <span>Play</span>\n                </Link>\n                <Link\n                  href=\"/profile\"\n                  className=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  <User className=\"w-4 h-4\" />\n                  <span>Profile</span>\n                </Link>\n              </>\n            )}\n\n            <LoginButton />\n            <AccountButton />\n          </div>\n        </div>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEvB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;;;;;;kCAItD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,mMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;kDAAK;;;;;;;;;;;;0CAGR,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;kDAAK;;;;;;;;;;;;4BAGP,sBACC;;kDACE,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAK;;;;;;;;;;;;;;0CAKZ,8OAAC,yIAAA,CAAA,UAAW;;;;;0CACZ,8OAAC,2IAAA,CAAA,UAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM1B", "debugId": null}}, {"offset": {"line": 539, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/app/play/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { supabase } from '@/lib/supabase'\nimport Navigation from '@/components/layout/Navigation'\nimport ChessBoard from '@/components/chess/ChessBoard'\nimport { Plus, Users, Clock, Trophy } from 'lucide-react'\nimport Link from 'next/link'\n\ninterface Game {\n  id: string\n  white_player_id: string\n  black_player_id?: string\n  status: 'waiting' | 'active' | 'completed' | 'abandoned'\n  created_at: string\n  white_player?: {\n    display_name: string\n    avatar_url?: string\n  }\n  black_player?: {\n    display_name: string\n    avatar_url?: string\n  }\n}\n\nexport default function PlayPage() {\n  const { user } = useAuth()\n  const [games, setGames] = useState<Game[]>([])\n  const [loading, setLoading] = useState(true)\n  const [creating, setCreating] = useState(false)\n\n  useEffect(() => {\n    if (user) {\n      fetchGames()\n    }\n  }, [user])\n\n  const fetchGames = async () => {\n    try {\n      const { data, error } = await supabase\n        .from('games')\n        .select(`\n          *,\n          white_player:users!games_white_player_id_fkey(display_name, avatar_url),\n          black_player:users!games_black_player_id_fkey(display_name, avatar_url)\n        `)\n        .or(`white_player_id.eq.${user?.id},black_player_id.eq.${user?.id}`)\n        .order('created_at', { ascending: false })\n\n      if (error) throw error\n      setGames(data || [])\n    } catch (error) {\n      console.error('Error fetching games:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const createGame = async () => {\n    if (!user) {\n      console.error('No user found when trying to create game')\n      return\n    }\n\n    console.log('Creating game for user:', user)\n    setCreating(true)\n\n    try {\n      // First, let's check if the games table exists by trying a simple query\n      console.log('Checking if games table exists...')\n      const { data: testData, error: testError } = await supabase\n        .from('games')\n        .select('count', { count: 'exact', head: true })\n\n      if (testError) {\n        console.error('Games table does not exist or is not accessible:', testError)\n        alert('Database setup incomplete. The games table does not exist. Please run the database setup in your Supabase dashboard.')\n        return\n      }\n\n      console.log('Games table exists, proceeding with game creation...')\n\n      const { data, error } = await supabase\n        .from('games')\n        .insert({\n          white_player_id: user.id,\n          status: 'waiting'\n        })\n        .select()\n        .single()\n\n      if (error) {\n        console.error('Supabase error details:', {\n          message: error.message,\n          details: error.details,\n          hint: error.hint,\n          code: error.code\n        })\n        throw error\n      }\n\n      console.log('Game created successfully:', data)\n      // Redirect to the game\n      window.location.href = `/game/${data.id}`\n    } catch (error) {\n      console.error('Error creating game:', error)\n      // Show user-friendly error message\n      alert('Failed to create game. Please check the console for details and ensure the database is properly set up.')\n    } finally {\n      setCreating(false)\n    }\n  }\n\n  const joinGame = async (gameId: string) => {\n    if (!user) return\n\n    try {\n      const { error } = await supabase\n        .from('games')\n        .update({\n          black_player_id: user.id,\n          status: 'active'\n        })\n        .eq('id', gameId)\n\n      if (error) throw error\n      \n      // Redirect to the game\n      window.location.href = `/game/${gameId}`\n    } catch (error) {\n      console.error('Error joining game:', error)\n    }\n  }\n\n  if (!user) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Navigation />\n        <div className=\"max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h1 className=\"text-3xl font-extrabold text-gray-900 mb-4\">\n              Sign in to Play Chess\n            </h1>\n            <p className=\"text-lg text-gray-600\">\n              You need to be signed in to create or join games.\n            </p>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navigation />\n      \n      <div className=\"max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-extrabold text-gray-900 mb-2\">Play Chess</h1>\n          <p className=\"text-lg text-gray-600\">Create a new game or join an existing one</p>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Game Actions */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"bg-white rounded-lg shadow-md p-6\">\n              <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Quick Actions</h2>\n              \n              <button\n                onClick={createGame}\n                disabled={creating}\n                className=\"w-full inline-flex items-center justify-center px-4 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed mb-4\"\n              >\n                {creating ? (\n                  <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"></div>\n                ) : (\n                  <>\n                    <Plus className=\"h-5 w-5 mr-2\" />\n                    Create New Game\n                  </>\n                )}\n              </button>\n\n              <div className=\"border-t pt-4\">\n                <h3 className=\"text-sm font-medium text-gray-900 mb-2\">Game Statistics</h3>\n                <div className=\"space-y-2 text-sm text-gray-600\">\n                  <div className=\"flex items-center\">\n                    <Trophy className=\"h-4 w-4 mr-2\" />\n                    <span>Games Played: {user.games_played || 0}</span>\n                  </div>\n                  <div className=\"flex items-center\">\n                    <Users className=\"h-4 w-4 mr-2\" />\n                    <span>Rating: {user.rating || 1200}</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Games List */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"bg-white rounded-lg shadow-md\">\n              <div className=\"px-6 py-4 border-b border-gray-200\">\n                <h2 className=\"text-xl font-semibold text-gray-900\">Your Games</h2>\n              </div>\n              \n              <div className=\"divide-y divide-gray-200\">\n                {loading ? (\n                  <div className=\"p-6 text-center\">\n                    <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"></div>\n                    <p className=\"mt-2 text-gray-600\">Loading games...</p>\n                  </div>\n                ) : games.length === 0 ? (\n                  <div className=\"p-6 text-center text-gray-500\">\n                    <Users className=\"h-12 w-12 mx-auto mb-4 text-gray-400\" />\n                    <p>No games yet. Create your first game!</p>\n                  </div>\n                ) : (\n                  games.map((game) => (\n                    <div key={game.id} className=\"p-6 hover:bg-gray-50\">\n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center space-x-4\">\n                          <div className=\"flex-shrink-0\">\n                            <div className={`w-3 h-3 rounded-full ${\n                              game.status === 'waiting' ? 'bg-yellow-400' :\n                              game.status === 'active' ? 'bg-green-400' :\n                              'bg-gray-400'\n                            }`}></div>\n                          </div>\n                          \n                          <div>\n                            <div className=\"flex items-center space-x-2\">\n                              <span className=\"font-medium text-gray-900\">\n                                {game.white_player?.display_name}\n                              </span>\n                              <span className=\"text-gray-500\">vs</span>\n                              <span className=\"font-medium text-gray-900\">\n                                {game.black_player?.display_name || 'Waiting...'}\n                              </span>\n                            </div>\n                            <div className=\"flex items-center space-x-4 mt-1 text-sm text-gray-500\">\n                              <span className=\"capitalize\">{game.status}</span>\n                              <span className=\"flex items-center\">\n                                <Clock className=\"h-4 w-4 mr-1\" />\n                                {new Date(game.created_at).toLocaleDateString()}\n                              </span>\n                            </div>\n                          </div>\n                        </div>\n                        \n                        <div className=\"flex space-x-2\">\n                          {game.status === 'waiting' && game.white_player_id !== user.id ? (\n                            <button\n                              onClick={() => joinGame(game.id)}\n                              className=\"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700\"\n                            >\n                              Join Game\n                            </button>\n                          ) : (\n                            <Link\n                              href={`/game/${game.id}`}\n                              className=\"inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50\"\n                            >\n                              {game.status === 'active' ? 'Continue' : 'View'}\n                            </Link>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  ))\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AACA;AARA;;;;;;;;AA0Be,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR;QACF;IACF,GAAG;QAAC;KAAK;IAET,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,CAAC;;;;QAIT,CAAC,EACA,EAAE,CAAC,CAAC,mBAAmB,EAAE,MAAM,GAAG,oBAAoB,EAAE,MAAM,IAAI,EAClE,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO,MAAM;YACjB,SAAS,QAAQ,EAAE;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,MAAM;YACT,QAAQ,KAAK,CAAC;YACd;QACF;QAEA,QAAQ,GAAG,CAAC,2BAA2B;QACvC,YAAY;QAEZ,IAAI;YACF,wEAAwE;YACxE,QAAQ,GAAG,CAAC;YACZ,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACxD,IAAI,CAAC,SACL,MAAM,CAAC,SAAS;gBAAE,OAAO;gBAAS,MAAM;YAAK;YAEhD,IAAI,WAAW;gBACb,QAAQ,KAAK,CAAC,oDAAoD;gBAClE,MAAM;gBACN;YACF;YAEA,QAAQ,GAAG,CAAC;YAEZ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC;gBACN,iBAAiB,KAAK,EAAE;gBACxB,QAAQ;YACV,GACC,MAAM,GACN,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,2BAA2B;oBACvC,SAAS,MAAM,OAAO;oBACtB,SAAS,MAAM,OAAO;oBACtB,MAAM,MAAM,IAAI;oBAChB,MAAM,MAAM,IAAI;gBAClB;gBACA,MAAM;YACR;YAEA,QAAQ,GAAG,CAAC,8BAA8B;YAC1C,uBAAuB;YACvB,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,mCAAmC;YACnC,MAAM;QACR,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,SACL,MAAM,CAAC;gBACN,iBAAiB,KAAK,EAAE;gBACxB,QAAQ;YACV,GACC,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO,MAAM;YAEjB,uBAAuB;YACvB,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,MAAM,EAAE,QAAQ;QAC1C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;QACvC;IACF;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0IAAA,CAAA,UAAU;;;;;8BACX,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA6C;;;;;;0CAG3D,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;IAO/C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,0IAAA,CAAA,UAAU;;;;;0BAEX,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA6C;;;;;;0CAC3D,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAGvC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAEzD,8OAAC;4CACC,SAAS;4CACT,UAAU;4CACV,WAAU;sDAET,yBACC,8OAAC;gDAAI,WAAU;;;;;qEAEf;;kEACE,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;sDAMvC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAyC;;;;;;8DACvD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,sMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,8OAAC;;wEAAK;wEAAe,KAAK,YAAY,IAAI;;;;;;;;;;;;;sEAE5C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,8OAAC;;wEAAK;wEAAS,KAAK,MAAM,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQxC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAG,WAAU;0DAAsC;;;;;;;;;;;sDAGtD,8OAAC;4CAAI,WAAU;sDACZ,wBACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAE,WAAU;kEAAqB;;;;;;;;;;;uDAElC,MAAM,MAAM,KAAK,kBACnB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;kEAAE;;;;;;;;;;;uDAGL,MAAM,GAAG,CAAC,CAAC,qBACT,8OAAC;oDAAkB,WAAU;8DAC3B,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAI,WAAW,CAAC,qBAAqB,EACpC,KAAK,MAAM,KAAK,YAAY,kBAC5B,KAAK,MAAM,KAAK,WAAW,iBAC3B,eACA;;;;;;;;;;;kFAGJ,8OAAC;;0FACC,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAK,WAAU;kGACb,KAAK,YAAY,EAAE;;;;;;kGAEtB,8OAAC;wFAAK,WAAU;kGAAgB;;;;;;kGAChC,8OAAC;wFAAK,WAAU;kGACb,KAAK,YAAY,EAAE,gBAAgB;;;;;;;;;;;;0FAGxC,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAK,WAAU;kGAAc,KAAK,MAAM;;;;;;kGACzC,8OAAC;wFAAK,WAAU;;0GACd,8OAAC,oMAAA,CAAA,QAAK;gGAAC,WAAU;;;;;;4FAChB,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;0EAMrD,8OAAC;gEAAI,WAAU;0EACZ,KAAK,MAAM,KAAK,aAAa,KAAK,eAAe,KAAK,KAAK,EAAE,iBAC5D,8OAAC;oEACC,SAAS,IAAM,SAAS,KAAK,EAAE;oEAC/B,WAAU;8EACX;;;;;yFAID,8OAAC,4JAAA,CAAA,UAAI;oEACH,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;oEACxB,WAAU;8EAET,KAAK,MAAM,KAAK,WAAW,aAAa;;;;;;;;;;;;;;;;;mDA5CzC,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2DrC", "debugId": null}}]}