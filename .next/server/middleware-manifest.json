{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_e76d35f6._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_4c46a49c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "JZiPvQ5XzRHs0H9v2y0AQPgZIzl0VWNXe/d02EC1/6o=", "__NEXT_PREVIEW_MODE_ID": "dcab16851a55ddb4af4c34a3b84be18e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "fbe187feb0d897c2b9dc14999a3e1943c8414716c2e142cbb1e546d5d5deed1c", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "8500e00d335490173aae463c4c8af1989a6ab92c1cb25762df5a2f5c9f4cf685"}}}, "sortedMiddleware": ["/"], "functions": {}}